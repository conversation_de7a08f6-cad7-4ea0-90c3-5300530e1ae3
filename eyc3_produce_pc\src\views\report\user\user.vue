<!--
 * @author: 风源
 * @name: 类名
 * @desc: 介绍
 * @LastEditTime: 2023-01-03 23:47:38
 * @FilePath: /eyc3_canyin_pc/src/views/report/user/user.vue
-->
<template>
    <yp_list
        ref="table"
        :url="url"
        row-key="id"
        :columns="columns"
        stripe
        :add="add"
        :derive="derive"
        :formitems="formitems"
    >
    </yp_list>
</template>

<script>
export default {
    name: 'ReportUserUser',
    data() {
        return {
            url: 'report/get_user_sum',
            columns: [
                {
                    label: '姓名',
                    prop: 'name'
                },
                {
                    label: '部门名称',
                    prop: 'department_name'
                },

                {
                    label: '消费次数',
                    prop: 'count'
                },
                {
                    label: '消费金额',
                    prop: 'money'
                },
                {
                    label: '餐补金额',
                    prop: 'subsidy_money'
                },
                {
                    label: '总报餐数',
                    prop: 'apply_count'
                },
                {
                    label: '总点餐数',
                    prop: 'order_count'
                },
                {
                    label: '虚拟账户',
                    children: [
                        {
                            label: '虚拟金额',
                            prop: 'virtual_money'
                        },
                        {
                            label: '虚拟余额',
                            prop: 'balance'
                        },
                        {
                            label: '虚拟充值金额',
                            prop: 'recharge_money'
                        }
                    ]
                },
                {
                    label: '真实账户',
                    children: [
                        {
                            label: '真实金额',
                            prop: 'real_money'
                        },
                        {
                            label: '真实余额',
                            prop: 'real_blance'
                        },
                        {
                            label: '真实充值金额',
                            prop: 'real_recharge_money'
                        }
                    ]
                }
            ],
            derive: {
                filename:"人员账单记录"
            },
            formitems: [
                {
                    label: '日期范围',
                    name: 'date_interval',
                    value: [
                        this.$TOOL.dateFormat(new Date(), 'yyyy-MM-01'),
                        this.$TOOL.dateFormat(new Date(), 'yyyy-MM-dd')
                    ],
                    component: 'date',
                    options: {
                        type: 'daterange',
                        rangeseparator: '至',
                        startplaceholder: '开始日期',
                        endplaceholder: '结束日期',
                        valueFormat: 'YYYY-MM-DD'
                    },
                    rules: [
                        {
                            required: true,
                            message: 'Please input Data',
                            trigger: 'change'
                        }
                    ]
                },
                {
					label: "选择人员",
					name: "userlst",
					value: "",
					component: "selectUser"
				},
                {
					label: "餐厅",
					name: "dininghall_id",
					value: "",
					component: "select",
					options: {
						remote: {
							api: `dininghall/get_all`,
							data: { name: "b" },
							label: "title",
							value: "id",
						},
						items: [
							{
								label: "全部",
								value: "",
							},
						],
					},
				},
				{
					label: "餐时",
					name: "repast_id",
					value: "",
					component: "select",
					options: {
						remote: {
							api: `repast/get_all`,
							data: { dininghall_id: "$dininghall_id" },
							label: "title",
							value: "id",
						},
						items: [
							{
								label: "全部",
								value: "",
							},
						],
					},
				},
				{
					label: "窗口",
					name: "window_id",
					value: "",
					component: "select",
					options: {
						remote: {
							api: `window/get_all`,
							data: { dininghall_id: "$dininghall_id" },
							label: "title",
							value: "id",
						},
						items: [
							{
								label: "全部",
								value: "",
							},
						],
					},
				},
                {
                    label: '离职人员',
                    name: 'isleave',
                    value: false,
                    component: 'switch'
                }
            ]
        }
    }
}
</script>

<style></style>
