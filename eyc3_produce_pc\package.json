{"name": "scui", "version": "1.6.6", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@element-plus/icons-vue": "2.0.6", "@handsontable/react": "^14.4.0", "@tinymce/tinymce-vue": "5.0.0", "@vue-office/excel": "^1.7.14", "@vue-office/pdf": "^1.5.3", "axios": "0.27.2", "codemirror": "5.65.5", "core-js": "3.24.1", "cropperjs": "1.5.12", "crypto-js": "4.1.1", "dayjs": "^1.11.11", "dingtalk-jsapi": "^3.0.12", "echarts": "5.3.3", "echarts-wordcloud": "^2.1.0", "element-plus": "2.2.12", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "js-base64": "^3.7.5", "jspdf": "^2.5.1", "jspdf-autotable": "^3.5.31", "jspreadsheet": "^11.3.0", "jsuites": "^5.4.3", "jszip": "^3.10.1", "lay-excel": "^1.7.6", "luckyexcel": "^1.0.1", "luckysheet": "^2.1.13", "mxdraw": "^0.1.300", "nprogress": "0.2.0", "pdfvuer": "^1.10.0", "pinyin-match": "^1.2.2", "print-js": "^1.6.0", "require.js": "^1.0.0", "sortablejs": "1.15.0", "tinymce": "6.1.2", "uuid": "^11.0.5", "vconsole": "^3.15.1", "vue": "3.2.37", "vue-demi": "^0.14.10", "vue-i18n": "9.2.2", "vue-pdf": "4.0.7", "vue-router": "4.1.3", "vuedraggable": "4.0.3", "vueshowpdf": "^1.1.2", "vuex": "4.0.2", "xgplayer": "2.31.7", "xgplayer-hls": "2.5.2", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0", "xlsx-style": "^0.8.13", "yarn": "^1.22.19"}, "devDependencies": {"@vitejs/plugin-vue": "^3.0.3", "sass": "1.37.5", "vite": "^4.4.9", "vite-plugin-compression": "^0.5.1"}, "eslintConfig": {"root": true, "env": {"node": true}, "globals": {"APP_CONFIG": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {"indent": 0, "no-tabs": 0, "no-mixed-spaces-and-tabs": 0, "vue/no-unused-components": 0, "vue/multi-word-component-names": 0}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}