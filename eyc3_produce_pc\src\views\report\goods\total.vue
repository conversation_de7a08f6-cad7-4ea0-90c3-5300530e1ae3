<!--
 * @author: 风源
 * @name: 类名
 * @desc: 介绍
 * @LastEditTime: 2022-12-15 16:52:39
 * @FilePath: \eyc3_canyin_pc\src\views\report\goods\total.vue
-->
<template>
	<yp_list
		ref="table"
		:url="url"
		row-key="id"
		:columns="columns"
		stripe
		:add="add"
		:derive="derive"
		:formitems="formitems"
		:paging="false"
	>
	</yp_list>
</template>

<script>
export default {
	name: "ReportGoodsTotal",
	data() {
		return {
			url: "report/get_supermarket_sell_sum",
			columns: [
				{
					label: "商品",
					prop: "title",
				},
				{
					label: "类型",
					prop: "category_title",
				},

				{
					label: "数量",
					prop: "count",
					sortable:true
				},
				{
					label: "价格",
					prop: "price",
					sortable:true
				},
				{
					label: "总价",
					prop: "money",
				},				
			],
			derive: {
				filename:"销售汇总"
			},
			formitems: [
				{
					label: "日期范围",
					name: "date_interval",
					value: [
						this.$TOOL.dateFormat(new Date(), "yyyy-MM-01"),
						this.$TOOL.dateFormat(new Date(), "yyyy-MM-dd"),
					],
					component: "date",
					options: {
						type: "daterange",
						rangeseparator: "至",
						startplaceholder: "开始日期",
						endplaceholder: "结束日期",
						valueFormat: "YYYY-MM-DD",
					},
					rules: [
						{
							required: true,
							message: "Please input Data",
							trigger: "change",
						},
					],
				},
				{
					label: "餐厅",
					name: "dininghall_id",
					value: "",
					component: "select",
					options: {
						remote: {
							api: `dininghall/get_all`,
							data: { name: "b" },
							label: "title",
							value: "id",
						},
						items: [
							{
								label: "全部",
								value: "",
							},
						],
					},
				}
				
			],
		};
	},
};
</script>

<style></style>
