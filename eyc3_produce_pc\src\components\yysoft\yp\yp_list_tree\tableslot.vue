<!--
 * @author: 风源
 * @name: 类名
 * @desc: 介绍
 * @LastEditTime: 2023-01-06 18:45:14
 * @FilePath: \eyc3_car_pc\src\components\yysoft\yp\yp_list\tableslot.vue
-->
<template>
    <slot
        :name="item.prop"
        v-bind="scope"
        v-if="item.component == 'fillvalue'"
    >
        {{ item.options[scope.row[item.prop]] }}
    </slot>
    <slot
        :name="item.prop"
        v-bind="scope"
        v-if="item.component == 'image'"
    >
        <template v-if="scope.row[item.prop]">
            <el-image
                style="width: 80px; height: 80px; border-radius: 5px"
                :src="scope.row[item.prop] + '?x-oss-process=image/resize,w_300/format,webp'"
                :zoom-rate="1.2"
                fit="cover"
            />
        </template>
        <template v-else>
            <div class="img-text">
                <template v-if="item.options.textprop">
                    {{ scope.row[item.options.textprop].substring(0, 1) }}
                </template>
            </div>
        </template>
    </slot>
    <slot
        :name="item.prop"
        v-bind="scope"
        v-if="item.component == 'images'"
    >
        <template v-if="scope.row[item.prop]">
            <template v-if="JSON.parse(scope.row[item.prop]).length >= 1">
                <template
                    v-for="(item, index) in JSON.parse(scope.row[item.prop])"
                    :key="index"
                >
                    <el-image
                        style="width: 80px; height: 80px; border-radius: 5px"
                        :src="item + '?x-oss-process=image/resize,w_300/format,webp'"
                        :zoom-rate="1.2"
                        fit="cover"
                    />
                </template>
            </template>
            <template v-else>
                <div class="img-text">
                    <template v-if="item.options">
                        <template v-if="item.options.textprop">
                            {{ scope.row[item.options.textprop].substring(0, 1) }}
                        </template>
                    </template>
                    <template v-else>暂无</template>
                </div>
            </template>
        </template>
        <template v-else>
            <div class="img-text">
                <template v-if="item.options">
                    <template v-if="item.options.textprop">
                        {{ scope.row[item.options.textprop].substring(0, 1) }}
                    </template>
                </template>
                <template v-else>暂无</template>
            </div>
        </template>
    </slot>
    <!-- tag -->
    <slot
        :name="item.prop"
        v-bind="scope"
        v-if="item.component == 'tag'"
    >
        <template
            v-for="(itm, index) in item.options"
            :key="index"
        >
            <el-tag v-if="itm.type == scope.row[item.prop]">{{ itm.label }} </el-tag>
        </template>
    </slot>
    <!-- butWatchMedia -->
    <slot
        :name="item.prop"
        v-bind="scope"
        v-if="item.component == 'mediaShow'"
    >
        <div class="mediaShow">
            <el-image
                class="mediaShow-img"
                :src="scope.row[item.options.mediaprop] + '?x-oss-process=image/resize,w_100/format,webp'"
                lazy
                fit="contain"
            />
        </div>
    </slot>
    <!-- link -->
    <slot
        :name="item.prop"
        v-bind="scope"
        v-if="item.component == 'link'"
    >
        <el-link
            :href="scope.row[item.prop]"
            target="_blank"
            >{{ item.options.text }}</el-link
        >
    </slot>
    <!-- button -->
    <slot
        :name="item.prop"
        v-bind="scope"
        v-if="item.component == 'button'"
    >
        <span
            class="table-button"
            v-for="(rs, rs_index) in item.options"
            :key="rs_index"
        >
            <template v-if="rs.component == 'lock'">
                <yy_button
                    :type="rs.type"
                    :label="rs.options.items[scope.row[rs.prop]].label"
                    :component="rs.component"
                    :options="rs.options"
                    :value="scope.row"
                    plain
                    size="small"
                    @finish-event="button_finish_event"
                    @dialog="dialog"
                ></yy_button>
            </template>
            <template v-else-if="rs.component == 'dialog'">
                <yy_dialog
                    :options="rs"
                    :form="scope.row"
                    :size="rs.options.size"
                    @refresh="button_finish_event"
                ></yy_dialog>
            </template>
            <template v-else-if="rs.component == 'detail'">
                <yy_descriptions
                    :label="rs.label"
                    :form="scope.row"
                    :title="rs.options.title"
                    :remote="rs.options.remote"
                    :items="rs.options.items"
                    :directionDesc="rs.options.directionDesc"
                ></yy_descriptions>
            </template>
            <template v-else>
                <yy_button
                    :type="rs.type"
                    :label="rs.label"
                    :component="rs.component"
                    :queryform="queryform"
                    :options="rs.options"
                    :value="scope.row"
                    plain
                    size="small"
                    @finish-event="button_finish_event"
                    @dialog="dialog"
                ></yy_button>
            </template>
        </span>
    </slot>
    <slot
        :name="item.prop"
        v-bind="scope"
        v-if="item.component == 'upload'"
    >
        <yy_upload
            v-if="item.component == 'upload'"
            :label="item.options.label"
            :templateUrl="item.options.templateUrl"
            :url="item.options.url"
            :data="item.options.data"
            :value="scope.row"
        ></yy_upload>
    </slot>
    <slot
        :name="item.prop"
        v-bind="scope"
        v-if="item.component == 'table'"
    >
        <el-popover
            placement="bottom"
            trigger="hover"
            width="auto"
        >
            <template #reference>
                <el-button style="margin-right: 16px">查看</el-button>
            </template>
            <el-table :data="JSON.parse(scope.row[item.prop])">
                <el-table-column
                    :prop="rs.prop"
                    :label="rs.label"
                    v-for="(rs, rs_index) in item.options"
                    :key="rs_index"
                />
            </el-table>
        </el-popover>
    </slot>
    <slot
        :name="item.prop"
        v-bind="scope"
        v-if="!item.component"
    >
        {{ scope.row[item.prop] }}
    </slot>
    <slot
        :name="item.prop"
        v-bind="scope"
        v-if="item.component == 'array'"
    >
        <div :data="JSON.parse(scope.row[item.prop])">
            <div
                :prop="rs.prop"
                :label="rs.label"
                v-for="(rs, rs_index) in item.options"
                :key="rs_index"
            >
                <span
                    v-for="(item, rs_index) in JSON.parse(scope.row[item.prop])"
                    :key="rs_index"
                >
                    {{ item[rs.prop] + ' ' }}
                </span>
            </div>
        </div>
    </slot>
    <slot
        :name="item.prop"
        v-bind="scope"
        v-if="item.component == 'arrayItems'"
    >
        <template
            v-for="(item, index) in JSON.parse(scope.row[item.prop])"
            :key="index"
        >
            <span>{{ item + ' ' }}</span>
        </template>
    </slot>
    <slot
        :name="item.prop"
        v-bind="scope"
        v-if="item.component == 'userlst'"
    >
        <div :data="JSON.parse(scope.row[item.prop])">
            <div
                :prop="rs.prop"
                :label="rs.label"
                v-for="(rs, rs_index) in item.options"
                :key="rs_index"
            >
                <div
                    v-for="(item, rs_index) in JSON.parse(scope.row[item.prop])"
                    :key="rs_index"
                >
                    <span
                        v-for="(userlst, index) in item"
                        :key="index"
                        style="text-indent: 1em; display: inline-block"
                    >
                        {{ userlst[rs.prop] }}
                    </span>
                </div>
            </div>
        </div>
    </slot>
</template>

<script>
export default {
    emits: ['dialog', 'finish-event'],
    props: {
        item: { type: Object, default: () => {} },
        scope: { type: Object, default: () => {} },
        queryform: { type: Object, default: () => {} }
    },
    data() {
        return {
            isSave: false,
            medicType: ''
            // coverImg:
            //     'http://files.qixuw.com/dingb9614df94342f570a1320dcb25e91351/publish/2023-03-29/yEhsAeslm4F4aBJzX7sYVBA6uB4N1yQk.jpeg'
        }
    },
    created() {},
    mounted() {
        if (this.item.component) {
            if (this.item.component == 'butWatchMedia') {
                if (this.item.options) {
                    this.item.options.items.forEach((el) => {
                        if (this.scope.row[this.item.options.decideProp] == el.name) {
                            this.medicType = el.value
                            if (this.medicType == 'video') {
                                this.scope.row[this.item.prop] =
                                    'http://files.qixuw.com/dingb9614df94342f570a1320dcb25e91351/publish/2023-04-07/pIOpdwCaP2KvEerm2TmSwE3sv99GQRxY.png'
                            }
                        }
                    })
                }
            }
        }
    },
    methods: {
        button_finish_event() {
            this.$emit('finish-event', true)
        }
    }
}
</script>

<style scoped lang="scss">
.table-button {
    display: inline-block;
    margin-right: 10px;
}
.img-text {
    height: 80px;
    width: 80px;
    background-color: #409eff;
    color: #fff;
    font-size: 30px;
    border-radius: 5px;
    line-height: 80px;
    text-align: center;
}

.butWatchMedia {
    display: flex;
    justify-content: center;
    .butWatchMedia-img {
        display: block;
        /* width: 200px; */
        height: 100px;
    }
}

.videoDiv {
    width: 100px;
    height: 100px;
    background-color: #79bbff;
    color: #fff;
    font-size: 20px;
    line-height: 100px;
    text-align: center;
}
</style>
