// function parseTime(time, cFormat) {
// 	if (arguments.length === 0) {
// 		return null;
// 	}
// 	const format = cFormat || "{y}-{m}-{d} {h}:{i}:{s}";
// 	let date;
// 	if (typeof time === "object") {
// 		date = time;
// 	} else {
// 		if (typeof time === "string" && /^[0-9]+$/.test(time)) {
// 			time = parseInt(time);
// 		}
// 		if (typeof time === "number" && time.toString().length === 10) {
// 			time = time * 1000;
// 		}
// 		date = new Date(time);
// 	}
// 	const formatObj = {
// 		y: date.getFullYear(),
// 		m: date.getMonth() + 1,
// 		d: date.getDate(),
// 		h: date.getHours(),
// 		i: date.getMinutes(),
// 		s: date.getSeconds(),
// 		a: date.getDay(),
// 	};
// 	const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
// 		let value = formatObj[key];
// 		// Note: getDay() returns 0 on Sunday
// 		if (key === "a") {
// 			return ["日", "一", "二", "三", "四", "五", "六"][value];
// 		}
// 		if (result.length > 0 && value < 10) {
// 			value = "0" + value;
// 		}
// 		return value || 0;
// 	});
// 	return time_str;
// }

// function get_data(url, data, callback) {
// 	getApp()
// 		.post(url, data)
// 		.then((res) => {
// 			try {
// 				if (res.ret == 200) {
// 					callback(res.data);
// 					// if (Array.isArray(res.data)) {

// 					// } else {
// 					// 	throw `请求失败,当前状态码：${res.ret}`
// 					// }
// 				}
// 			} catch (e) {
// 				throw e;
// 			}
// 		})
// 		.catch((e) => {
// 			console.log(e);
// 		});
// }

// function chack_data(data, from, that) {
// 	let keys = from.filter((item) => item.require);
// 	let res = keys.find((el) => {
// 		let str = JSON.stringify(data[el.key]);
// 		return (!data[el.key] && data[el.key] !== 0) || str == "{}" || str == "[]";
// 	});
// 	if (res) {
// 		// console.log("检测完成", that, res)
// 		res.is_show_require_text = true;
// 		if (Array.isArray(that.$refs[res.key])) {
// 			that.$refs[res.key][0].up_date();
// 		} else {
// 			that.$refs[res.key].up_date();
// 		}
// 	}
// 	return !res;
// }
// //flag为区别提交处理还是详情处理 默认详情处理   true为提交   false为详情处理
// function handle_data(from, data, flag) {
// 	console.log("表单处理前----1---->", data);
// 	//最后处理完成的结果数据res_obj
// 	let res_obj = {};

// 	//处理详情json格式
// 	if (!flag) {
// 		let keys = Object.keys(data);
// 		keys.forEach((key) => {
// 			if (data[key] && typeof data[key] == "string") {
// 				if (data[key].includes("[") || data[key].includes("{")) {
// 					data[key] = JSON.parse(data[key]);
// 				}
// 			}
// 		});
// 	}
// 	//处理表单参数
// 	try {
// 		handle_from(from);
// 	} catch (e) {
// 		console.log(e);
// 	}
// 	//处理提交json格式
// 	if (flag) {
// 		let keys = Object.keys(res_obj);
// 		keys.forEach((key) => {
// 			if (typeof res_obj[key] == "object") {
// 				res_obj[key] = JSON.stringify(res_obj[key]);
// 			}
// 		});
// 	}
// 	console.log("表单处理后----2---->", res_obj);
// 	return res_obj;

// 	function handle_from(from) {
// 		from.forEach((el) => {
// 			if (!el.key || el.key == "tip") {
// 				return;
// 			}
// 			if (el.opts) {
// 				if (typeof el.opts.key_map == "string") {
// 					handle_string(data, el, flag);
// 				} else if (Array.isArray(el.opts.key_map)) {
// 					handle_array(data, el, flag);
// 				} else if (typeof el.opts.key_map == "object") {
// 					handle_object(data, el, flag);
// 				} else {
// 					console.log("没有处理key_map的类型-------->", typeof el.opts.key_map);
// 				}
// 			} else {
// 				res_obj[el.key] = data[el.key];
// 			}
// 			if (el.child) {
// 				if (el.child_show_config && !flag) {
// 					el.child = el.child_show_config.show_list[data[el.key] + ""] || [];
// 				}
// 				handle_from(el.child);
// 			}
// 			if (el.child2) {
// 				if (el.child2_show_config && !flag) {
// 					el.child2 = el.child2_show_config.show_list[data[el.key] + ""] || [];
// 				}
// 				handle_from(el.child2);
// 			}
// 		});
// 	}

// 	function get_list(url, post_data, el) {
// 		getApp()
// 			.post(url, post_data)
// 			.then((res) => {
// 				el.child = res[data[el.key] + ""] || [];
// 				handle_from(el.child);
// 			})
// 			.catch((e) => {
// 				console.log(e);
// 			});
// 	}

// 	function handle_object(data, el, flag) {
// 		let res = {};
// 		let opts = el.opts;
// 		//处理合并   合并都为单选
// 		// console.log("数据处理------》", opts, flag)
// 		if (opts.merge) {
// 			if (flag) {
// 				//提交模块
// 				//提交数据都为数组
// 				let obj = handle_item_object(
// 					(data[el.key] && data[el.key][0]) || data[el.key],
// 					el,
// 					flag,
// 					opts.key_map
// 				);
// 				Object.assign(res_obj, obj);
// 			} else {
// 				//详情模块
// 				let obj = handle_item_object(data, el, flag, opts.key_map);
// 				res_obj[el.key] = [obj];
// 				// res_obj[el.key] = obj
// 			}
// 		} else {
// 			//处理非合并  此函数处理的都是单选
// 			if (flag) {
// 				//提交模块
// 				//提交数据都为数组
// 				let obj = handle_item_object(data[el.key][0], el, flag, opts.key_map);
// 				res_obj[el.key] = obj;
// 			} else {
// 				//详情模块
// 				let obj = handle_item_object(data[el.key], el, flag, opts.key_map);
// 				if (el.ctype == "yy_select_user" || el.ctype == "yy_combo_box") {
// 					res_obj[el.key] = [obj];
// 				} else {
// 					res_obj[el.key] = obj;
// 				}
// 			}
// 		}
// 	}

// 	function handle_array(data, el, flag) {
// 		let map_item = el.opts.key_map[0];
// 		if (typeof map_item == "string") {
// 			if (flag) {
// 				//提交模块
// 				res_obj[el.key] =
// 					data[el.key] && data[el.key].map((el) => el[map_item]);
// 			} else {
// 				//详情模块
// 				res_obj[el.key] =
// 					data[el.key] &&
// 					data[el.key].map((el) => ({
// 						[map_item]: el[map_item],
// 					}));
// 			}
// 		} else if (typeof map_item == "object") {
// 			res_obj[el.key] =
// 				data[el.key] &&
// 				data[el.key].map &&
// 				data[el.key].map((it) => handle_item_object(it, el, flag, map_item));
// 		} else {
// 			console.log("handle_array没有处理的类型", typeof map_item);
// 		}
// 	}

// 	function handle_string(data, el, flag) {
// 		let map_item = el.opts.key_map;
// 		if (flag) {
// 			//提交模块
// 			// console.log(data, "========", el.key);
// 			res_obj[el.key] =
// 				data[el.key] && data[el.key][0] ?
// 				data[el.key][0][map_item] :
// 				data[el.key][map_item];
// 		} else {
// 			//详情模块
// 			res_obj[el.key] = [{
// 				[map_item]: data[el.key],
// 			}, ];
// 		}
// 	}

// 	function handle_item_object(data_obj, el, flag, key_map) {
// 		let res = {};
// 		let opts = el.opts;
// 		let k_v = Object.entries(key_map);
// 		if (flag) {
// 			//提交模块
// 			k_v.forEach((ekey) => {
// 				//提交  =  详情
// 				if(data_obj){
// 					res[ekey[0]] = data_obj[ekey[1]];
// 				}
// 			});
// 		} else {
// 			//详情模块
// 			k_v.forEach((ekey) => {
// 				//详情  =  提交
// 				res[ekey[1]] = data_obj[ekey[0]];
// 			});
// 		}
// 		return res;
// 	}
// }

// function submit_data(url, data, callback) {
// 	getApp()
// 		.post(url, data)
// 		.then((res) => {
// 			try {
// 				if (res.ret == 200) {
// 					callback(res.data);
// 					// if (Array.isArray(res.data)) {

// 					// } else {
// 					// 	throw `请求失败,当前状态码：${res.ret}`
// 					// }
// 				}
// 			} catch (e) {
// 				throw e;
// 			}
// 		})
// 		.catch((e) => {
// 			console.log(e);
// 		});
// }

function deep_merge(target, obj) {
	let key_ls = Object.keys(obj);
	key_ls.forEach((key) => {
		if (target[key] && target[key].toString() === "[object Object]") {
			deep_merge(target[key], obj[key]);
		} else {
			target[key] = obj[key];
		}
	});
	return target;
}

// function submit_image(post_url, file, formData = {}) {
// 	return new Promise((resolve, reject) => {
// 		uni.uploadFile({
// 			url: post_url, //仅为示例，非真实的接口地址
// 			file: file,
// 			name: "file",
// 			formData: Object.assign({
// 					corp_product: getApp().globalData.corp_product,
// 				},
// 				formData
// 			),
// 			header: {
// 				Authorization: getApp().globalData.jwt,
// 			},
// 			success: (res) => {
// 				if (res.statusCode == 200) {
// 					let data = JSON.parse(res.data);
// 					if (!data.errcode) {
// 						resolve(data);
// 					} else {
// 						reject(data);
// 					}
// 				} else {
// 					reject(data);
// 				}
// 			},
// 			fail(err) {
// 				reject(err);
// 			},
// 		});
// 	});
// }
export {
	// parseTime,
	// handle_data,
	// chack_data,
	deep_merge,
	// submit_image
};
