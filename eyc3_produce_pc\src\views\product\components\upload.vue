<!--
 * @Descripttion: 文件导入
 * @version: 1.0
 * @Author: sakuya
 * @Date: 2022年5月24日11:30:03
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-01-06 20:56:45
-->

<template>
    <div v-if="dataType.category_id">
        <el-button
            icon="sc-icon-upload"
            @click="open"
            >{{ label }}</el-button
        >
    </div>
    <div
        class="magbutton"
        v-else
    >
        <el-button
            type="primary"
            icon="sc-icon-upload"
            @click="open"
            >{{ label }}</el-button
        >
    </div>
    <el-dialog
        v-model="dialog"
        title="导入"
        :width="550"
        :close-on-click-modal="false"
        append-to-body
        destroy-on-close
    >
        <el-progress
            v-if="loading"
            :text-inside="true"
            :stroke-width="20"
            :percentage="percentage"
            style="margin-bottom: 15px"
        />
        <div v-loading="loading">
            <el-upload
                ref="uploader"
                drag
                :accept="accept"
                :maxSize="maxSize"
                :limit="1"
                :data="data"
                :show-file-list="false"
                multiple
                :http-request="request"
                :before-upload="before"
                :on-progress="progress"
                :on-success="success"
                :on-error="error"
            >
                <slot name="uploader">
                    <el-icon class="el-icon--upload"><el-icon-upload-filled /></el-icon>
                    <div class="el-upload__text">将文件拖到此处或 <em>点击选择文件上传</em></div>
                </slot>
                <template #tip>
                    <div class="el-upload__tip">
                        <template v-if="tip">{{ tip }}</template>
                        <template v-else>请上传小于或等于 {{ maxSize }}M 的 {{ accept }} 格式文件</template>
                        <p
                            v-if="templateUrl || columnData"
                            style="margin-top: 7px"
                        >
                            <el-link
                                target="_blank"
                                type="primary"
                                :underline="false"
                                @click="dex"
                                >下载导入模板</el-link
                            >
                        </p>
                    </div>
                </template>
            </el-upload>
            <el-form
                v-if="$slots.form"
                inline
                label-width="100px"
                label-position="left"
                style="margin-top: 18px"
            >
                <slot
                    name="form"
                    :formData="formData"
                ></slot>
            </el-form>
        </div>
        <template #footer> </template>
    </el-dialog>
    <div
        v-if="luckysheetloadimng"
        class="modal-container"
    >
        <div
            style="height: 82vh"
            class="modal-content"
        >
            <div
                id="luckysheet"
                style="height: 80vh; width: 100%"
            ></div>
        </div>
        <div style="text-align: right; margin-top: 50px">
            <el-button @click="cancellation">取 消</el-button>
            <el-button
                type="primary"
                @click="submit"
                >提交</el-button
            >
        </div>
    </div>
    <el-dialog
        v-model="addupload"
        title="导入预检测"
        :width="550"
        :close-on-click-modal="false"
        append-to-body
        destroy-on-close
    >
        <div v-loading.fullscreen.lock="adduploadloading">
            <div
                v-for="item in detailtext"
                :key="item"
                v-if="detailtext.length > 0"
            >
                {{ item }}
            </div>
            <div v-else>数据无异常，请点击提交继续导入。</div>
        </div>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="claed_addupload()">取消</el-button>
                <el-button
                    type="primary"
                    @click="adduploadclick()"
                >
                    提交
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script>
import { ElMessage } from 'element-plus'
import LuckyExcel from 'luckyexcel'
import * as XLSX from 'xlsx'
export default {
    name: 'upload',
    emits: ['success'],
    props: {
        templateUrl: { type: String, default: '' },
        label: { type: String, default: '上传' },
        url: { type: String, default: '' },
        url_loading: { type: String, default: '' },
        data: { type: Object, default: () => {} },
        dataType: { type: Object, default: () => {} },
        value: { type: Object, default: () => null },
        downUrl: { type: String, default: '' },
        columnData: { type: Object, default: () => {} },
        filename: { type: String, default: '导入模版' },
        accept: { type: String, default: '.xls, .xlsx' },
        maxSize: { type: Number, default: 10 },
        multiple: { type: Boolean, default: false },
        tip: { type: String, default: '' }
    },
    data() {
        return {
            addupload: false,
            adduploadloading: false,
            dialog: false,
            luckysheetloadimng: false,
            loading: false,
            percentage: 0,
            formData: {},
            luckysheet: null,
            uploadloading: false,
            paramfile: null
        }
    },

    mounted() {},
    methods: {
        open() {
            this.dialog = true
            this.formData = {}
        },
        close() {
            this.dialog = false
            this.loading = false
        },
        before(file) {
            const maxSize = file.size / 1024 / 1024 < this.maxSize
            if (!maxSize) {
                this.$message.warning(`上传文件大小不能超过 ${this.maxSize}MB!`)
                return false
            }
            this.loading = true
        },
        progress(e) {
            this.percentage = e.percent
        },
        success(res, file) {
            this.$refs.uploader.handleRemove(file)
            this.$refs.uploader.clearFiles()
            this.loading = false
            this.percentage = 0
            this.$emit('success', res, this.close)
        },
        error(err) {
            this.loading = false
            this.percentage = 0
            this.$notify.error({
                title: '上传文件未成功',
                message: err
            })
        },
        dex() {
            this.$HTTP
                .get(this.templateUrl, {}, { responseType: 'blob' })
                .then((res) => {
                    this.downLoading = false
                    this.downLoadProgress = 0
                    if (res.result) {
                        import('@/utils/Export2Excel').then((excel) => {
                            let columnArr = this.columnData.map((n) => n).join(',')
                            excel.export_json_to_excel({
                                header: columnArr,
                                data: res.result,
                                filename: this.filename
                            })
                            this.dialog = false
                        })
                    } else {
                        console.log(res)
                        this.downLoadFile(res, this.filename)
                    }
                })
                .catch((err) => {
                    this.downLoading = false
                    this.downLoadProgress = 0
                    this.$notify.error({
                        title: '下载文件失败',
                        message: err
                    })
                })
        },
        downLoadFile(res, filename) {
            const link = document.createElement('a')
            const blob = new Blob([res], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;' })
            link.style.display = 'none'
            link.href = URL.createObjectURL(blob)
            link.download = `${filename + '.xlsx'}`
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
        },
        cancellation() {
            window.luckysheet.destroy()
            this.luckysheetloadimng = false
        },
        request(param) {
            const data = new FormData()
            data.append(param.filename, param.file)
            data.append('type', this.dataType.type)
            this.paramfile = data
            console.log((this.post_data = data), 'asdasdasdasdasdasda')
            this.renderExcel(param.file)
        },
        renderExcel(content) {
            this.luckysheetloadimng = true
            this.dialog = false
            this.loading = false
            // 将文件内容转换为LuckyExcel可以处理的数据格式
            LuckyExcel.transformExcelToLucky(content, (exportJson, luckysheetfile) => {
                this.uploadloading = false
                // 销毁原来表格
                window.luckysheet.destroy()
                //重新创建新表格
                window.luckysheet.create({
                    container: 'luckysheet', //设定DOM容器id
                    data: exportJson.sheets,
                    showinfobar: false, //是否显示顶部信息栏
                    toolbar: false, //隐藏工具栏
                    lang: 'zh', // 设定表格语言
                    enableAddRow: false, //允许添加行
                    enableAddBackTop: false //允许回到顶部
                })
            })
        },
        claed_addupload() {
            this.addupload = false
        },
        submit() {
            this.uploadloading = true
            let allSheetData = luckysheet.getluckysheetfile()
            let sheet1 = allSheetData[0]
            let downOriginData = sheet1.data
            const valuesOnlyData = downOriginData.map((row) => {
                return row.map((item) => (item ? item.v : null))
            })
            console.log(sheet1, valuesOnlyData, '111111')
            let array = valuesOnlyData.filter((innerArray) => {
                // 检查当前一维数组是否有非null元素
                return innerArray.some((item) => item !== null)
            })

            /* 检测数据位置是否正确
            let tenthElements = [];

            // 遍历二维数组
            for (let i = 0; i < array.length; i++) {
            // 检查当前行是否有足够的元素
            if (array[i].length >= 9) {
                // 获取第十个元素
                let tenthElement = array[i][8];
                tenthElements.push(tenthElement);
            } else {
                // 如果不足十个元素，则可以插入null或其他默认值
                tenthElements.push(null);
            }
            }

            console.log(tenthElements);
            */
            const result = []
            for (const obj of array) {
                const newObj = {}
                for (let i = 0; i < 38; i++) {
                    newObj[i] = obj[i]
                }
                result.push(newObj)
            }
            // array.map((obj) => O

            this.array = result
            const post_data = { data: array, type: this.dataType.type }
            // console.log(this.url_loading, post_data);
            // 调接口
            this.$HTTP
                .post(this.url_loading, post_data)
                .then((res) => {
                    if (res.errcode != 0) {
                        ElMessage.error(res.errmsg)
                        this.uploadloading = false
                    } else {
                        this.detailtext = res.result
                        this.addupload = true
                    }
                    this.dialog = false
                    this.loading = false
                    this.uploadloading = false
                    this.luckysheetloadimng = false
                })
                .catch((err) => {
                    this.uploadloading = false
                    ElMessage({
                        message: err,
                        type: 'success'
                    })
                })
        },
        adduploadclick() {
            const post_data = { data: this.array, category_id: this.dataType.category_id }
            this.adduploadloading = true
            console.log('post_data', post_data.data)
            // 使用xlsx库创建一个工作簿
            const wb = XLSX.utils.book_new()

            // 将对象数组转换为工作表，指定对象中的键作为表头
            const ws = XLSX.utils.json_to_sheet(post_data.data)

            // 将工作表添加到工作簿中
            XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')

            // 将工作簿写入一个二进制数组
            const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'array' })

            // 使用FileSaver.js保存文件
            const blob = new Blob([wbout], { type: 'application/octet-stream' })
            // console.log(blob,'blob');
            // 创建 FormData 对象
            const formData = new FormData()

            // 添加 Blob 对象到 FormData
            formData.append('file', blob, 'luckysheet_data.xlsx') // 'file' 是服务器期望的文件字段名，'hello.txt' 是文件名
            // saveAs(blob, 'luckysheet_data.xlsx')
            console.log(formData,'formData');
            console.log(blob,'blob');
            this.$HTTP
                .post(this.url, formData)
                .then((res) => {
                    console.log(res,'res>>>>');
                    if (res.errcode != 0) {
                        this.addupload = false
                        ElMessage({
                            message: res.errmsg,
                            type: 'success'
                        })
                    } else {
                        this.addupload = false
                        this.uploadloading = false
                        this.adduploadloading = false
                        this.luckysheetloadimng = false
                        window.luckysheet.destroy()
                        this.$emit('uploadload', res.result)
                        ElMessage({
                            message: '操作成功!',
                            type: 'success'
                        })
                    }
                    this.dialog = false
                })
                .catch((err) => {
                    ElMessage({
                        message: err,
                        type: 'success'
                    })
                })
        }
    }
}
</script>

<style lang="scss" scoped>
.magbutton {
    margin: 10px 0;
}
.modal-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    /* transform: translate(-50%, -50%); */
    background-color: #fff;
    /* max-width: 800px;
    /* padding: 20px; */
    z-index: 10;
}

.modal-content {
    height: 100%;
    /* animation: modalOpen 0.3s ease-in-out; */
}
.modal-footer {
}
@keyframes modalOpen {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 添加关闭动画 */
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s;
}
.fade-enter,
.fade-leave-to {
    opacity: 0;
}
#luckysheet {
    margin: 0px;
    padding: 0px;
    position: absolute;
    width: 100%;
    left: 0px;
    top: 60px;
    bottom: 0px;
    z-index: 9999;
}
</style>
