<template>
    <yp_form :columns="columns"></yp_form>
</template>

<script>
export default {
    name: 'BasicAuditForm',
    data() {
        return {
            header: null,
            form: {},
            options: {},
            columns: [
                {
                    label: '标题',
                    name: 'title',
                    component: 'input',
                    options: {
                        disabled: true
                    }
                },
                {
                    label: '操作人',
                    name: 'user_name',
                    component: 'input',
                    options: {
                        disabled: true
                    }
                },
                {
                    label: '操作时间',
                    name: 'updated_at',
                    component: 'input',
                    options: {
                        disabled: true
                    }
                },
                {
                    label: 'IP',
                    name: 'ip',
                    component: 'input',
                    options: {
                        disabled: true
                    }
                },
                {
                    label: '操作详情',
                    name: 'content',
                    component: 'input',
                    options: {
                        disabled: true,
                        type: 'textarea'
                    }
                }
            ]
        }
    },
}
</script>

<style>
</style>