<template>
    <el-table
        ref="multipleTable"
        :data="tableData"
        stripe
        :header-cell-style="{
            background: '#F3F6FB',
            color: '#606266',
            fontSize: '14px',
            fontWeight: '400'
        }"
        style="min-width: 50%"
        row-key="id"
        id="tableId"
    >
        <el-table-column
            align="center"
            v-for="item in tableTitleList"
            :key="item.prop"
            :label="item.name"
            show-overflow-tooltip
        >
            <template v-slot="scope">
                <span>{{ scope.row[item.prop] ? scope.row[item.prop] : item.default }}</span>
            </template>
        </el-table-column>
    </el-table>
</template>
<script>
export default {
    props: {
        tableslot: { type: Object, default: () => {} }
    },
    data() {
        return {
            tableData: [],
            tableTitleList: []
        }
    },
    mounted() {
        this.getData()
    },
    methods: {
        // 处理远程
        // 获取数据
        getData() {
            this.tableTitleList = this.tableslot.tableTitleList
            this.$HTTP.post(this.tableslot.url).then((res) => {
                if (res.errcode != 0) {
                } else {
                    this.tableData = res.result
                }
            })
        }
    }
}
</script>
