<template>
    <el-container>
        <el-header>
            <div class="left-panel">
                <el-button
                    type="primary"
                    icon="el-icon-plus"
                    @click="add"
                >添加</el-button>
            </div>
            <!-- <div class="right-panel">
                <div class="right-panel-search">
                    <el-input
                        v-model="search.keyword"
                        placeholder="部门名称"
                        clearable
                    ></el-input>
                    <el-button
                        type="primary"
                        icon="el-icon-search"
                        @click="upsearch"
                    ></el-button>
                </div>
            </div> -->
        </el-header>
        <el-main class="nopadding">
            <div
                class="scTable"
                :style="{ height: _height }"
                ref="scTableMain"
                v-loading="loading"
            >
                <scTable
                    ref="table"
                    :data="post_datalist"
                    :lthority="limauthority"
                    row-key="id"
                    @selection-change="selectionChange"
                    hidePagination
                >
                    <el-table-column
                        label="分类名称"
                        prop="title"
                        min-width="250"
                    ></el-table-column>
                    <el-table-column
                        label="创建时间"
                        prop="created_at"
                        min-width="180"
                    ></el-table-column>
                    <el-table-column
                        label="操作"
                        align="center"
                        min-width="150"
                    >
                        <template #default="scope">
                            <el-button-group>
                                <el-button
                                    text
                                    type="primary"
                                    size="small"
                                    @click="table_edit(scope.row, scope.$index)"
                                    >编辑</el-button
                                >
                                <el-popconfirm
                                    title="确定删除吗？"
                                    @confirm="table_del(scope.row, scope.$index)"
                                >
                                    <template #reference>
                                        <el-button
                                            text
                                            type="primary"
                                            size="small"
                                            >删除</el-button
                                        >
                                    </template>
                                </el-popconfirm>
                            </el-button-group>
                        </template>
                    </el-table-column>
                </scTable>
            </div>
        </el-main>
    </el-container>
    <el-drawer :title="titleMap[mode]" v-model="visible" :width="600" destroy-on-close @closed="$emit('closed')">
		<el-form :model="form" :rules="rules" :disabled="mode=='show'" ref="dialogForm" label-width="100px">
			<el-form-item label="上级分类" prop="parentId">
				<el-cascader ref="refCascader" v-model="form.parent_id" :options="groups" :props="groupsProps" :show-all-levels="false" clearable style="width: 100%;" @change="handleChange"></el-cascader>
			</el-form-item>
			<el-form-item label="分类名称" prop="title">
				<el-input v-model="form.title" placeholder="请输入分类名称" clearable></el-input>
			</el-form-item>
		</el-form>
		<template #footer>
			<div style="text-align: left;">
                <el-button type="primary" :loading="isSaveing" @click="submit()">提 交</el-button>
                <el-button @click="visible=false" >取 消</el-button>
            </div>
		</template>
	</el-drawer>
</template>

<script>
import { ElMessage, ElNotification } from 'element-plus'
export default {
    name: 'dept',
    components: {
    },
    data() {
        return {
            loading:false,
            post_datalist: [],
            selection: [],
            search: {
                keyword: null
            },
            limauthority:false,
            height:'100%',
            mode: "add",
				titleMap: {
					add: '新增',
					edit: '编辑',
					show: '查看'
				},
				visible: false,
				isSaveing: false,
				//表单数据
				form: {
                    title:"",
					parent_id: "",
                    parent_title:""
				},
				//验证规则
				rules: {
					title: [
						{required: true, message: '请输入分类名称'}
					]
				},
				//所需数据选项
				groups: [],
				groupsProps: {
					value: "id",
                    label:'title',
					emitPath: false,
					checkStrictly: true
				}
        }
    },
    computed: {
        _height() {
            return Number(this.height) ? Number(this.height) + 'px' : this.height
        },
    },
    mounted(){
        this.post_data_list('category/get_tier')
    },
    methods: {
        // 数据请求
        post_data_list(url){
            this.loading = true
            this.$HTTP.post(url).then((res) => {
                if (res.errcode != 0) {
                    if(res.errcode!=510){
                        ElMessage.error(res.errmsg)
                    }else{
                        this.limauthority = true
                    }
                    this.loading = false
                } else {
                    this.post_datalist = res.result
                    this.groups = res.result
                    this.loading = false
                    // this.$notify({
                    //     title: '添加',
                    //     message: '操作成功 ',
                    //     type: 'success',
                    //     duration: 2000
                    // })
                }
            })
        },
        //添加
        add() {
            this.visible = true
            this.mode = 'add'
            this.form = {
                title:"",
				parent_id: "",
                parent_title:""
			}
        },
        //编辑
        table_edit(row) {
            this.mode = 'edit'
            this.visible = true
            this.form = {
                id:row.id,
                title:row.title,
				parent_id: row.parent_id,
                parent_title:row.parent_title
			}
        },
        //删除
        async table_del(row) {
            var reqData = { id: row.id }
            this.$HTTP.post('category/post_del', reqData).then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    this.post_data_list('category/get_tier')
                    this.$notify({
                        title: '删除',
                        message: '操作成功 ',
                        type: 'success',
                        duration: 2000
                    })
                }
            })
        },
        // 获取父级title
        handleChange(a) {
            const areaTextArr = [];
            const arr = this.$refs["refCascader"].getCheckedNodes()[0].pathNodes;
            arr.forEach((i) => {
                areaTextArr.push(i.label);
            });
            this.form.parent_title = areaTextArr[areaTextArr.length-1]
        },
        // 提交
        submit(){
            this.isSaveing = true
            if(this.mode == 'edit'){
                this.$HTTP.post('category/post_modify', this.form).then((res) => {
                    if (res.errcode != 0) {
                        this.isSaveing = false
                        ElMessage.error(res.errmsg)
                    } else {
                        this.post_data_list('category/get_tier')
                        this.isSaveing = false
                        this.visible = false
                        this.$notify({
                            title: '编辑',
                            message: '操作成功 ',
                            type: 'success',
                            duration: 2000
                        })
                    }
                })
            }else{
                this.$HTTP.post('category/post_add', this.form).then((res) => {
                    if (res.errcode != 0) {
                        this.isSaveing = false
                        ElMessage.error(res.errmsg)
                    } else {
                        this.post_data_list('category/get_tier')
                        this.isSaveing = false
                        this.visible = false
                        this.$notify({
                            title: '添加',
                            message: '操作成功 ',
                            type: 'success',
                            duration: 2000
                        })
                    }
                })
            }
            console.log(this.form);
        }
    }
}
</script>

<style scoped>
.scTable:deep(.el-table__footer) .cell {
    font-weight: bold;
}
.scTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-horizontal {
    height: 12px;
    border-radius: 12px;
}
.scTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-vertical {
    width: 12px;
    border-radius: 12px;
}
</style>
