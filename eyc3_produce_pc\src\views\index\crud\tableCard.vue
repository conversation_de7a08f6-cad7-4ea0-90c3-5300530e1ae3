<template>
	<el-container>
        <el-header>
            <el-page-header @back="goBack" :content="content">
            </el-page-header>
        </el-header>
        <el-main class="nopadding" v-loading="pictLoading">
            <el-table
            @row-contextmenu="rowContextmenu"
                :data="post_list"
                stripe
                style="width: 100%,height: 50%;">
                <template #empty>
                    <el-empty
                        description="暂无数据"
                        :image-size="200"
                    ></el-empty>
                </template>
                <el-table-column
                    width="50"
                    label="图标"
                    #default="scope"
                >
                    <el-icon size="16px" color="color" v-if="scope.row.type==3"> <!-- 原材料 -->
                        <sc-icon-raw-material/>
                    </el-icon>
                    <el-icon size="16px" color="color" v-if="scope.row.type==0"> <!-- 产品 -->
                        <sc-icon-product/>
                    </el-icon>
                    <el-icon size="16px" color="color" v-if="scope.row.type==2"> <!-- 零件 -->
                        <sc-icon-part/>
                    </el-icon>
                    <el-icon size="16px" color="red" v-if="scope.row.type==1"> <!-- 部件 -->
                        <sc-icon-mponent/>
                    </el-icon>
                </el-table-column>
                <el-table-column
                    label="ID"
                    min-width="70"
                >
                    <template #default="scope">
                        <span>{{ scope.row.id }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="number"
                    label="代号"
                    min-width="180"
                    align="center"
                >
                    <template #default="scope">
                        <span>{{ scope.row.number }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="title"
                    label="名称"
                    min-width="100"
                    align="center"
                >
                    <template #default="scope">
                        <span style="color:#4496ee;cursor:pointer;" @click="returnBomClick(scope.row)">{{ scope.row.title }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="drawing_number"
                    label="图号"
                    min-width="100"
                    align="center"
                >
                    <template #default="scope">
                        <span>{{ scope.row.drawing_number }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="specs"
                    label="规格"
                    min-width="100"
                    align="center"
                >
                    <template #default="scope">
                        <span>{{ scope.row.specs }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="texture_title"
                    label="材质"
                    min-width="100"
                    align="center"
                >
                    <template #default="scope">
                        <span>{{ scope.row.texture_title }}</span>
                    </template>
                </el-table-column>
            </el-table>
        </el-main>
        <el-footer>
            <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="page.page"
                :page-sizes="[10, 20, 30, 40]"
                :page-size="page.per_page"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                small
            >
            </el-pagination>
        </el-footer>
    </el-container>
    <div
        class="box-menu"
        v-if="menuVisible"
        :style="{ left: menu_left + 'px', top: menu_top + 'px' }"
    >
        <div @click.stop="pegging_click()">
            <el-button
                type="link"
                icon="el-icon-edit"
                >BOM反查</el-button
            >
        </div>
    </div>
</template>

<script>

	export default {
		data() {
			return {
				page:{
                    page:1,
                    per_page:10
                },
                checkBom: null,
                pictLoading:false,
                menuVisible: false,
                content:'',
                post_list:[],
                total:0,
			}
		},
		created(){

		},
		mounted(){
            this.content = this.$route.params.title
            if(this.$route.params.op){
				this.post_data(this.$route.params.url,{material_number:this.$route.params.op})
			}else{
				this.post_data(this.$route.params.url)
			}
		},
		methods:{
            // bom反查
            pegging_click(){
            console.log(this.checkBom, 'this.BomObj')
            this.$store.state.isShowOutTree=false
			this.form.parent_number = this.checkBom.number
			this.$store.state.listObj_bom = {
				isShow: true,
				isShowTree: true,
				items:  this.checkBom,
				table: false,
				upload: false
			}
            this.$router.push({path:'/search/tree'})

			this.menuVisible = false
    },
             // 鼠标右击事件
        rowContextmenu(row, column, event) {
            this.$emit('rownotification', false)
            this.$store.state.menuVisibletop = true
            console.log(this.$store.state, this.$store.state.menuVisibletop, 'this.$store.menuVisibletop')
            this.BomObj = row
            this.checkBom = {
                ctype: 'product',
                drawing_number: row.drawing_number,
                id: row.id,
                title: row.title,
                son: row.son,
                type: row.type,
                number: row.number,
                dosage: row.dosage,
                part_type: row.part_type,
                status: row.status,
                specs: row.specs
            }
            this.$store.state.border_bom = [
                {
                    id: row.id,
                    title: row.title,
                    number: row.number
                }
            ]
            this.$store.state.cost_number = row.number
            this.menuVisible = true
            // 节点数据
            this.form = {
                parent_material_number: row.number,
                material_number: '',
                title: '',
                type: '',
                drawing_number: '',
                specs: '',
                weight: '',
                unit_title: '',
                unit_id: '',
                texture_id: '',
                texture_title: '',
                price: '',
                quota: '',
                part_type_id: '',
                part_type_title: '',
                dosage: '',
                category_title: '',
                category_id: ''
            }
            // 将菜单显示在鼠标点击旁边定位
            this.menu_left = event.clientX + 50
            this.menu_top = event.clientY - 0
            document.addEventListener('click', this.foo)
        },
        foo() {
            this.menuVisible = false
            document.removeEventListener('click', this.foo)
        },
            // 返回BOM页面
            returnBomClick(op){
                console.log(op,'返回BOm页面');
                this.$store.state.listObj = {
                        items:op,
                }
                let myArray = 
                    {
                        id: op.id,
                        craft_small_ver:op.craft_small,
                        ctype:"product",
                        drawing_number:op.drawing_number,
                        title: op.title,
                        number: op.number,
                        type:op.type,
                    }
                
                    this.$store.state.bomAddIndex=myArray
                    // // console.log(this.$store.state.listObj);
                // // localStorage.setItem("arraylist",JSON.stringify(myArray))
                // // localStorage.setItem("parent_material_number",op.number)
                // // localStorage.setItem("material_id",op.id)
                this.$router.push({path:'/index/crud/IndexopenBom'})

            },
            //返回上一页
            goBack() {
                this.$router.push({path:'/index'})
                // this.$router.go(-1)
            },
            // 获取接口数据
			post_data(url,op){
                this.pictLoading = true
                this.page = {...this.page,...op}
				this.$HTTP
                .post(url,this.page).then(res=>{
                    if (res.errcode != 0) {
                        ElMessage.error(res.errmsg)
                        this.pictLoading = false
                    } else {
                        this.pictLoading = false
                        this.post_list = res.result.data
                        this.total = res.result.total
                    }
				})
			},
            // 分页事件
            handleSizeChange(val) {
                // console.log(`每页 ${val} 条`)
                this.page.per_page = val
                this.post_data(this.$route.params.url)
            },
            handleCurrentChange(val) {
                // console.log(`当前页: ${val}`)
                this.page.page = val
                this.post_data(this.$route.params.url)
            },
		}
	}
</script>

<style scoped lang="scss">
.box-menu {
    // width: 100px;
    position: absolute;
    z-index: 1000;
    background-color: #fff;
    box-shadow: 0px 0px 10px #ccc, 0px 0px 20px #ccc, 0px 0px 30px #ccc;
    padding: 10px;

    div {
        cursor: pointer;
        line-height: 30px;
    }
}
</style>
