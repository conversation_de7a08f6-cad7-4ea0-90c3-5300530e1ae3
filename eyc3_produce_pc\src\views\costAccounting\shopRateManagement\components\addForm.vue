<template>
    <el-drawer v-model="drawer" :size="800" :title="savetitle" direction="rtl" destroy-on-close>
        <el-main style="padding: 0 30px" class="el-main">
            <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="auto" class="demo-ruleForm"
                :size="formSize" status-icon>
                <el-row>
                    <!-- <el-col :span="12">
                        <el-form-item label="实例标题:" prop="name">
                            <el-input v-model="ruleForm.name" />
                        </el-form-item>
                    </el-col> -->
                    <el-col :span="12">
                        <el-form-item label="车间:" prop="title">
                            <el-input v-model="ruleForm.title" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="代码:" prop="code">
                            <el-input v-model="ruleForm.code" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="车间费率:" prop="hour_rate">
                            <el-input-number v-model="ruleForm.hour_rate" :precision="2" :step="0.1" :min="0" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div style="margin-top: 75vh;">
                <el-button type="primary" :loading="isSaveing" @click="submit()">提 交</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-main>

    </el-drawer>
</template>
<script setup>
import { ref, defineProps, defineEmits } from 'vue';
import http from '@/utils/request';
import { ElMessage } from 'element-plus';

const props = defineProps({
    getDataList: {
        type: Function,
        required: true
    }
});

const emit = defineEmits(['update:modelValue']);

const drawer = ref(false)
const savetitle = ref("")
const isSaveing = ref(false)
const isAdd = ref(true)
const ruleFormRef = ref()
const ruleForm = ref({
    // name: "",
    title: "",
    code: "",
    hour_rate: 0,
})
const rules = ref({
    // name: [
    //     {
    //         required: true,
    //         message: '请输入设备名称',
    //         trigger: 'blur',
    //     }
    // ],
    title: [
        {
            required: true,
            message: '请输入车间名称',
            trigger: 'blur',
        }
    ],
    code: [
        {
            required: true,
            message: '请输入代码',
            trigger: 'blur',
        }
    ]
})

function open(titel) {
    savetitle.value = titel
    drawer.value = true
    isAdd.value = true
    ruleForm.value = {
        // name: "",
        title: "",
        code: "",
        hour_rate: 0,
    }
}
async function submit() {
    isSaveing.value = true
    await ruleFormRef.value.validate((valid, fields) => {
        if (valid) {
            if(isAdd.value){
                http.post("center/post_add", ruleForm.value).then(res => {
                    if (res.errcode != 0) {
                        ElMessage.error(res.errmsg)
                        isSaveing.value = false
                        drawer.value = false
                    } else {
                        ElMessage.success("添加成功")
                        isSaveing.value = false
                        drawer.value = false
                        props.getDataList()
                    }
                }).catch((err)=>{
                    isSaveing.value = false
                    drawer.value = false
                })
            }else{
                http.post("center/post_modify", ruleForm.value).then(res => {
                    if (res.errcode != 0) {
                        ElMessage.error(res.errmsg)
                        isSaveing.value = false
                        drawer.value = false
                    } else {
                        ElMessage.success("修改成功")
                        isSaveing.value = false
                        drawer.value = false
                        props.getDataList()
                    }
                }).catch((err)=>{
                    isSaveing.value = false
                    drawer.value = false
                })
            }
        } else {
            isSaveing.value = false
        }
    })
}
function editItem(titel, item) {
    savetitle.value = titel
    ruleForm.value = item
    isAdd.value = false
    drawer.value = true
}
function cancel() {
    drawer.value = false
}
defineExpose({
    open,
    editItem
})
</script>