<!--
 * @author: 吉慧雯
 * @name: 审计日志
 * @desc: 介绍
 * @LastEditTime: 2022-02-06
 * @FilePath: \eyc3_guard_pc/src/views/work/basic/audit
-->
<template>
    <yp_list
        ref="table"
        :url="url"
        :columns="columns"
        :formitems="formitems"
        :buttonList="buttonList"
    >
    </yp_list>
</template>

<script>
export default {
    name: 'BasicAudit',
    data() {
        return {
            url: 'operate_log/get_ls',
            columns: [
                {
                    label: '标题',
                    prop: 'title'
                },
                {
                    label: '操作人',
                    prop: 'user_name'
                },
                {
                    label: '操作时间',
                    prop: 'created_at'
                },
                {
                    label: 'IP',
                    prop: 'ip'
                },
                {
                    label: '操作',
                    prop: 'action',
                    component: 'button',
                    options: [
                        {
                            label: '详情',
                            component: 'detail',
                            options: {
                                title: '审计日志详情',
                                column: 1,
                                directionDesc: 'vertical',
                                remote: {
                                    api: 'operate_log/get_info',
                                    data: {
                                        id: '$id'
                                    }
                                },
                                items: [
                                    {
                                        label: '标题',
                                        prop: 'title'
                                    },
                                    {
                                        label: '操作人',
                                        prop: 'user_name'
                                    },
                                    {
                                        label: '操作时间',
                                        prop: 'created_at'
                                    },
                                    {
                                        label: 'IP',
                                        prop: 'ip'
                                    },
                                    {
                                        label: '操作详情',
                                        prop: 'content'
                                    }
                                ]
                            }
                        }
                        // {
                        //     label: '详情',
                        //     component: 'form',
                        //     options: {
                        //         name: 'BasicAuditDetail', // 跳转路由名
                        //         remote: {
                        //             state: 'detail', // 状态
                        //             label: '审计日志详情', // 页头标题
                        //             api: 'operate_log/get_info', // 获取详情接口
                        //             data: {
                        //                 // 获取详情接口数据
                        //                 id: '$id'
                        //             }
                        //         }
                        //     }
                        // }
                    ]
                }
            ],
            formitems: [
                {
                    label: '操作人',
                    name: 'user_name',
                    component: 'input',
                    options: {
                        placeholder: '请输入'
                    }
                },
                {
                    label: '开始时间',
                    name: 'start_time',
                    value: '',
                    component: 'date',
                    options: {
                        type: 'datetime',
                        valueFormat: 'YYYY-MM-DD HH:mm:ss'
                    },
                },
                {
                    label: '结束时间',
                    name: 'end_time',
                    value: '',
                    component: 'date',
                    options: {
                        type: 'datetime',
                        valueFormat: 'YYYY-MM-DD HH:mm:ss'
                    },

                },
                {
                    label: '物料号',
                    name: 'number',
                    component: 'input',
                    options: {
                        placeholder: '请输入'
                    }
                },
                // {
                //     label: '日期范围',
                //     name: 'date_interval',
                //     value: [
                //         this.$TOOL.dateFormat(new Date(), 'yyyy-MM-01'),
                //         this.$TOOL.dateFormat(new Date(), 'yyyy-MM-dd')
                //     ],
                //     component: 'date',
                //     options: {
                //         type: 'daterange',
                //         rangeseparator: '至',
                //         startplaceholder: '开始日期',
                //         endplaceholder: '结束日期',
                //         valueFormat: 'YYYY-MM-DD'
                //     },
                // }
            ]
        }
    }
}
</script>

<style lang="scss" scoped>
</style>
