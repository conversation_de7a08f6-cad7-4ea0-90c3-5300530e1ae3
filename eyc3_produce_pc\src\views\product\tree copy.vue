<template>
    <div
        v-if="isShowTree"
        class="ShowTree"
    >
        <h1 style="margin-top: 20px; margin-bottom: 20px;width: 350px;">反查结果： <el-button @click="closePage" class="butonn">关闭</el-button></h1>

        <el-tree
            class="menu"
            :data="treeData"
            style="width: 100%"
            :allow-drop="handleAllowDrop"
            draggable
            node-key="id"
            default-expand-all
            highlight-current
            :props="defaultProps"
            @node-click="groupClick"
            @node-contextmenu="rightClick"
            :current-node-key="selectedNodeId"
        >
        <!-- <el-tree
        class="menu"
        :data="treeData"
        style="width: 500%"
        :allow-drop="handleAllowDrop"
        draggable
            node-key="id"
            default-expand-all
            highlight-current
            :props="defaultProps"
            :load="loadNode"
            @node-drop="nodeDrop"
            @node-click="groupClick"
            @node-contextmenu="rightClick"
            @node-expand="onNodeExpand"
            :allow-drag="allowDrag"
        > -->
            <template #default="{ node, data }">
                <span class="custom-tree-node">
                    <span>
                        <el-icon
                            size="16px"
                            color="#409EFC"
                            class="no-inherit"
                        >
                            <el-icon-folder v-if="data.ctype == 'category'" />
                            <el-icon
                                v-if="data.ctype == 'product'"
                                size="16px"
                                color="#409EFC"
                                class="no-inherit"
                            >
                                <sc-icon-raw-material v-if="data.type == 3" />
                                <el-icon
                                    size="16px"
                                    color="color"
                                    v-else-if="data.type == 2"
                                >
                                    <!-- 零件 -->
                                    <sc-icon-part />
                                </el-icon>
                                <el-icon
                                    size="16px"
                                    color="red"
                                    v-else-if="data.type == 1"
                                >
                                    <!-- 部件 -->
                                    <sc-icon-mponent />
                                </el-icon>
                                <el-icon-document v-else-if="data.ctype == 'product'" />
                            </el-icon>
                        </el-icon>
                        <span v-if="data.ctype == 'category'" :class="{ 'custom-highlight': isSelectedNode(data.id) }">{{ node.label }}</span>
                        <span v-if="data.ctype == 'product'">
                            <el-tooltip
                                :content="`${data.number} ${data.dosage ? '【' + data.dosage + '】' : ''}【${
                                    node.label
                                }】${data.drawing_number ? '—' + data.drawing_number : ''}${
                                    data.specs ? '—' + data.specs : ''
                                }`"
                                placement="right-start"
                                effect="dark"
                            >
                                <span :class="`${data.part_type == 1 ? 'text_or' : 'text_over'}`"
                                    >{{ data.number }} {{ data.dosage ? '【' + data.dosage + '】' : '' }} 【{{
                                        node.label
                                    }}】{{ data.specs ? '—' + data.specs : '' }}</span
                                >
                            </el-tooltip>
                        </span>
                    </span>
                </span>
            </template>
        </el-tree>
        <div class="box-menu"  v-if="menuVisible" :style="{ left: menu_left + 'px', top: menu_top + 'px' }">
						<div @click.stop="tree_add()">
							<el-button type="link" icon="el-icon-plus">新建</el-button>
						</div>
						<div v-if="subdata">
							<el-button type="link" icon="el-icon-circle-plus" @click.stop="searchRef()">添加</el-button>
						</div>
						<div v-if="subdata">
							<el-button type="link" icon="el-icon-document-copy" @click.stop="bomcopy()">复制</el-button>
						</div>
						<div>
							<el-button type="link" icon="el-icon-Finished" @click.stop="copyClick()">衍生</el-button>
						</div>
						<div>
							<el-button type="link" icon="el-icon-edit" @click.stop="dicEdit()">修改</el-button>
						</div>
						<div>
							<el-button type="link" icon="el-icon-search" @click.stop="backward()">反查</el-button>
						</div>
						<div>
							<el-button type="link" icon="el-icon-refresh-left" @click.stop="Refresh()">刷新</el-button>
						</div>
						<div>
							<el-button type="link" icon="el-icon-view" @click.stop="detection()">检测</el-button>
						</div>
						
							<div>
								<el-button type="link" icon="el-icon-tickets" @click.stop="costClick()">成本</el-button>
							</div>
							<div v-if="subdata">
							<el-button type="link" icon="el-icon-circle-close" @click.stop="Deactivate()"  v-show="deactivate">停用</el-button>
						</div>
						<div>
							<el-button type="link" icon="el-icon-MessageBox" @click.stop="printClick()">打印</el-button>
						</div>
						<div v-if="subdata">
							<el-button type="link" icon="el-icon-Download" @click.stop="download(currentData)" v-show="downloadData">下载</el-button>
						</div>
					
					
					
					
						<div v-if="bomp">
							<el-button type="link" icon="el-icon-document-checked" @click.stop="bompaste()">粘贴</el-button>
						</div>
						<div>
							<!-- <el-button type="link" icon="el-icon-delete" @click.stop="dicDel()">删除</el-button> -->
						</div>
						<div @click.stop="uploadClick()" v-if="!subdata">
										<Upload
										v-if="upload"
									:label="upload.label"
									:templateUrl="upload.templateUrl"
									:columnData="upload.columnData"
									:url="upload.url"
									:maxSize="upload.maxSize"
									:accept="upload.accept"
									:filename="upload.filename"
									:dataType="{type:0,category_id:currentData.id}"
									:url_loading="upload.url_loading"
									@uploadload="uploadload"
									
										></Upload>
										
										</div>
					</div>
    </div>
    <el-container  v-if="isShowTree" style="width: 5200%;background-color: #ffffff;">
        <listitem @drawer="drawer"></listitem>
    </el-container>
</template>

<script>
import listitem from './listTree.vue'
export default {
    components: {
        listitem
    },
    data() {
        return {
            treeData: [],
            groupData: [],
            treeDataAll: [],
            // 反查bom存的文件
            folderData: [],
            defaultProps: {
                children: 'children',
                label: 'title'
            },
            editForm: {
				node: null, // 存储被点击的节点
				add:null,//存储添加的数据
			},
            menuVisibletop: false,
            menuVisible: false,
            subdata: false,
            bomp:false,
			bomtype:'0',
            form: {
                parent_number: '',
                number: '',
                title: '',
                type: '',
                drawing_number: '',
                specs: '',
                weight: '',
                unit_title: '',
                unit_id: '',
                texture_id: '',
                texture_title: '',
                price: '',
                quota: '',
                part_type_id: '',
                part_type_title: '',
                dosage: '',
                category_title: '',
                category_id: '',
                tags: '',
                selectedNodeId: null,
                menuVisibletop: false,
            },
            isShowTree: false,
            isShowTreeS: false,
            currentData: []
        }
    },
    created() {
        this.$store.watch(
            (state) => state.listObj,
            (newValue) => {
                this.isShowTree = newValue.isShowTree
                this.targetNodeId = newValue.items.number
                this.gridDataAll = [newValue.items]
                console.log(this.gridDataAll, 'this.targetNodeId')
                // this.selectedNodeId = this.gridDataAll[0].id
                // if (this.isShowTree == true) {
                this.$HTTP
                    .post('bom/get_parent_material', {
                        material_number: this.targetNodeId
                    })
                    .then((res) => {
                        console.log(res, '父节点>>>>21312312')
                        if (res.result.length == 0) {
                            this.$HTTP
                                .post('category/get_parent_category', {
                                    number: this.targetNodeId
                                })
                                .then((res) => {
                                    this.folderData = res.result
                                    let myArray = this.folderData.map((item) => ({
                                        ...item,
                                        children: this.gridDataAll
                                    }))
                                    console.log(myArray, '总共的父节22222点数据')
                                    this.treeData = this.convertToTree(myArray)
                                    if (this.folderData) {
                                        let userId = this.folderData[0].id
                                        this.queryFolderAll(userId)
                                    }
                                    // }
                                })
                        } else {
                            this.parentNodeAll(res.result[0].number, res.result)
                        }
                    })
                // }
            }
        )
    },
    methods: {
        backward(){
			console.log('222222222222',this.currentData.number);

			// this.$store.state.isShowOutTree=false
			// this.form.parent_number = '2504010161'
			// this.$store.state.listObj = {
			// 	isShow: true,
			// 	isShowTree: true,
			// 	items:  this.currentData,
			// 	table: false,
			// 	upload: false
			// }
            this.$HTTP
                    .post('bom/get_parent_material', {
                        material_number: this.currentData.number
                    })
                    .then((res) => {
                        console.log(res, '父节点1111111111111111111111111111111111111111111111111111111111111111111111111111')
                        if (res.result.length == 0) {
                            this.$HTTP
                                .post('category/get_parent_category', {
                                    number: this.currentData.number
                                })
                                .then((res) => {
                                    this.folderData = res.result
                                    let myArray = this.folderData.map((item) => ({
                                        ...item,
                                        children: this.gridDataAll
                                    }))
                                    console.log(myArray, '总共的父节22222点数据')
                                    this.treeData = this.convertToTree(myArray)
                                    if (this.folderData) {
                                        let userId = this.folderData[0].id
                                        this.queryFolderAll(userId)
                                    }
                                    // }
                                })
                        } else {
                            this.parentNodeAll(res.result[0].number, res.result)
                        }
                    })
			this.menuVisible = false
		},
        isSelectedNode(nodeId) {
      return nodeId === this.selectedNodeId;
    },
        closePage() {
            // this.currentData.number = 2
            console.log('222222222222');
            this.isShowTree = false
            // this.isShowTreeS = false
            this.$store.state.isShowOutTree = true
            // this.$store.state.isShowOutList = false
            this.form.parent_number = this.currentData.number
            // this.$store.state.listObj = {
			// 	isShow: true,
			// 	items:  this.currentData,
			// 	table: false,
			// 	upload: false
			// } 
            // this.isShowOutTree=true
        },
        // 查询文件夹
        queryFolder(data) {},
        //如果有多个文件夹反查
        queryFolderAll(data) {
            this.$HTTP
                .post('category/get_parent_category', {
                    id: data
                })
                .then((res) => {
                    // if (res.result[0] ) {
                    //     console.log(res.result, '多个文件夹反查')
                    //     this.folderData = res.result
                    //     console.log(this.treeData, 'this.treeData')
                    //     let myArray = res.result.map((item) => ({
                    //         ...item,
                    //         children: this.treeData
                    //     }))
                    //     this.treeData = this.convertToTree(myArray)
                    // } else {
                    this.folderData = res.result
                    console.log(this.treeData, 'this.treeData')
                    let myArray = res.result.map((item) => ({
                        ...item,
                        children: this.treeData
                    }))
                    if (this.folderData) {
                        let userId = this.folderData[0].id
                        this.queryFolderAll(userId)
                    }
                    this.treeData = this.convertToTree(myArray)
                    // }
                })
        },
        // 反查
        parentNodeAll(data, array) {
            this.$HTTP
                .post('bom/get_parent_material', {
                    material_number: data
                })
                .then((res) => {
                    console.log(res, '总共的父节列表点数据')
                    if (res.result.length == 0) {
                        //console.log(res, '总共的父节点数据')
                        let newArray = array.map((item) => ({
                            ...item,
                            children: this.gridDataAll // 为children字段分配一个新的数字值
                        }))
                        this.$HTTP
                            .post('category/get_parent_category', {
                                number: this.targetNodeId
                            })
                            .then((res) => {
                                this.folderData = res.result
                                let myArray = this.folderData.map((item) => ({
                                    ...item,
                                    children: newArray
                                }))
                                console.log(myArray, '总共的父节22222点数据')
                                this.treeData = this.convertToTree(myArray)
                            })
                        // this.treeData = this.convertToTree(newArray)
                    } else {
                        let newArray = res.result.map((item) => ({
                            ...item,
                            children: array.map((item) => ({
                                ...item,
                                children: this.gridDataAll
                            })) // 为children字段分配一个新的数字值
                        }))
                        console.log(newArray, 'newArray为什么到这里没有数据了')
                        this.$HTTP
                            .post('bom/get_parent_material', {
                                material_number: res.result[0].number
                            })
                            .then((res) => {
                                console.log(res, '总共的父节点数据')
                                if (res.result.length == 0) {
                                    this.$HTTP
                                        .post('category/get_parent_category', {
                                            number: this.targetNodeId
                                        })
                                        .then((res) => {
                                            this.folderData = res.result
                                            let myArray = this.folderData.map((item) => ({
                                                ...item,
                                                children: newArray
                                            }))
                                            console.log(myArray, 'newArray为什么到这里没有数据了')
                                            this.treeData = this.convertToTree(myArray)
                                        })
                                    // let myArray = arrayNew.map((item) => ({
                                    //     ...item,
                                    //     children: newArray
                                    // }))
                                    // console.log(myArray, '总共的父节22222点数据')
                                    // this.treeData = this.convertToTree(myArray)
                                } else {
                                    let newArrayAll = res.result.map((item) => ({
                                        ...item,
                                        children: newArray
                                    }))
                                    this.treeData = this.convertToTree(newArrayAll)
                                }
                            })
                    }
                })
        },
        convertToTree(data) {
            return data.map((item) => ({
                ...item,
                label: item.title, // 使用title作为显示的标签
                children: item.children
                    ? item.children.map((child) => ({
                          ...child,
                          label: child.title, // 使用title作为显示的标签
                          // 如果child还有children，则递归转换
                          children: child.children
                              ? child.children.map((grandchild) => ({
                                    ...grandchild,
                                    label: grandchild.title, // 使用title作为显示的标签
                                    // 如果grandchild是叶子节点，则不再递归
                                    children: grandchild.leaf ? undefined : grandchild.children
                                }))
                              : undefined
                      }))
                    : undefined
            }))
        },
        	// 树形下拉数据
		treeList(url, page, op) {  
			this.pictLoading = true
			this.$HTTP.post(url, page).then((res) => {
				let resItem = res.result
				resItem.map((item) => {
					if (item.son == 1) {
						item.leaf = false
					} else {
						item.leaf = true
					}
				})
				this.pictLoading = false
				this.groupData = resItem
			})
		},
        // drawer(op){
        //     console.log(op,'opasda');
		// 	// if(!op){
		// 	// 	this.menuVisibletop = op;
		// 	// 	this.$store.state.menuVisibletop = false
		// 	// }
		// },
        // 鼠标右击事件
		rightClick(event, object, Node, element) {
			this.editForm.node = object;//鼠标点击的树节点
			this.editFormNode = Node;//鼠标点击的树节点
			// console.log(event, object, Node, element, '1231231231232132131232131')
			object.ctype != 'product' ? (this.post_page = { type: 0 }) : (this.post_page = { include: '1,2,3' })
			if(object.ctype==='category'){
				this.subdata = false 
				this.bomp = false
				// console.log('文件夹')
			}else{
				// console.log('不是文件夹')
				this.subdata = true
				if(this.bomtype){
					if(this.bomtype!=0){
						this.bomp = true
					}else {
						this.bomp = false
					}
				}else{
					this.bomp = false
				}
			}
			
			this.menuVisibletop = false
			this.menuVisible = true
			// 节点数据
			this.currentData = object
			this.$store.state.cost_number = object.number
			// console.log(event.clientX,event.clientY,event,'event.clientX,event.clientY');
			// 获取浏览器窗口的大小
			const windowHeight = window.innerHeight;
    		const windowWidth = window.innerWidth;
			// 判断鼠标点击的位置是否接近窗口底部
			const isNearBottom = event.clientY > windowHeight - 320; // 这里的100是可调整的阈值
			if (isNearBottom) {
				// console.log('鼠标点击的位置接近窗口底部');
				// 将菜单显示在鼠标点击旁边定位
				this.menu_left = event.clientX + 50
				this.menu_top = event.clientY -320
			// 在这里显示你的弹窗，并将它定位到靠近窗口底部的位置
			} else {
				// console.log('鼠标点击的位置不是接近窗口底部');
				// 在这里显示你的弹窗，并将它定位到其他合适的位置将菜单显示在鼠标点击旁边定位
				this.menu_left = event.clientX + 50
				this.menu_top = event.clientY - 0
			}

			document.addEventListener('click', this.foo)
		},
        //树点击事件
        	groupClick(data) {
			console.log(data,'data选中的数据')
			this.selectedNodeId = data.id; // 设置当前点击节点为选中节点
			// this.treeArray = data
			if (data.ctype == 'product') {
				if (data.son) {
					console.log('22222222233333333333333333333');
					this.form.parent_number = data.number
					this.$store.state.listObjTree = {
						isShow: true,
						items: data,
						table: false,
						upload: false
					}
					sessionStorage.setItem(`parent_material_number_${sessionStorage.getItem('windowId')}`, data.number)
					sessionStorage.setItem(`material_id_${sessionStorage.getItem('windowId')}`, data.id)
					let myArray = [
						{
							id: data.id,
							title: data.title,
							number: data.number
						}
					]
					sessionStorage.setItem(`arraylist_${sessionStorage.getItem('windowId')}`, JSON.stringify(myArray))
					// console.log(data,'1111');
					if (data.type == '3') {
						this.$store.state.curIndex = 4
					} else if(data.type == '2'){
						this.$store.state.curIndex = 2
					} else{
						this.$store.state.curIndex = 1
						this.$store.state.menuVisibletop = false
					}
					// this.treeList('material/get_category_product_all',{category_id:data.id,material_number:data.number},true)
				} else {
					console.log('344444444444444444444444');
					// console.log(data,'222');
					this.form.parent_number = data.number
					sessionStorage.setItem(`parent_material_number_${sessionStorage.getItem('windowId')}`, data.number)
					sessionStorage.setItem(`material_id_${sessionStorage.getItem('windowId')}`, data.id)
					this.$store.state.listObjTree = {
						isShow: true,
						items: data,
						table: false,
						upload: false
					}
					sessionStorage.setItem(`parent_material_number_${sessionStorage.getItem('windowId')}`, data.number)
					sessionStorage.setItem(`material_id_${sessionStorage.getItem('windowId')}`, data.id)
					let myArray = [
						{
							id: data.id,
							title: data.title,
							number: data.number
						}
					]
					sessionStorage.setItem(`arraylist_${sessionStorage.getItem('windowId')}`, JSON.stringify(myArray))
					if (data.type == '3') {
						this.$store.state.curIndex = 4
					} else if(data.type == '2'){
						this.$store.state.curIndex = 2
					} else{
						this.$store.state.curIndex = 1
						this.$store.state.menuVisibletop = false
					}
				}
			} else if (data.type == '0' || data.type == '1' || data.type == '2' || data.type == '3') {
				this.form.parent_number = data.number
				this.$store.state.listObjTree = {
					isShow: true,
					items: data,
					table: false,
					upload: false
				}
				sessionStorage.setItem(`parent_material_number_${sessionStorage.getItem('windowId')}`, data.number)
				sessionStorage.setItem(`material_id_${sessionStorage.getItem('windowId')}`, data.id)
				let myArray = [
					{
						id: data.id,
						title: data.title,
						number: data.number
					}
				]
				sessionStorage.setItem(`arraylist_${sessionStorage.getItem('windowId')}`, JSON.stringify(myArray))
				if (data.type == '3') {
					this.$store.state.curIndex = 4
				} else if(data.type == '2'){
					this.$store.state.curIndex = 2
				} else{
					this.$store.state.curIndex = 1
					this.$store.state.menuVisibletop = false
				}
			}
			this.menuVisible = false
		},
        foo() {
			this.menuVisible = false
			document.removeEventListener('click', this.foo)
		},
        // groupClick(data) {     
        //     // this.isShowTreeS=true
        //     // this.$store.state.listObj = {
		// 	// 	isShow: false,
		// 	// 	isShowTree: true,
		// 	// 	items:  this.currentData,
		// 	// 	table: false,
		// 	// 	upload: false
		// 	// }   
        //     console.log(data, this.isShowTree,'data选中的数据')        
        //     if (data.ctype == 'product') {
        //         if (data.son) {
        //             // this.form.parent_number = data.number
        //             console.log('22222222233333333333333333333')
        //             this.$store.state.arrayTress = {
        //                 isTreeS: true,
        //                 items: data.number,
        //             }
        //             console.log('344444444444444444444444',this.$store.state.arrayTress)
        //             // sessionStorage.setItem(`parent_material_number_${sessionStorage.getItem('windowId')}`, data.number)
        //             // sessionStorage.setItem(`material_id_${sessionStorage.getItem('windowId')}`, data.id)
        //             // let myArray = [
        //             //     {
        //             //         id: data.id,
        //             //         title: data.title,
        //             //         number: data.number
        //             //     }
        //             // ]
        //             // sessionStorage.setItem(`arraylist_${sessionStorage.getItem('windowId')}`, JSON.stringify(myArray))
        //             // console.log(data,'1111');
        //             if (data.type == '3') {
        //                 this.$store.state.curIndex = 4
        //             } else if (data.type == '2') {
        //                 this.$store.state.curIndex = 2
        //             } else {
        //                 this.$store.state.curIndex = 1
        //                 this.$store.state.menuVisibletop = false
        //             }
        //             this.treeList('material/get_category_product_all',{category_id:data.id,material_number:data.number},true)
        //         } else {
        //             // console.log(data,'222');
        //             sessionStorage.setItem(`parent_material_number_${sessionStorage.getItem('windowId')}`, data.number)
        //             sessionStorage.setItem(`material_id_${sessionStorage.getItem('windowId')}`, data.id)
        //             this.$store.state.arrayTress = {
        //                 isTreeS: true,
        //                 items: data.number,
        //             }
        //             console.log('344444444444444444444444',this.$store.state.arrayTress)
        //             // sessionStorage.setItem(`parent_material_number_${sessionStorage.getItem('windowId')}`, data.number)
        //             // sessionStorage.setItem(`material_id_${sessionStorage.getItem('windowId')}`, data.id)
        //             // let myArray = [
        //             //     {
        //             //         id: data.id,
        //             //         title: data.title,
        //             //         number: data.number
        //             //     }
        //             // ]
        //             // sessionStorage.setItem(`arraylist_${sessionStorage.getItem('windowId')}`, JSON.stringify(myArray))
        //             if (data.type == '3') {
        //                 this.$store.state.curIndex = 4
        //             } else if (data.type == '2') {
        //                 this.$store.state.curIndex = 2
        //             } else {
        //                 this.$store.state.curIndex = 1
        //                 this.$store.state.menuVisibletop = false
        //             }
        //         }
        //     } else if (data.type == '0' || data.type == '1' || data.type == '2' || data.type == '3') {
        //         this.$store.state.arrayTress = {
        //             isTreeS: true,
        //             items: data.number,
        //         }
        //         console.log('344444444444444444444444',this.$store.state.arrayTress)
        //         // sessionStorage.setItem(`parent_material_number_${sessionStorage.getItem('windowId')}`, data.number)
        //         // sessionStorage.setItem(`material_id_${sessionStorage.getItem('windowId')}`, data.id)
        //         // let myArray = [
        //         //     {
        //         //         id: data.id,
        //         //         title: data.title,
        //         //         number: data.number
        //         //     }
        //         // ]
        //         // sessionStorage.setItem(`arraylist_${sessionStorage.getItem('windowId')}`, JSON.stringify(myArray))
        //         if (data.type == '3') {
        //             this.$store.state.curIndex = 4
        //         } else if (data.type == '2') {
        //             this.$store.state.curIndex = 2
        //         } else {
        //             this.$store.state.curIndex = 1
        //             this.$store.state.menuVisibletop = false
        //         }
        //     }
        //     this.menuVisible = false
        // }
    }
}
</script>
<style lang="scss" scoped>
.custom-highlight {
    background-color: #409eff; /* 自定义选中节点的背景色 */
    width:20%
}
:deep {
    .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
        background-color: #81caff !important;
        width:100%
    }
}
.ShowTree {
    padding:10px;
    background-color: #ffffff;
    width:500px;
    .butonn {
        position: absolute;
        // float:right;
        // top: 10;
        // right: 0;
        margin-left: 200px;
        /* 其他样式... */
    }
}
.no-inherit {
    vertical-align: bottom;
    margin-right: 10px;
}
.custom-tree-node {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 24px;
    height: 100%;
}

.custom-tree-node .do {
    margin-left: 20px;
    display: none;
}

.custom-tree-node .do i {
    margin-left: 5px;
    color: #999;
}

.custom-tree-node .do i:hover {
    color: #333;
}

.custom-tree-node:hover .do {
    display: inline-block;
}

.text_or {
    color: #67c23a;
}
.box-menu {
	width: 100px;
	position: absolute;
	z-index: 1000;
	background-color: #fff;
	box-shadow: 0px 0px 10px #ccc, 0px 0px 20px #ccc, 0px 0px 30px #ccc;
	padding: 10px;

	div {
		cursor: pointer;
		line-height: 30px;
	}
}
// .text_over{
//     display: inline-block;
//     // overflow: hidden;
//     text-overflow: ellipsis;
//     white-space: nowrap;
//     width: 220px;
// }
</style>