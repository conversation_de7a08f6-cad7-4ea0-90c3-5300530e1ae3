<template>
    <el-container>
        <el-header>
            <el-page-header @back="goBack" content="工艺卡未发布版本">
            </el-page-header>
        </el-header>
        <el-main class="nopadding" v-loading="pictLoading">
            <el-table :data="tableData" stripe style="width: 100%">
                <template #empty>
                    <el-empty description="暂无数据" :image-size="200"></el-empty>
                </template>
                <el-table-column prop="id" label="ID" width="80" />
                <el-table-column prop="material_number" label="代号" min-width="180" />
                <el-table-column prop="material_title" label="名称" min-width="120">
                    <template #default="scope">
                        <span style="color:#4496ee;cursor:pointer;" @click="getDetil(scope.row)">{{
                            scope.row.material_title }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="user_name" label="更新人" min-width="120" />
                <el-table-column prop="updated_at" label="更新时间" min-width="160" />
            </el-table>
            <el-pagination v-model:current-page="pageSet.page" v-model:page-size="pageSet.perpage"
                :page-sizes="[10, 20, 30, 40]" :total="pageSet.total" layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </el-main>
    </el-container>
    <el-drawer v-model="drawer" :title="DetileTile" :with-header="true" size="50%">
        <div style="padding: 0 20px;">
            <el-table :data="tableDetailData" stripe style="width: 100%">
                <template #empty>
                    <el-empty description="暂无数据" :image-size="200"></el-empty>
                </template>
                <el-table-column prop="material_number" label="编码" min-width="120" />
                <el-table-column prop="process_title" label="工序名称" min-width="120">
                </el-table-column>
                <el-table-column prop="process_content" label="工序内容" min-width="150" />
                <el-table-column prop="tool_code" label="机床代号" min-width="130" />
                <el-table-column prop="tool_title" label="机床名称" min-width="100" />
                <el-table-column prop="unit_hour" label="单件工时" min-width="100" />
            </el-table>
        </div>
    </el-drawer>
</template>

<script setup>
import { useRouter,useRoute } from 'vue-router';
import http from '@/utils/request';
import { onMounted, ref } from 'vue';
let router=useRouter()
const pageSet = ref({
    perpage: 10,
    page: 1,
    total: 0
});
const route = useRoute();
let tableData = ref([]);
let drawer = ref(false)
let DetileTile = ref('详情')
const pictLoading = ref(false);
let tableDetailData=ref([])
// 返回上一页
function goBack() {
    router.push({ path: "/index" });
}
if(route.params.op){
    pageSet.value.material_number=route.params.op
}
function getTableData() {
    pictLoading.value = true;
    http.get('datav/get_craftlog_ls',pageSet.value).then(res => {
        if(res.errcode === 0){
            tableData.value = res.result.data;
            pageSet.value.total = res.result.total;
            pictLoading.value = false;
        }else{
            pictLoading.value = false;
        }
        console.log(res,8855);
    }).catch((err)=>{
        pictLoading.value = false;
    })
}
// 处理每页条数变化
function handleSizeChange(val) {
    pageSet.value.perpage = val;
    getPageList();
}

// 处理页码变化
function handleCurrentChange(val) {
    pageSet.value.page = val;
    getPageList();
}

//详情抽屉
function getDetil(op) {
    drawer.value = true
    tableDetailData.value=JSON.parse(op.crafts)
    console.log(JSON.parse(op.crafts), 9999);

}
onMounted(() => {
    getTableData()
})
</script>