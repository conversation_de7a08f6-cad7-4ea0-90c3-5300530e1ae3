<template>
    <el-container>
        <el-header>
            <div class="left-panel">
                <el-button type="primary" icon="el-icon-plus" @click="add">添加</el-button>
            </div>
            <div class="right-panel">
                <div class="right-panel-search">
                    <el-input v-model="query.keyword" placeholder="模糊查询" clearable></el-input>
                    <el-button type="primary" icon="el-icon-search" @click="Query_button"></el-button>
                </div>
            </div>
        </el-header>
        <el-main class="nopadding" v-loading="loading">
            <div class="table-container">
                <el-table :data="tableData" border style="width: 100%" max-height="760" table-layout="fixed">
                    <el-table-column prop="processCode" label="工艺代号" width="150" />
                    <el-table-column prop="name" label="设备名称" width="180" />
                    <el-table-column prop="type" label="设备类型" width="150" />
                    <el-table-column prop="notes" label="设备说明" width="200" />
                    <el-table-column prop="hourlyRate" label="综合小时费率" width="150" />
                    <el-table-column prop="costCenter" label="成本中心" width="150" />
                    <el-table-column prop="outsourcingOrNot" label="是否外协" width="150">
                        <template #default="scope">
                            {{ scope.row.outsourcingOrNot ? '是' : '否' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="rateOfMinutes" label="人工分钟费率" width="150" />
                    <el-table-column fixed="right" label="操作" min-width="120">
                        <template #default="scope">
                            <el-button link type="primary" size="small" @click="handleClick(scope.row)">
                                修改
                            </el-button>
                            <el-button link type="primary" size="small" @click="delItem(scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div style="margin-top: 10px;">
                <el-pagination v-model:current-page="query.page" v-model:page-size="query.pageSize"
                    :page-sizes="[20, 50, 100]" :size="size" :disabled="disabled" :background="background"
                    layout="sizes, prev, pager, next" :total="query.total" @size-change="handleSizeChange"
                    @current-change="handleCurrentChange" />
            </div>
        </el-main>
        <addForm ref="addFromRef"></addForm>
    </el-container>
</template>
<script setup name="laborRate">
import addForm from './components/addForm.vue';
import { ref } from 'vue';
let addFromRef = ref()
let loading = ref(false)
let tableData = ref([
    {
        processCode: "1",
        name: "1",
        type: "1",
        notes: "1",
        hourlyRate: 10,
        costCenter: 30,
        outsourcingOrNot: false,
        rateOfMinutes: 20
    },
    {
        processCode: "2",
        name: "3",
        type: "4",
        notes: "5",
        hourlyRate: 20,
        costCenter: 40,
        outsourcingOrNot: true,
        rateOfMinutes: 50
    },
])
let query = ref({
    page:1,
    pageSize:20,
    total:100,
    keyword: null
})
function add() {
    addFromRef.value.open("新建人工费率管理")
}
function handleClick(e) {
    addFromRef.value.editItem("修改人工费率管理", e)
}
function delItem(e) {
    console.log(e)
}
function Query_button() {
    console.log(222)
}
const handleSizeChange = (val) => {
    query.value.pageSize=val
}
const handleCurrentChange = (val) => {
    query.value.page=val
}
</script>
<style lang="scss" scoped>
.el-main.nopadding {
    padding: 0;
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
}

// .table-container {
    // flex: 1;
    // max-height: calc(100vh - 200px);
    /* 根据实际布局调整 */
    // overflow: auto;
// }
</style>