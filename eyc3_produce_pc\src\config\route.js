
// 静态路由配置
// 书写格式与动态路由格式一致，全部经由框架统一转换
// 比较动态路由在meta中多加入了role角色权限，为数组类型。一个菜单是否有权限显示，取决于它以及后代菜单是否有权限。
// routes 显示在左侧菜单中的路由(显示顺序在动态路由之前)

const routes = [
	// {
	// 	name: "index",
	// 	path: "/index",
	// 	component: "index/index",
	// 	meta: {
	// 		icon: "el-icon-Cpu",
	// 		title: "首页",
	// 	}
	// },
	// {
	// 	name: "product",
	// 	path: "/product",
	// 	meta: {
	// 		icon: "el-icon-Cpu",
	// 		title: "产品",
	// 	},
	// 	children: [
	// 		{
	// 			path: "/product/index",
	// 			name: "productIndex",
	// 			component: "product/index",
	// 			meta: {
	// 				icon: "el-icon-Edit",
	// 				title: "产品库",
	// 				primission: 1,
	// 			},
	// 			children: [
	// 				{
	// 					name: "Search",
	// 					path: "/search",
	// 					component: "search/list",
	// 					meta: {
	// 						hidden: true,
	// 						icon: "el-icon-search",
	// 						title: "搜索",
	// 					},
						
	// 				},
	// 				{
	// 					name: "Searchss",
	// 					path: "/searchss",
	// 					component: "searchss/list",
	// 					meta: {
	// 						hidden: true,
	// 						icon: "el-icon-download",
	// 						title: "打印",
	// 					},
	// 					children: [
	// 					]
	// 				},
	// 				{
	// 					name: "cost",
	// 					path: "/cost",
	// 					component: "cost/list",
	// 					meta: {
	// 						hidden: true,
	// 						icon: "el-icon-document",
	// 						title: "成本",
	// 					},
	// 					children: [
	// 					]
	// 				},
	// 			]
	// 		},
	// 	]
	// },
	// {
	// 	name: "Information",
	// 	path: "/information",
	// 	component: "information/list",
	// 	meta: {
	// 		icon: "el-icon-setting",
	// 		title: "基础信息",
	// 	},
	// 	children: [
	// 		{
	// 			name: "InformationUnit",
	// 			path: "/information/unit",
	// 			component: "information/unit/list",
	// 			meta: {
	// 				// icon: "el-icon-setting",
	// 				title: "单位",
	// 			},
	// 		},
	// 		{
	// 			name: "InformationUnitAdd",
	// 			path: "/information/unit/add",
	// 			component: "information/unit/crud/add",
	// 			meta: {
	// 				hidden: true,
	// 				// icon: "el-icon-setting",
	// 				title: "新增单位",
	// 			},
	// 		},
	// 		{
	// 			name: "InformationUnitEdit",
	// 			path: "/information/unit/edit",
	// 			component: "information/unit/crud/edit",
	// 			meta: {
	// 				hidden: true,
	// 				// icon: "el-icon-setting",
	// 				title: "编辑单位",
	// 			},
	// 		},
	// 		{
	// 			name: "InformationClass",
	// 			path: "/information/class",
	// 			component: "information/class/list",
	// 			meta: {
	// 				// icon: "el-icon-setting",
	// 				title: "分类",
	// 			},
	// 		},
	// 		{
	// 			name: "InformationClassAdd",
	// 			path: "/information/class/add",
	// 			component: "information/class/crud/add",
	// 			meta: {
	// 				hidden: true,
	// 				// icon: "el-icon-setting",
	// 				title: "新增分类",
	// 			},
	// 		},
	// 		{
	// 			name: "InformationClassEdit",
	// 			path: "/information/class/edit",
	// 			component: "information/class/crud/edit",
	// 			meta: {
	// 				hidden: true,
	// 				// icon: "el-icon-setting",
	// 				title: "编辑分类",
	// 			},
	// 		},
	// 		{
	// 			name: "InformationProcess",
	// 			path: "/information/process",
	// 			component: "information/process/list",
	// 			meta: {
	// 				// icon: "el-icon-setting",
	// 				title: "工序",
	// 			},
	// 		},
	// 		{
	// 			name: "InformationProcessAdd",
	// 			path: "/information/process/add",
	// 			component: "information/process/crud/add",
	// 			meta: {
	// 				hidden: true,
	// 				// icon: "el-icon-setting",
	// 				title: "新增工序",
	// 			},
	// 		},
	// 		{
	// 			name: "InformationProcessEdit",
	// 			path: "/information/process/edit",
	// 			component: "information/process/crud/edit",
	// 			meta: {
	// 				hidden: true,
	// 				// icon: "el-icon-setting",
	// 				title: "编辑工序",
	// 			},
	// 		},
	// 		{
	// 			name: "InformationTool",
	// 			path: "/information/tool",
	// 			component: "information/tool/list",
	// 			meta: {
	// 				// icon: "el-icon-setting",
	// 				title: "机床",
	// 			},
	// 		},
	// 		{
	// 			name: "InformationToolAdd",
	// 			path: "/information/tool/add",
	// 			component: "information/tool/crud/add",
	// 			meta: {
	// 				hidden: true,
	// 				// icon: "el-icon-setting",
	// 				title: "新增机床",
	// 			},
	// 		},
	// 		{
	// 			name: "InformationToolEdit",
	// 			path: "/information/tool/edit",
	// 			component: "information/tool/crud/edit",
	// 			meta: {
	// 				hidden: true,
	// 				// icon: "el-icon-setting",
	// 				title: "编辑机床",
	// 			},
	// 		},
	// 		{
	// 			name: "InformationMaterial",
	// 			path: "/information/material",
	// 			component: "information/material/list",
	// 			meta: {
	// 				// icon: "el-icon-setting",
	// 				title: "材质",
	// 			},
	// 		},
	// 		{
	// 			name: "InformationMaterialAdd",
	// 			path: "/information/material/add",
	// 			component: "information/material/crud/add",
	// 			meta: {
	// 				hidden: true,
	// 				// icon: "el-icon-setting",
	// 				title: "新增材质",
	// 			},
	// 		},
	// 		{
	// 			name: "InformationMaterialEdit",
	// 			path: "/information/material/edit",
	// 			component: "information/material/crud/edit",
	// 			meta: {
	// 				hidden: true,
	// 				// icon: "el-icon-setting",
	// 				title: "编辑材质",
	// 			},
	// 		},
	// 		{
	// 			name: "InformationProcessContent",
	// 			path: "/information/processcontent",
	// 			component: "information/processcontent/list",
	// 			meta: {
	// 				// icon: "el-icon-setting",
	// 				title: "工序内容",
	// 			},
	// 		},
	// 		{
	// 			name: "InformationProcessContentAdd",
	// 			path: "/information/processcontent/add",
	// 			component: "information/processcontent/crud/add",
	// 			meta: {
	// 				hidden: true,
	// 				// icon: "el-icon-setting",
	// 				title: "新增工序内容",
	// 			},
	// 		},
	// 		{
	// 			name: "InformationProcessContentEdit",
	// 			path: "/information/processcontent/edit",
	// 			component: "information/processcontent/crud/edit",
	// 			meta: {
	// 				hidden: true,
	// 				// icon: "el-icon-setting",
	// 				title: "编辑工序内容",
	// 			},
	// 		},
	// 		{
	// 			name: "InformationParts",
	// 			path: "/information/parts",
	// 			component: "information/parts/list",
	// 			meta: {
	// 				// icon: "el-icon-setting",
	// 				title: "零件分类",
	// 			},
	// 		},
	// 		{
	// 			name: "InformationPartsAdd",
	// 			path: "/information/parts/add",
	// 			component: "information/parts/crud/add",
	// 			meta: {
	// 				hidden: true,
	// 				// icon: "el-icon-setting",
	// 				title: "新增零件分类",
	// 			},
	// 		},
	// 		{
	// 			name: "InformationPartsEdit",
	// 			path: "/information/parts/edit",
	// 			component: "information/parts/crud/edit",
	// 			meta: {
	// 				hidden: true,
	// 				// icon: "el-icon-setting",
	// 				title: "编辑零件分类",
	// 			},
	// 		},
	// 	]
	// },
	// // 审计日志
	// {
	// 	path: "/basic/audit",
	// 	name: "BasicAudit",
	// 	component: "basic/audit/list",
	// 	meta: {
	// 		icon: "el-icon-Tickets",
	// 		title: "审计日志",
	// 		primission: 13,
	// 	},
	// 	children: [
	// 		{
	// 			path: "/basic/audit/detail",
	// 			name: "BasicAuditDetail",
	// 			component: "basic/audit/crud/detail",
	// 			meta: {
	// 				hidden: true,
	// 				title: '审计日志详情',
	// 				primission: 13,
	// 			},
	// 		},
	// 	],
	// },
	// // 权限管理
	// {
	// 	path: "/basic/power",
	// 	name: "BasicPower",
	// 	component: "basic/power/list",
	// 	meta: {
	// 		icon: "el-icon-Key",
	// 		title: "权限管理",
	// 		primission: 9,
	// 	},
	// 	children: [
	// 		{
	// 			path: "/basic/power/add",
	// 			name: "BasicPowerAdd",
	// 			component: "basic/power/crud/add",
	// 			meta: {
	// 				hidden: true,
	// 				title: "新增权限管理",
	// 				primission: 9,
	// 			},
	// 		},
	// 		{
	// 			path: "/basic/power/edit",
	// 			name: "BasicPowerEdit",
	// 			component: "basic/power/crud/edit",
	// 			meta: {
	// 				hidden: true,
	// 				title: "编辑权限管理",
	// 				primission: 9,
	// 			},
	// 		},
	// 	],
	// },
	// {
	// 	path: "/power",
	// 	name: "Power",
	// 	component: "power/list",
	// 	meta: {
	// 		icon: "el-icon-Key",
	// 		title: "权限管理",
	// 		primission: 9,
	// 	},
	// 	children: [
	// 		{
	// 			path: "/power/add",
	// 			name: "PowerAdd",
	// 			component: "/power/crud/add",
	// 			meta: {
	// 				hidden: true,
	// 				title: "新增权限管理",
	// 				primission: 9,
	// 			},
	// 		},
	// 		{
	// 			path: "/power/edit",
	// 			name: "PowerEdit",
	// 			component: "/power/crud/edit",
	// 			meta: {
	// 				hidden: true,
	// 				title: "编辑权限管理",
	// 				primission: 9,
	// 			},
	// 		},
	// 	],
	// },
]

export default routes;
