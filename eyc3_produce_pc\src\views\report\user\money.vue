<!--
 * @author: 风源
 * @name: 类名
 * @desc: 介绍
 * @LastEditTime: 2022-12-10 21:00:00
 * @FilePath: \eyc3_pc_base\src\views\report\user\money.vue
-->
<template>
	<yp_list
		ref="table"
		:url="url"
		row-key="id"
		:columns="columns"
		stripe
		:add="add"
		:derive="derive"
		:formitems="formitems"
	>
	</yp_list>
</template>

<script>
export default {
	name: "ReportUserMoney",
	data() {
		return {
			url: "report/get_account_log_ls",
			columns: [
				{
					label: "姓名",
					prop: "name",
				},
				{
					label: "部门名称",
					prop: "department_name",
				},

				{
					label: "操作人",
					prop: "staff_name",
				},
				{
					label: "金额",
					prop: "money",
				},
				{
					label: "余额",
					prop: "balance",
				},
				{
					label: "类型",
					prop: "type",
					component: "fillvalue",
					options: [
						"固定金额订单",
						"自定义订单",
						"报餐订单",
						"点餐订单",
						"超市购物",
						"会议餐",
					],
				},
				{
					label: "变动类型",
					prop: "change_type",
					component: "fillvalue",
					options: ['管理员充值', '计划任务充值', '就餐消费', '退款', '扣款', '清空余额', '转账扣款', '转装收款'],
				},

				{
					label: "日期",
					prop: "updated_at",
				},
				{
					label: "备注",
					prop: "desc",
				},
				
			],
			derive: {
				filename:"资金记录"
			},
			formitems: [
				{
					label: "日期范围",
					name: "date_interval",
					value: [
						this.$TOOL.dateFormat(new Date(), "yyyy-MM-01"),
						this.$TOOL.dateFormat(new Date(), "yyyy-MM-dd"),
					],
					component: "date",
					options: {
						type: "daterange",
						rangeseparator: "至",
						startplaceholder: "开始日期",
						endplaceholder: "结束日期",
						valueFormat: "YYYY-MM-DD",
					},
					rules: [
						{
							required: true,
							message: "Please input Data",
							trigger: "change",
						},
					],
				},
				{
					label: "选择人员",
					name: "userlst",
					value: "",
					component: "selectUser"
				},
				{
					label: "变动类型",
					name: "type",
					value: "",
					component: "select",
					options: {
						items: [
							{
								label: "虚拟充值",
								value: 0,
							},
							{
								label: "扣款",
								value: 1,
							},
							{
								label: "转账",
								value: 2,
							},
						],
					},
				},
				
				{
					label: "离职人员",
					name: "isleave",
					value: false,
					component: "switch",
				},
				
			],
		};
	},
};
</script>

<style></style>
