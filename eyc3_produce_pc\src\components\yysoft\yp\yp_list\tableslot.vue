<!--
 * @author: 风源
 * @name: 类名
 * @desc: 介绍场
 * @LastEditTime: 2023-07-24 20:50:12
 * @FilePath: \eyc3_car_pc\src\components\yysoft\yp\yp_list\tableslot.vue
-->
<template>
    <slot :name="item.prop" v-bind="scope" v-if="item.component == 'fillvalue'">
        {{ item.options[scope.row[item.prop]] }}
    </slot>
    <slot :name="item.prop" v-bind="scope" v-if="item.component == 'image'">
        <template v-if="scope.row[item.prop]">
            <el-image style="width: 80px; height: 80px; border-radius: 5px"
                :src="scope.row[item.prop] + '?x-oss-process=image/resize,w_300/format,webp'" :zoom-rate="1.2"
                fit="cover" />
        </template>
        <template v-else>
            <div class="img-text">
                <template v-if="item.options">
                    <template v-if="item.options.textprop">
                        {{ scope.row[item.options.textprop].substring(0, 1) }}
                    </template>
                </template>
                <template v-else>暂无</template>
            </div>
        </template>
    </slot>
    <slot :name="item.prop" v-bind="scope" v-if="item.component == 'images'">
        <template v-if="scope.row[item.prop]">
            <template v-if="JSON.parse(scope.row[item.prop]).length >= 1">
                <template v-for="(item, index) in JSON.parse(scope.row[item.prop])" :key="index">
                    <el-image style="width: 80px; height: 80px; border-radius: 5px"
                        :src="item + '?x-oss-process=image/resize,w_300/format,webp'" :zoom-rate="1.2" fit="cover" />
                </template>
            </template>
            <template v-else>
                <div class="img-text">
                    <template v-if="item.options">
                        <template v-if="item.options.textprop">
                            {{ scope.row[item.options.textprop].substring(0, 1) }}
                        </template>
                    </template>
                    <template v-else>暂无</template>
                </div>
            </template>
        </template>
        <template v-else>
            <div class="img-text">
                <template v-if="item.options">
                    <template v-if="item.options.textprop">
                        {{ scope.row[item.options.textprop].substring(0, 1) }}
                    </template>
                </template>
                <template v-else>暂无</template>
            </div>
        </template>
    </slot>
    <slot :name="item.prop" v-bind="scope" v-if="item.component == 'richtext'">
        <div v-html="scope.row[item.prop]" class="richtext"></div>
    </slot>
    <slot :name="item.prop" v-bind="scope" v-if="item.component == 'link'">
        <span v-if="scope.row[item.prop]">
            <template v-for="(obj, index) in JSON.parse(scope.row[item.prop])" :key="index">
                <el-popover placement="bottom" trigger="hover" width="auto">
                    <template #reference>
                        <el-button style="margin-right: 16px">查看</el-button>
                    </template>
                    <el-table :data="JSON.parse(scope.row[item.prop])">
                        <el-table-column :prop="rs.prop" :label="rs.label" v-for="(rs, rs_index) in item.options"
                            :key="rs_index">
                            <a :href="obj[rs.prop]">{{ obj[rs.prop] }}</a>
                        </el-table-column>
                    </el-table>
                </el-popover>
            </template>
        </span>
    </slot>

    <slot :name="item.prop" v-bind="scope" v-if="item.component == 'tag'">
        <el-tag :type="item.options[scope.row[item.prop]].mode">{{ item.options[scope.row[item.prop]].label }}</el-tag>
    </slot>
    <!-- button -->
    <slot :name="item.prop" v-bind="scope" v-if="item.component == 'clear'">
        <el-button @click="onClearClick" type="warning" :loading="loading">{{ item.options.label }}</el-button>
    </slot>
    <slot :name="item.prop" v-bind="scope" v-if="item.component == 'button'">
        <span class="table-button" v-for="(rs, rs_index) in item.options" :key="rs_index">
            <template v-if="rs.component == 'lock'">
                <yy_button :type="rs.type" :label="rs.options.items[scope.row[rs.prop]].label" :component="rs.component"
                    :options="rs.options" :value="scope.row" plain size="small" @finish-event="button_finish_event"
                    @dialog="dialog"></yy_button>
            </template>
            <template v-else-if="rs.component == 'dialog'">
                <yy_dialog :options="rs" :form="scope.row" :size="rs.options.size" @refresh="button_finish_event">
                </yy_dialog>
            </template>
            <template v-else-if="rs.component == 'detail'">
                <yy_descriptions ref="yy_descriptions" :label="rs.label" :form="scope.row" :title="rs.options.title"
                    :remote="rs.options.remote" :items="rs.options.items" :column="rs.options.column"
                    :buttonList="rs.options.buttonList" :directionDesc="rs.options.directionDesc"
                    :haveslot="rs.options.haveslot" @finish-event="button_finish_event" @infoData="info_data">
                    <template #footer>
                        <slot name="descriptions-footer"></slot>
                    </template>
                </yy_descriptions>
            </template>
            <template v-else-if="rs.component == 'switchState'">
                <yy_button :type="rs.type" :label="rs.options.labelItems[scope.row[rs.prop]].label"
                    :component="rs.component" :queryform="queryform" :options="rs.options" :value="scope.row" plain
                    size="small" @finish-event="button_finish_event" @dialog="dialog"></yy_button>
            </template>
            <template v-else>
                <yy_button :type="rs.type" :label="rs.label" :component="rs.component" :queryform="queryform"
                    :options="rs.options" :value="scope.row" plain size="small" @finish-event="button_finish_event"
                    @dialog="dialog"></yy_button>
            </template>
        </span>
    </slot>
    <slot :name="item.prop" v-bind="scope" v-if="item.component == 'upload'">
        <yy_upload v-if="item.component == 'upload'" :label="item.options.label" :templateUrl="item.options.templateUrl"
            :url="item.options.url" :data="item.options.data" :value="scope.row"></yy_upload>
    </slot>
    <slot :name="item.prop" v-bind="scope" v-if="item.component == 'table'">
        <el-popover placement="bottom" trigger="hover" width="auto">
            <template #reference>
                <el-button style="margin-right: 16px">查看</el-button>
            </template>
            <el-table :data="JSON.parse(scope.row[item.prop])">
                <el-table-column :prop="rs.prop" :label="rs.label" v-for="(rs, rs_index) in item.options" :key="rs_index" />
            </el-table>
        </el-popover>
    </slot>
    <slot :name="item.prop" v-bind="scope" v-if="!item.component">
        {{ scope.row[item.prop] }}
    </slot>
    <slot :name="item.prop" v-bind="scope" v-if="item.component == 'array'">
        <div :data="JSON.parse(scope.row[item.prop])">
            <div :prop="rs.prop" :label="rs.label" v-for="(rs, rs_index) in item.options" :key="rs_index">
                <span v-for="(item, rs_index) in JSON.parse(scope.row[item.prop])" :key="rs_index">
                    {{ item[rs.prop] + ' ' }}
                </span>
            </div>
        </div>
    </slot>
    <slot :name="item.prop" v-bind="scope" v-if="item.component == 'arrayJson'">
        <span v-for="(el, rs_index) in JSON.parse(scope.row[item.prop])" :key="rs_index">
            {{ el[item.options.prop] + ' ' }}
        </span>
    </slot>
    <slot :name="item.prop" v-bind="scope" v-if="item.component == 'arrayItems'">
        <template v-for="(item, index) in JSON.parse(scope.row[item.prop])" :key="index">
            <span>{{ item + ' ' }}</span>
        </template>
    </slot>
    <slot :name="item.prop" v-bind="scope" v-if="item.component == 'timeTag'">
        <template v-if="new Date(scope.row[item.options.start_time]).getTime() < new Date().getTime() &&
            new Date(scope.row[item.options.end_time]).getTime() >= new Date().getTime()
            ">
            <el-tag :type="item.options.items[0].mode ? item.options.items[0].mode : ''">{{
                item.options.items[0].label
            }}</el-tag>
        </template>
        <template v-else-if="new Date(scope.row[item.options.start_time]).getTime() > new Date().getTime()">
            <el-tag :type="item.options.items[1].mode ? item.options.items[1].mode : ''">{{
                item.options.items[1].label
            }}</el-tag>
        </template>
        <template v-else>
            <el-tag :type="item.options.items[2].mode ? item.options.items[2].mode : ''">{{
                item.options.items[2].label
            }}</el-tag>
        </template>
    </slot>
    <slot :name="item.prop" v-bind="scope" v-if="item.component == 'userlst'">
        <div :data="JSON.parse(scope.row[item.prop])">
            <div :prop="rs.prop" :label="rs.label" v-for="(rs, rs_index) in item.options" :key="rs_index">
                <div v-for="(item, rs_index) in JSON.parse(scope.row[item.prop])" :key="rs_index">
                    <span v-for="(userlst, index) in item" :key="index" style="text-indent: 1em; display: inline-block">
                        {{ userlst[rs.prop] }}
                    </span>
                </div>
            </div>
        </div>
    </slot>
    <slot :name="item.prop" v-bind="scope" v-if="item.component == 'userlstStr'">
        <span v-for="(userlst, index) in JSON.parse(scope.row[item.prop]).users" :key="index">
            {{ userlst.name + ' ' }}
        </span>
    </slot>
    <slot :name="item.prop" v-bind="scope" v-if="item.component == 'objArr'">
        <template v-if="scope.row[item.prop]">
            <template v-for="(item, index) in JSON.parse(scope.row[item.prop])[item.options.props.onekey]" :key="index">
                <span style="margin-right: 3px">
                    {{ item[this.item.options.props.twokey] }}
                </span>
            </template>
        </template>
    </slot>
    <slot :name="item.prop" v-bind="scope" v-else-if="item.component == 'timeConcrete'">
        {{ scope.row[item.prop].split(' ')[1] }}
    </slot>
</template>

<script>
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import yy_descriptions from '@/components/yysoft/yy/yy_descriptions'

export default {
    emits: ['dialog', 'finish-event', 'finishEvent', 'info_data', 'infoData'],
    props: {
        item: { type: Object, default: () => { } },
        scope: { type: Object, default: () => { } },
        queryform: { type: Object, default: () => { } }
    },
    components: {
        yy_descriptions
    },
    data() {
        return {
            isSave: false,
            loading: false
        }
    },
    created() { },
    mounted() {
        // this.handlerButton()
    },
    methods: {
        button_finish_event() {
            this.$emit('finish-event', true)
        },
        info_data(infodata) {
            this.$emit('info-data', infodata)
        },
        //处理button显示label
        handlerButton() {
            // console.log(this.item)
            if (this.item.component == 'button') {
                this.item.options.forEach((el) => {
                    if (el.component == 'switchState') {
                        // console.log('------413-----')
                        // console.log(this.scope)
                    }
                })
            }
        },
        onClearClick() {
            this.loading = true
            ElMessageBox.confirm(
                `确定要清除${this.scope.row.name}帐户的余额吗?`,
                this.item.options.label ?? '操作预警',
                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }
            )
                .then((res) => {
                    if (res === 'confirm') {
                        this.onsubmit()
                    }
                })
                .catch((err) => {
                    this.loading = false
                })
        },
        onsubmit() {
            var postData = {}
            if (this.item.options.remote) {
                var rs = this.item.options.remote.data
                for (const key in rs) {
                    if (rs[key].substring && rs[key].substring(0, 1) == '$') {
                        postData[key] = this.value[rs[key].substring(1)]
                        if (postData[key] == '') return
                    } else {
                        postData[key] = rs[key]
                    }
                }
                postData.userlst = JSON.stringify({ departments: [], users: [{ userid: this.scope.row.userid }] })
                this.$HTTP.get(this.item.options.remote.api, postData).then((res) => {
                    if (res.errcode != 0) {
                        ElMessage.error(res.errmsg)
                    } else {
                        ElMessage.success('操作成功!')
                        this.$emit('finishEvent', true)
                    }
                    this.oncancel()
                })
            }
        },
        oncancel() {
            this.loading = false
        }
    }
}
</script>

<style scoped>
.table-button {
    display: inline-block;
    margin-right: 10px;
}

.img-text {
    height: 80px;
    width: 80px;
    background-color: #409eff;
    color: #fff;
    font-size: 30px;
    border-radius: 5px;
    line-height: 80px;
    text-align: center;
}

.richtext>>>img {
    width: 50px;
}
</style>
