<template>
    <el-main v-loading="pictLoading">
        <div class="btn">
            <el-button type="primary" @click="Synch_cloud" :disabled="isCloud">同步云星空</el-button>
        </div>
        <!-- <h2 class="H2">{{ this.option[0].title }}详情</h2> -->
        <div class="card-header">
            <div v-for="(item, i) in headerList" :key="i">
                {{ item.text }}：
                <span v-if="item.text !== '标签' && item.text !== '类型'" style="color:rgba(0, 0, 0, 0.6)">{{
                    objlist[item.key]
                        ? objlist[item.key]
                        : '暂无'
                }}</span>
                <span v-else-if="item.text !== '类型'" style="color:red;cursor:pointer;"
                    @click="JumpClick">{{ objlist[item.key] ? objlist[item.key] : '暂无' }}</span>
                <span v-else style="color: rgba(0, 0, 0, 0.6)">
                    {{
                        objlist[item.key] == 0 ? "产品" : objlist[item.key] == 1 ? "部件" : objlist[item.key] == 2 ? "零件" : objlist[item.key]==3?"原材料":"暂无"
                    }}
                </span>
            </div>
        </div>
    </el-main>
</template>
<script>
import fromTable from './components/fromtable.vue'
import { exportExcel } from './components/export'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import LuckyExcel from 'luckyexcel'

export default {
    name: 'craft',
    components: {
        fromTable
    },
    data() {
        return {
            pictLoading: false,
            headerList: [
                {
                    text: '编码',
                    key: 'number'
                },
                {
                    text: '名称',
                    key: 'title'
                },
                {
                    text: '类型',
                    key: 'type'
                },
                {
                    text: '图号',
                    key: 'drawing_number'
                },
                {
                    text: '规格',
                    key: 'specs'
                },
                {
                    text: '净重',
                    key: 'weight'
                },
                {
                    text: '单位',
                    key: 'unit_title'
                },
                {
                    text: '材质',
                    key: 'texture_title'
                },
                {
                    text: '计划价',
                    key: 'price'
                },
                {
                    text: '毛重',
                    key: 'quota'
                },
                {
                    text: '零件类型',
                    key: 'part_type_title'
                },
                {
                    text: '标签',
                    key: 'tags'
                },
                {
                    text: '库存数量',
                    key: 'stock'
                },
                {
                    text: '创建人',
                    key: 'user_name'
                },
                {
                    text: '创建时间',
                    key: 'created_at'
                },
                // {
                //     text: '废料率',
                //     key: 'scrap_rate'
                // },
                {
                    text: '废料率',
                    key: 'scrap_rate'
                },
                {
                    text: '生产车间',
                    key: 'produce_center'
                },
                {
                    text: '领用车间',
                    key: 'use_center'
                },
            ],
            objlist: {},
            option: ''
        }
    },
    props: {
        // 添加一个页面标识符属性，用于区分不同页面
        pageType: {
            type: String,
            default: 'default'
        }
    },
    created() {
        // 使用页面标识符来获取正确的数据
        const materialIdKey = `material_id_${this.pageType}_${sessionStorage.getItem('windowId')}`;

        this.$store.watch(
            (state) => state.listObj,
            (newValue) => {
                if (newValue) {
                    this.indata('material/get_info', { id: sessionStorage.getItem(materialIdKey) })
                }
            }
        )
        this.$store.watch(
            (state) => state.listObj?.table,
            (newValue) => {
                if (newValue !== undefined) {
                    this.indata('material/get_info', { id: sessionStorage.getItem(materialIdKey) })
                }
            }
        );
        this.indata('material/get_info', { id: sessionStorage.getItem(materialIdKey) })
    },
    methods: {
        JumpClick() {
            this.$router.push({ name: 'searchfors', params: this.objlist })
        },
        indata(url, page) {
            // console.log(url,page);
            this.pictLoading = true
            this.$HTTP.get(url, page).then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    // this.$store.state.listObj.table = false
                    // this.$store.state.listObj.upload = false
                    var a = res.result
                    if (a.tags) {
                        let arr = JSON.parse(a.tags)
                        let array = []
                        arr.forEach(item => {
                            array.push(item.title)
                        })
                        a.tags = array.join(',')
                    }
                    this.stockpost(a)
                }
            })
        },
        stockpost(a) {
            // 使用页面标识符来获取正确的数据
            const materialNumberKey = `parent_material_number_${this.pageType}_${sessionStorage.getItem('windowId')}`;
            this.$HTTP.get('material/get_stock', { number: sessionStorage.getItem(materialNumberKey) }).then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    let stock = { stock: res.result }
                    this.objlist = { ...a, ...stock }
                    if (this.objlist.sync_status === 1) {
                        this.isCloud = true
                    } else {
                        this.isCloud = false
                    }
                    // console.log(a,this.objlist);
                    this.pictLoading = false
                }
            })
        },
        Synch_cloud() {
            this.$message({
                message: '正在同步云星空,请等待接收工作通知',
                type: 'success'
            });
            // 使用页面标识符来获取正确的数据
            const materialIdKey = `material_id_${this.pageType}_${sessionStorage.getItem('windowId')}`;
            this.indata('material/get_info', { id: sessionStorage.getItem(materialIdKey) })
            // const loading = this.$loading({
            //     lock: true,
            //     text: '同步中',
            //     spinner: 'el-icon-loading',
            //     background: 'rgba(0, 0, 0, 0.7)'
            // })
            // 使用页面标识符来获取正确的数据
            const materialNumberKey = `parent_material_number_${this.pageType}_${sessionStorage.getItem('windowId')}`;
            let page = { material_number: sessionStorage.getItem(materialNumberKey) }
            this.$HTTP.get('material/post_sync_kingdee', page).then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    // this.$notify({
                    //     title: '云星空同步',
                    //     message: '操作成功 ',
                    //     type: 'success',
                    //     duration: 2000
                    // })
                    // this.messages.push(`同步成功${this.count}次`)
                    // this.count++
                    // loading.close()
                    this.objlist = res.result
                    this.pictLoading = false
                }
            })
        }
    }
}
</script>
<style>
.H2 {
    /* margin: 30px 106px 10px; */
    margin: 30px 0px 10px;
}

.nopadding h2 {
    /* margin: 30px 120px 10px; */
    margin: 30px 14px 10px;
}

.card-header {
    display: flex;
    margin: 30px 14px;
    flex-wrap: wrap;
    align-items: center;
    font-size: 14px;
}

.card-header div {
    width: 33.33%;
    height: 45px;
}

#luckysheet {
    margin: 0px;
    padding: 0px;
    position: absolute;
    width: 100%;
    left: 0px;
    top: 60px;
    bottom: 0px;
}

#uploadBtn {
    font-size: 16px;
}

#tip {
    position: absolute;
    z-index: 1000000;
    left: 0px;
    top: 0px;
    bottom: 0px;
    right: 0px;
    background: rgba(255, 255, 255, 0.8);
    text-align: center;
    font-size: 40px;
    align-items: center;
    justify-content: center;
    display: flex;
}

.btn {
    display: inline-block;
    float: right;
    /* border: 1px solid red;  */
    margin-bottom: 100px;
}
</style>
