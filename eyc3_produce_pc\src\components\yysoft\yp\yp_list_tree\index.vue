<template>
    <el-container :style="{ height: globalHeight }">
        <template v-if="isSide">
            <el-aside width="200px">
                <el-container>
                    <el-header>
                        <h2>分类</h2>
                    </el-header>
                    <el-main class="nopadding">
                        <el-menu
                            :default-active="defaultActive"
                            class="el-menu-vertical-demo"
                            @select="groupClick"
                        >
                            <template v-if="sideNavData">
                                <template
                                    v-for="(item, index) in sideNavData"
                                    :key="index"
                                >
                                    <el-menu-item :index="item[this.navValue]">
                                        <span>{{ item[this.navLabel] }}</span>
                                    </el-menu-item>
                                </template>
                            </template>
                            <template v-else>
                                <el-empty
                                    :image-size="80"
                                    :description="emptyNavText"
                                />
                            </template>
                        </el-menu>
                    </el-main>
                </el-container>
            </el-aside>
        </template>
        <el-container>
            <el-header>
                <div class="left-panel">
                    <template v-if="this.buttonList">
                        <div
                            v-for="(item, index) in buttonList"
                            :key="index"
                            class="button_left_div"
                        >
                            <yy_upload
                                v-if="item.component == 'upload'"
                                :label="item.label"
                                :templateUrl="item.templateUrl"
                                :url="item.url"
                                :maxSize="item.maxSize"
                            ></yy_upload>
                            <yy_button
                                v-else-if="item.component == 'form'"
                                :label="item.label"
                                :type="item.type"
                                :options="item.options"
                                :value="query"
                                :component="item.component"
                            ></yy_button>
                            <yy_button
                                v-else-if="item.component == 'confirm'"
                                :label="item.label"
                                :type="item.type"
                                :options="item.options"
                                :value="query"
                                :component="item.component"
                                @finishEvent="finishEvent"
                            ></yy_button>
                            <el-button
                                v-else-if="item.component == 'submit'"
                                type="primary"
                                @click="submitSeclect"
                            >
                                {{ item.label }}</el-button
                            >
                        </div>
                    </template>
                </div>
                <div class="right-panel">
                    <template v-if="this.queryItem">
                        <div class="right-panel-search">
                            <el-input
                                v-model="searchStr"
                                :placeholder="queryItem.placeholder ? queryItem.placeholder : '关键词'"
                                clearable
                            ></el-input>
                            <el-button
                                type="primary"
                                icon="el-icon-search"
                                @click="upsearch"
                            ></el-button>
                        </div>
                    </template>
                </div>
            </el-header>
            <el-main class="nopadding">
                <div
                    :style="{ height: '100%' }"
                    v-loading="loading"
                >
                    <div class="scTable-table">
                        <el-table
                            ref="multipleTable"
                            :data="tableData"
                            border
                            :style="{ height: '100%' }"
                            :key="toggleIndex"
                            @select="select"
                            @row-click="rowClick"
                            @selection-change="handleSelectionChange"
                            @select-all="selectAll"
                        >
                            <template
                                v-for="(item, index) in userColumn"
                                :key="index"
                            >
                                <template v-if="item.type == 'selection'">
                                    <el-table-column
                                        :label="item.label"
                                        type="selection"
                                        width="55"
                                    ></el-table-column>
                                </template>
                                <template v-else>
                                    <el-table-column
                                        v-if="!item.hide"
                                        :column-key="item.prop"
                                        :label="item.label"
                                        :prop="item.prop"
                                        :width="item.width"
                                        :sortable="item.sortable"
                                        :fixed="item.fixed"
                                        :filters="item.filters"
                                        :filter-method="remoteFilter || !item.filters ? null : filterHandler"
                                        :show-overflow-tooltip="item.showOverflowTooltip"
                                    >
                                        <template #default="scope">
                                            <tableslot
                                                :item="item"
                                                :scope="scope"
                                                :queryform="queryform"
                                                @finish-event="finishEvent"
                                            ></tableslot>
                                        </template>
                                    </el-table-column>
                                </template>
                            </template>
                            <template #empty>
                                <el-empty
                                    :description="emptyText"
                                    :image-size="100"
                                ></el-empty>
                            </template>
                        </el-table>
                    </div>
                    <div
                        class="scTable-page"
                        v-if="!hidePagination"
                    >
                        <div class="scTable-pagination">
                            <el-pagination
                                small
                                background
                                :current-page="pageData.page"
                                :page-size="pageData.per_page"
                                :page-sizes="[10, 20, 30, 40, 50]"
                                layout="total, sizes, prev, pager, next, jumper"
                                :total="total"
                                @current-change="paginationChange"
                                @update:page-size="pageSizeChange"
                            />
                        </div>
                        <div class="scTable-do">
                            <el-button
                                @click="refresh"
                                icon="el-icon-refresh"
                                circle
                                style="margin-left: 15px"
                            ></el-button>
                            <el-popover
                                v-if="userColumn"
                                placement="top"
                                title="列设置"
                                :width="500"
                                trigger="click"
                                :hide-after="0"
                                @show="customColumnShow = true"
                                @after-leave="customColumnShow = false"
                            >
                                <template #reference>
                                    <el-button
                                        icon="el-icon-set-up"
                                        circle
                                        style="margin-left: 15px"
                                    ></el-button>
                                </template>
                                <columnSetting
                                    v-if="customColumnShow"
                                    ref="columnSetting"
                                    @userChange="columnSettingChange"
                                    @save="columnSettingSave"
                                    @back="columnSettingBack"
                                    :column="userColumn"
                                ></columnSetting>
                            </el-popover>
                        </div>
                    </div>
                </div>
            </el-main>
        </el-container>
    </el-container>
</template>

<script>
import columnSetting from './columnSetting'
import tableslot from './tableslot'
export default {
    name: 'listTree',
    components: {
        columnSetting,
        tableslot
    },
    props: {
        url: { type: String, default: '' },
        globalHeight: { type: String, default: '100%' },
        postData: { type: Object, default: () => {} },
        columns: { type: Object, default: () => {} },
        sideNav: { type: Object, default: () => {} },
        queryItem: { type: Object, default: () => {} },
        buttonList: { type: Object, default: () => {} },
        hidePagination: { type: Boolean, default: false },
        paging: { type: Boolean, default: true },
        radio: { type: Boolean, default: false }
    },
    emits: ['mediaList'],
    data() {
        return {
            // 侧边栏搜索
            groupFilterText: '',
            // 搜索模块
            searchStr: '',
            searchData: {},
            // 列表配置项
            userColumn: [],
            tableData: [],
            // 侧边栏导航
            isSide: true,
            navLabel: 'label',
            navValue: 'value',
            defaultActive: -1,
            sideNavData: [],
            sideData: {},
            // 分页数据
            pageData: {
                per_page: 10,
                page: 1
            },
            // 分页器相关
            currentPage: 1,
            pageSize: 10,
            total: 0,
            // 配置列表按钮
            customColumnShow: false,
            // 遮罩层loading
            loading: false,
            emptyText: '暂无数据',
            emptyNavText: '暂无分组',
            // 多选
            //存放分页选中条目,回显用
            selectedData: [],
            //存放选中条目，做传参用
            templateRadio: [],
            //自定义列配置
            toggleIndex: 0
        }
    },
    computed: {
        _table_height() {
            return 'calc(100% - 50px)'
        }
    },
    watch: {
        groupFilterText(val) {
            this.groupFilterNode(val)
        }
    },
    mounted() {
        //判断是否开启自定义列
        this.userColumn = JSON.parse(JSON.stringify(this.columns))
        setTimeout(() => {
            this.getData()
            this.getSideNavData()
        }, 100)
    },
    methods: {
        // 获取列表数据
        getData() {
            this.loading = true
            this.$HTTP
                .post(this.url, { ...this.postData, ...this.pageData, ...this.searchData, ...this.sideData })
                .then((res) => {
                    if (res.errcode != 0) {
                        this.emptyText = res.errmsg
                    } else {
                        this.emptyText = '暂无数据'
                        let tableData = (this.paging ? res.result.data : res.result) || []
                        this.userColumn = JSON.parse(JSON.stringify(this.columns))
                        this.tableData = tableData
                        this.total = res.result.total
                        // console.log(this.tableData)
                        // console.log(this.userColumn)
                        this.userColumn.forEach((el) => {
                            if (el.component == 'mediaShow') {
                                this.tableData.forEach((item) => {
                                    item[el.options.mediaprop] = item[el.prop]
                                    var reg = RegExp(
                                        /^https?:\/\/(.+\/)+.+(\.(swf|avi|flv|mpg|rm|mov|wav|asf|3gp|mkv|rmvb|mp4))$/i
                                    )
                                    var reg1 = RegExp(
                                        /^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/)(([A-Za-z0-9-~]+)\.)+([A-Za-z0-9-~\/])+$/
                                    )
                                    var reg2 = RegExp(/(.*)\.(jpg|bmp|gif|ico|pcx|jpeg|tif|png|raw|tga|webp|JPG)$/)
                                    if (reg.exec(item[el.prop])) {
                                        item[el.options.mediaprop] =
                                            'http://files.qixuw.com/dingb9614df94342f570a1320dcb25e91351/visitor/2023-04-13/NmvHNlW5fM1ZPYUBHbCyQ7Emkat8jdoj.png'
                                    } else if (reg2.exec(item[el.prop])) {
                                        item[el.options.mediaprop] = item[el.options.mediaprop]
                                    } else {
                                        item[el.options.mediaprop] =
                                            'http://files.qixuw.com/dingb9614df94342f570a1320dcb25e91351/visitor/2023-04-13/ECmNbWarOtXBm27Fh9MX2rPY51WynGvK.png'
                                    }
                                    //     if (reg1.exec(item[el.prop])) {
                                    //     item[el.options.mediaprop] =
                                    //         'http://files.qixuw.com/dingb9614df94342f570a1320dcb25e91351/visitor/2023-04-13/ECmNbWarOtXBm27Fh9MX2rPY51WynGvK.png'
                                    // }
                                })
                            }
                        })
                    }
                })
                .finally(() => {
                    this.loading = false
                    this.modalLoading = false
                })
        },
        // 获取侧边栏数据
        getSideNavData() {
            if (this.sideNav) {
                if (this.sideNav.remote) {
                    let remote = this.sideNav.remote
                    this.navLabel = this.sideNav.remote.label
                    this.navValue = this.sideNav.remote.value
                    this.$HTTP
                        .post(remote.api, { ...remote.data })
                        .then((res) => {
                            if (res.errcode != 0) {
                                this.emptyNavText = res.errmsg
                            } else {
                                this.emptyNavText = '暂无分组'
                                this.sideNavData = res.result.data
                                if (this.sideNav.isall) {
                                    var allObj = {}
                                    allObj[this.navLabel] = '全部'
                                    allObj[this.navValue] = -1
                                    this.sideNavData.unshift(allObj)
                                }
                            }
                        })
                        .finally(() => {
                            // this.loading = false
                            // this.modalLoading = false
                        })
                } else {
                    this.sideNavData = this.sideNav.items
                    if (this.sideNav.isall) {
                        var allObj = {}
                        allObj[this.navLabel] = '全部'
                        allObj[this.navValue] = -1
                        this.sideNavData.unshift(allObj)
                    }
                }
            } else {
                this.isSide = false
            }
        },
        //树过滤
        groupFilterNode(value) {
            // if (!value) return true
            // return data.label.indexOf(value) !== -1
            // console.log('-----------------------')
            // console.log(value)
            // console.log(this.sideNavData)
        },
        //树点击事件
        groupClick(data) {
            console.log(data)
            if (data != -1) {
                this.sideData[this.sideNav.name] = data
            } else {
                this.sideData = {}
            }
            this.getData()
        },
        //搜索
        upsearch() {
            // console.log('搜索------')
            this.searchData[this.queryItem.name] = this.searchStr
            this.searchStr = ''
            this.getData()
        },
        // 多选列表项
        select(selection, row) {
            if (this.radio) {
                this.$refs.multipleTable.clearSelection()
                if (selection.length == 0) return
                this.$refs.multipleTable.toggleRowSelection(row, true)
            }
        },
        handleSelectionChange(rows) {
            if (!this.radio) {
                //将选中赋值到回显和传参数组
                this.templateRadio = rows
            } else {
                this.templateRadio = []
                var index = rows.length - 1
                this.templateRadio.push(rows[index])
            }
        },
        rowClick(row, column) {
            if (this.radio) {
                const selectData = this.templateRadio
                this.$refs.multipleTable.clearSelection()
                if (selectData.length == 1) {
                    selectData.forEach((item) => {
                        // 判断 如果当前的一行被勾选, 再次点击的时候就会取消选中
                        if (item == row) {
                            this.$refs.multipleTable.toggleRowSelection(row, false)
                        }
                        // 不然就让当前的一行勾选
                        else {
                            this.$refs.multipleTable.toggleRowSelection(row, true)
                        }
                    })
                } else {
                    this.$refs.multipleTable.toggleRowSelection(row, true)
                }
            }
        },
        selectAll(selection) {
            if (this.radio) {
                if (selection.length >= 1) {
                    this.$refs.multipleTable.clearSelection()
                }
            }
        },
        // 确认提交多选
        submitSeclect() {
            this.$emit('mediaList', this.templateRadio)
            if (!this.radio) {
                this.$refs.multipleTable.clearSelection()
            }
        },
        //自定义变化事件
        columnSettingChange(userColumn) {
            // console.log(userColumn)
            this.userColumn = userColumn
            this.toggleIndex += 1
        },
        //自定义列保存
        async columnSettingSave(userColumn) {
            this.$refs.columnSetting.isSave = true
            try {
                await config.columnSettingSave(this.tableName, userColumn)
            } catch (error) {
                this.$message.error('保存失败')
                this.$refs.columnSetting.isSave = false
            }
            this.$message.success('保存成功')
            this.$refs.columnSetting.isSave = false
        },
        //自定义列重置
        async columnSettingBack() {
            this.$refs.columnSetting.isSave = true
            try {
                const column = await config.columnSettingReset(this.tableName, this.columns)
                this.userColumn = column
                this.$refs.columnSetting.usercolumn = JSON.parse(JSON.stringify(this.columns || []))
            } catch (error) {
                this.$message.error('重置失败')
                this.$refs.columnSetting.isSave = false
            }
            this.$refs.columnSetting.isSave = false
        },
        // 刷新
        refresh() {
            this.getData()
        },
        // 分页器
        //分页点击
        paginationChange(size) {
            // console.log(this.query.page)
            this.pageData.page = size
            this.getData()
        },
        //条数变化
        pageSizeChange(size) {
            // console.log('---------------------')
            // console.log(size)
            this.pageData.per_page = size
            this.getData()
        },
        finishEvent(e) {
            console.log('finishEvent')
            this.getData()
        }
    }
}
</script>

<style scoped>
.scTable {
}
.scTable-table {
    height: calc(100% - 50px);
}
.scTable-page {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
}
.scTable-do {
    white-space: nowrap;
}
.scTable:deep(.el-table__footer) .cell {
    font-weight: bold;
}
.scTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-horizontal {
    height: 12px;
    border-radius: 12px;
}
.scTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-vertical {
    width: 12px;
    border-radius: 12px;
}
.button_lst {
    display: flex;
    justify-content: space-between;
}
.button_left {
    display: flex;
}
.button_left_div {
    margin-right: 15px;
}
</style>
