<template>
    <el-drawer v-model="drawer" :size="500" :title="savetitle" direction="rtl" destroy-on-close>
        <el-main style="padding: 0 30px" class="el-main">
            <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="auto" class="demo-ruleForm"
                :size="formSize" status-icon>
                <el-row>
                    <!-- <el-col :span="12">
                        <el-form-item label="实例标题:" prop="name">
                            <el-input v-model="ruleForm.name" />
                        </el-form-item>
                    </el-col> -->
                    <el-col :span="24">
                        <el-form-item label="物料编码:" prop="material_number">
                            <el-input v-model="ruleForm.material_number" placeholder="请选择物料" disabled>
                                <template #append>
                                    <el-button type="primary" @click="dialogFormVisible=true">选择物料</el-button>
                                </template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="物料名称:" prop="material_title">
                            <el-input v-model="ruleForm.material_title" disabled />
                        </el-form-item>
                    </el-col>
                    <!-- <el-col :span="12">
                        <el-form-item label="车间费率:" prop="hour_rate">
                            <el-input-number v-model="ruleForm.hour_rate" :precision="2" :step="0.1" :min="0" />
                        </el-form-item>
                    </el-col> -->
                </el-row>
            </el-form>
            <div style="margin-top: 75vh;">
                <el-button type="primary" :loading="isSaveing" @click="submit()">提 交</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-main>
        <el-dialog v-model="dialogFormVisible" title="选择物料" :width="'800px'">
            <div><el-input v-model="params.keyword" style="max-width: 300px" placeholder="" class="input-with-select">
                    <template #append>
                        <el-button type="primary" @click="getMaterialList()">查询</el-button>
                    </template>
                </el-input></div>
            <el-table :data="gridData" height="450">
                <template #empty>
                    <el-empty description="暂无数据" :image-size="100"></el-empty>
                </template>
                <el-table-column property="number" label="物料编码" />
                <el-table-column property="title" label="物料名称" />
                <el-table-column property="specs" label="规格" />
                <el-table-column property="texture_title" label="材质" />
                <el-table-column property="unit_title" label="单位" />
                <el-table-column fixed="right" label="操作" min-width="100">
                    <template #default="scope">
                        <el-button type="primary" size="small" @click="useThe(scope.row)" v-if="!scope.row.isChecked">
                            使用
                        </el-button>
                        <el-button size="small" @click="cancelUse(scope.row)" v-else>取消使用</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="dialogFormVisible = false">取消</el-button>
                    <el-button type="primary" @click="dialogDetermine">
                        确定
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </el-drawer>
</template>
<script setup>
import { ref, defineProps, defineEmits } from 'vue';
import http from '@/utils/request';
import { ElMessage } from 'element-plus';

const props = defineProps({
    getDataList: {
        type: Function,
        required: true
    }
});

const emit = defineEmits(['update:modelValue']);
let dialogFormVisible = ref(false)
const drawer = ref(false)
const savetitle = ref("")
let gridData = ref([])
const isSaveing = ref(false)
const isAdd = ref(true)
const ruleFormRef = ref()
let selectMaterial = ref({})
const params = ref({
    include: 3,
    keyword: ""
})
const ruleForm = ref({
    // name: "",
    material_number: "",
    material_title: "",
})
const rules = ref({
    // name: [
    //     {
    //         required: true,
    //         message: '请输入设备名称',
    //         trigger: 'blur',
    //     }
    // ],
    material_number: [
        {
            required: true,
            message: '请输入车间名称',
            trigger: 'change',
        }
    ],
    material_title: [
        {
            required: true,
            message: '请输入代码',
            trigger: 'blur',
        }
    ]
})

function open(titel) {
    savetitle.value = titel
    drawer.value = true
    isAdd.value = true
    ruleForm.value = {
        material_number: "",
        material_title: "",
    }
}
async function submit() {
    isSaveing.value = true
    await ruleFormRef.value.validate((valid, fields) => {
        if (valid) {
            if (isAdd.value) {
                http.post("verify/post_add", ruleForm.value).then(res => {
                    if (res.errcode != 0) {
                        ElMessage.error(res.errmsg)
                        isSaveing.value = false
                        drawer.value = false
                    } else {
                        ElMessage.success("添加成功")
                        isSaveing.value = false
                        drawer.value = false
                        props.getDataList()
                    }
                }).catch((err) => {
                    isSaveing.value = false
                    drawer.value = false
                })
            } else {
                http.post("verify/post_modify", ruleForm.value).then(res => {
                    if (res.errcode != 0) {
                        ElMessage.error(res.errmsg)
                        isSaveing.value = false
                        drawer.value = false
                    } else {
                        ElMessage.success("修改成功")
                        isSaveing.value = false
                        drawer.value = false
                        props.getDataList()
                    }
                }).catch((err) => {
                    isSaveing.value = false
                    drawer.value = false
                })
            }
        } else {
            isSaveing.value = false
        }
    })
}
//获取物料列表
function getMaterialList() {
    if (params.value.keyword == "") {
        ElMessage.warning("请输入物料编码")
        return
    }
    http.get("material/get_all", params.value).then(res => {
        gridData.value = res.result
        if (res.result.length == 0) {
            ElMessage.warning("未找到物料")
        } else {
            gridData.value.forEach(item => {
                item.isChecked = false
            })
            if (ruleForm.value.material_number) {
                gridData.value.forEach(item => {
                    if (item.number == ruleForm.value.material_number) {
                        item.isChecked = true
                    }
                })
            }
        }
    })
}
//选择物料
function editItem(titel, item) {
    savetitle.value = titel
    ruleForm.value = JSON.parse(JSON.stringify(item))
    isAdd.value = false
    drawer.value = true
}
function cancel() {
    drawer.value = false
}
//确定选择物料
function dialogDetermine() {
    dialogFormVisible.value = false
    ruleForm.value.material_number = selectMaterial.value.number || null
    ruleForm.value.material_title = selectMaterial.value.title || null
}
//使用物料
function useThe(item) {
    gridData.value.forEach(item => {
        item.isChecked = false
    })
    item.isChecked = true
    selectMaterial.value = item
}
//取消使用物料
function cancelUse(item) {
    item.isChecked = false
    selectMaterial.value = {}
}
defineExpose({
    open,
    editItem
})
</script>