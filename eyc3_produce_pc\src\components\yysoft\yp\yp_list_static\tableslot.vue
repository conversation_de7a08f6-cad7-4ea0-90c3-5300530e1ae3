<!--
 * @author: 风源
 * @name: 类名
 * @desc: 介绍
 * @LastEditTime: 2023-01-06 18:45:14
 * @FilePath: \eyc3_car_pc\src\components\yysoft\yp\yp_list\tableslot.vue
-->
<template>
    <slot
        :name="item.prop"
        v-bind="scope"
        v-if="item.component == 'fillvalue'"
    >
        {{ item.options[scope.row[item.prop]] }}
    </slot>
    <slot
        :name="item.prop"
        v-bind="scope"
        v-if="item.component == 'image'"
    >
        <template v-if="scope.row[item.prop]">
            <el-image
                style="width: 80px; height: 80px; border-radius: 5px"
                :src="scope.row[item.prop] + '?x-oss-process=image/resize,w_300/format,webp'"
                :zoom-rate="1.2"
                fit="cover"
            />
        </template>
        <template v-else>
            <div class="img-text">
                <template v-if="item.options.textprop">
                    {{ scope.row[item.options.textprop].substring(0, 1) }}
                </template>
            </div>
        </template>
    </slot>
    <slot
        :name="item.prop"
        v-bind="scope"
        v-if="item.component == 'tag'"
    >
        <el-tag :type="item.options[scope.row[item.prop]].mode">{{ item.options[scope.row[item.prop]].label }}</el-tag>
    </slot>
    <!-- button -->
    <slot
        :name="item.prop"
        v-bind="scope"
        v-if="item.component == 'button'"
    >
        <span
            class="table-button"
            v-for="(rs, rs_index) in item.options"
            :key="rs_index"
        >
            <template v-if="rs.component == 'lock'">
                <yy_button
                    :type="rs.type"
                    :label="rs.options.items[scope.row[rs.prop]].label"
                    :component="rs.component"
                    :options="rs.options"
                    :value="scope.row"
                    plain
                    size="small"
                    @finish-event="button_finish_event"
                    @dialog="dialog"
                ></yy_button>
            </template>
            <template v-else-if="rs.component == 'dialog'">
                <yy_dialog
                    :options="rs"
                    :form="scope.row"
                    :size="rs.options.size"
                    @refresh="button_finish_event"
                ></yy_dialog>
            </template>
            <template v-else>
                <yy_button
                    :type="rs.type"
                    :label="rs.label"
                    :component="rs.component"
                    :queryform="queryform"
                    :options="rs.options"
                    :value="scope.row"
                    plain
                    size="small"
                    @finish-event="button_finish_event"
                    @dialog="dialog"
                ></yy_button>
            </template>
        </span>
    </slot>
    <slot
        :name="item.prop"
        v-bind="scope"
        v-if="item.component == 'upload'"
    >
        <yy_upload
            v-if="item.component == 'upload'"
            :label="item.options.label"
            :templateUrl="item.options.templateUrl"
            :url="item.options.url"
            :data="item.options.data"
            :value="scope.row"
        ></yy_upload>
    </slot>
    <slot
        :name="item.prop"
        v-bind="scope"
        v-if="item.component == 'table'"
    >
        <el-popover
            placement="bottom"
            trigger="hover"
            width="auto"
        >
            <template #reference>
                <el-button style="margin-right: 16px">查看</el-button>
            </template>
            <el-table :data="JSON.parse(scope.row[item.prop])">
                <el-table-column
                    :prop="rs.prop"
                    :label="rs.label"
                    v-for="(rs, rs_index) in item.options"
                    :key="rs_index"
                />
            </el-table>
        </el-popover>
    </slot>
    <slot
        :name="item.prop"
        v-bind="scope"
        v-if="!item.component"
    >
        {{ scope.row[item.prop] }}
    </slot>
    <slot
        :name="item.prop"
        v-bind="scope"
        v-if="item.component == 'array'"
    >
        <div :data="JSON.parse(scope.row[item.prop])">
            <div
                :prop="rs.prop"
                :label="rs.label"
                v-for="(rs, rs_index) in item.options"
                :key="rs_index"
            >
                <span
                    v-for="(item, rs_index) in JSON.parse(scope.row[item.prop])"
                    :key="rs_index"
                >
                    {{ item[rs.prop] + ' ' }}
                </span>
            </div>
        </div>
    </slot>
    <slot
        :name="item.prop"
        v-bind="scope"
        v-if="item.component == 'userlst'"
    >
        <div :data="JSON.parse(scope.row[item.prop])">
            <div
                :prop="rs.prop"
                :label="rs.label"
                v-for="(rs, rs_index) in item.options"
                :key="rs_index"
            >
                <div
                    v-for="(item, rs_index) in JSON.parse(scope.row[item.prop])"
                    :key="rs_index"
                >
                    <span
                        v-for="(userlst, index) in item"
                        :key="index"
                        style="text-indent: 1em; display: inline-block"
                    >
                        {{ userlst[rs.prop] }}
                    </span>
                </div>
            </div>
        </div>
    </slot>
</template>

<script>
export default {
    emits: ['dialog', 'finish-event'],
    props: {
        item: { type: Object, default: () => {} },
        scope: { type: Object, default: () => {} },
        queryform: { type: Object, default: () => {} }
    },
    data() {
        return {
            isSave: false
        }
    },
    created() {},
    mounted() {},
    methods: {
        button_finish_event() {
            this.$emit('finish-event', true)
        }
    }
}
</script>

<style scoped>
.table-button {
    display: inline-block;
    margin-right: 10px;
}
.img-text {
    height: 80px;
    width: 80px;
    background-color: #409eff;
    color: #fff;
    font-size: 30px;
    border-radius: 5px;
    line-height: 80px;
    text-align: center;
}
</style>
