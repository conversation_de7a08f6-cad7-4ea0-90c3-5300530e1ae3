<template>
    <el-main v-loading="pictLoading">
        <h2 class="H2">工艺卡</h2>
        <div class="card-header">
            <div v-for="(item, i) in headerList" :key="i">
                {{ item.text }}：
                <span style="color: rgba(0, 0, 0, 0.6)" v-if="item.text !== '类型'">
                    {{ objlist[item.key] ? objlist[item.key] : '暂无' }}
                </span>
                <span v-else style="color: rgba(0, 0, 0, 0.6)">
                    {{
                        objlist[item.key] == 0
                            ? '产品'
                            : objlist[item.key] == 1
                                ? '部件'
                                : objlist[item.key] == 2
                                    ? '零件'
                                    : objlist[item.key] == 3
                                        ? '原材料'
                                        : '暂无'
                    }}
                </span>
            </div>
        </div>
    </el-main>
    <el-main v-loading="Tabledoing">
        <h2>工序过程</h2>
        <fromTable @showDrawer="showDrawer" @process_type="process_type" :crafts="craftsForm" v-if="objlist.crafts">
        </fromTable>
    </el-main>
    <div v-if="drawer" class="modal-container" :style="{ width: drawerWidth + 'px' }" @mouseup="stopDrag">
        <!-- 拖拽条（放在左侧边缘） -->
        <div class="drag-handle" @mousedown.prevent="startDrag"></div>
        <div class="header_ta">
            <h3>{{ drawerName }}</h3>
            <el-icon :size="25" @click="close_button()">
                <el-icon-Close />
            </el-icon>
        </div>
        <div class="modal-content">
            <div id="luckysheet"></div>
            <!-- style="margin: 0 0 10px 0"  -->
            <!-- <div v-show="isMaskShow" id="tip">Downloading</div> -->
        </div>
    </div>
</template>
<script>
import fromTable from './components/fromtable.vue'
import { exportExcel } from './components/export'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import LuckyExcel from 'luckyexcel'

export default {
    name: 'craft',
    components: {
        fromTable
    },
    data() {
        return {
            drawerWidth: 340,  // 默认宽度
            isDragging: false,  // 是否正在拖拽
            startX: 0,         // 拖拽起始位置
            pictLoading: false,
            Tabledoing: false,
            drawer: false,
            headerList: [
                {
                    text: '编码',
                    key: 'number'
                },
                {
                    text: '名称',
                    key: 'title'
                },
                {
                    text: '图号',
                    key: 'drawing_number'
                },
                {
                    text: '类型',
                    key: 'type'
                },
                {
                    text: '规格',
                    key: 'specs'
                },
                {
                    text: '单位',
                    key: 'unit_title'
                },
                {
                    text: '材质',
                    key: 'texture_title'
                },
                {
                    text: '重量',
                    key: 'weight'
                },
                // {
                //     text: '编制',
                //     key: ''
                // },
                {
                    text: '校对',
                    key: 'craft_modify_user_name'
                },
                {
                    text: '修改时间',
                    key: 'updated_at'
                }
            ],
            objlist: {},
            craftsForm: [],
            isMaskShow: false,
            drawerName: '',
            lastToolCode: null,
            material_numberAll: '',
        }
    },
    created() {
        this.material_numberAll = sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`)
        this.loadWidthFromStorage() 
        this.$store.watch(
            (state) => state.listObj,
            (newValue) => {
                this.indata('craft/get_detail', { material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`) })
            }
        )
        this.$store.watch(
            (state) => state.listObj.table,
            (newValue) => {
                this.indata('craft/get_detail', { material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`) })
            }
        )
        // this.indata('craft/get_detail',{material_number:localStorage.getItem("parent_material_number")})
        this.post_data()
    },
    methods: {
        // 开始拖拽
        startDrag(e) {
            this.isDragging = true; // 添加状态标记
            this.startX = e.clientX;

            const dragMask = document.createElement('div');
            dragMask.style.cssText = `
      position: fixed;
      top:0;
      left:0;
      width:100vw;
      height:100vh;
      z-index:9998;  // 调整为低于拖拽条
      cursor: col-resize;
    `;

            document.body.appendChild(dragMask);

            const moveHandler = (e) => {
                this.handleDrag(e);
            };

            const upHandler = () => {
                this.isDragging = false; // 清除状态
                document.removeEventListener('mousemove', moveHandler);
                document.removeEventListener('mouseup', upHandler);
                dragMask.remove();
            };

            document.addEventListener('mousemove', moveHandler);
            document.addEventListener('mouseup', upHandler);
        },
        // 处理拖拽
  
        handleDrag(e) {
            if (!this.isDragging) return;

            // 1. 使用自然方向计算
            const deltaX = this.startX - e.clientX;

            // 2. 添加速度抑制系数
            const speedFactor = 0.8; // 缩小系数控制往左拖拽速度
            const adjustedDelta = deltaX > 0 ? deltaX : deltaX * speedFactor;

            // 3. 应用位移到宽度
            this.drawerWidth = Math.max(340, Math.min(1200, this.drawerWidth + adjustedDelta));

            // 4. 实时更新起始点
            this.startX = e.clientX;
            this.saveWidthToStorage();
            // 5. 边界弹性反馈
            // if (this.drawerWidth >= 1200 || this.drawerWidth <= 340) {
                //     this.$forceUpdate();
                // }
            },
            // 停止拖拽
            stopDrag() {
            console.log(this.drawerWidth,777)
            this.isDragging = false;
            document.removeEventListener('mousemove', this.handleDrag);
            document.removeEventListener('mouseup', this.stopDrag);
      
        },
        // 新增加载方法
        loadWidthFromStorage() {
            try {
                const savedWidth = localStorage.getItem(
                    `craftsmanWidth`
                );
                if (savedWidth) {
                    this.drawerWidth = Math.max(340, Math.min(1200, parseInt(savedWidth)));
                }
            } catch (error) {
                console.error('读取存储失败:', error);
            }
        },
        saveWidthToStorage() {
            try {
                localStorage.setItem(
                    `craftsmanWidth`,
                    this.drawerWidth.toString()
                );
            } catch (error) {
                console.error('本地存储失败:', error);
            }
        },
        close_button() {
            this.drawer = false
            // 销毁原来表格
            window.luckysheet.destroy()
        },
        process_type() {
            this.drawer = false
            this.lastToolCode = null
            // 销毁原来表格
            window.luckysheet.destroy()
            // console.log('111111111111111111');
        },
        showDrawer(op) {
            if (this.lastToolCode !== op.tool_code) {
                // 检查 tool_code 是否发生改变
                this.lastToolCode = op.tool_code // 更新为新的 tool_code
                this.$HTTP.get('tool/get_info', { code: op.tool_code }).then((res) => {
                    if (res.errcode != 0) {
                        ElMessage.error(res.errmsg)
                    } else {
                        if (res.result) {
                            if (res.result.url) {
                                this.$HTTP.get(res.result.url).then((item) => {
                                    if (item) {
                                        this.isMaskShow = true
                                        this.drawerName = res.result.process_title
                                        // if(this.drawer) return
                                        this.drawer = true
                                        console.log(res.result.url,999)
                                        // res.result.url="https://files.tflzg.com/2025-01-10/20250110021847885365xu2sfJqZnBuYrkvlvMOvhJgeRyty1DYe.xlsx"
                                        // console.log(res.result.url,999)
                                        this.doingapi("https://files.qixuw.com/dingb9614df94342f570a1320dcb25e91351/huashan/2025-04-10/b7e956e42bcc75a2adafdc6d4e2b6e23.xlsx")
                                    }
                                })
                            } else {
                                this.$notify({
                                    title: '警告',
                                    message: '暂无匹配的xlsx文件',
                                    type: 'warning'
                                })
                            }
                        } else {
                            this.$notify({
                                title: '警告',
                                message: '暂无匹配的xlsx文件',
                                type: 'warning'
                            })
                        }
                    }
                })
            }
        },
        doingapi(value, name) {
            // console.log('asgdjasgdjasgdhjasgdajsdga',value,name);
            LuckyExcel.transformExcelToLuckyByUrl(value, name, (exportJson, luckysheetfile) => {
                if (exportJson.sheets == null || exportJson.sheets.length == 0) {
                    alert('Failed to read the content of the excel file, currently does not support xls files!')
                    return
                }
                // jsonData.value = exportJson

                this.isMaskShow = false
                // 销毁原来表格
                window.luckysheet.destroy()
                //重新创建新表格
                window.luckysheet.create({
                    container: 'luckysheet', //设定DOM容器id
                    showinfobar: false, //是否显示顶部信息栏
                    data: exportJson.sheets, //表格内容
                    title: exportJson.info.name, //表格标题
                    userInfo: exportJson.info.creator //
                })
            })
        },
        handleChange() {
            console.log(val)
        },
        post_data() {
            let Array = JSON.parse(localStorage.getItem(`craftStaging_${this.material_numberAll}`))
            let num = sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`)
            let arrShow = false
            if (Array) {
                Array.forEach((item) => {
                    if (item.material_number === num) {
                        arrShow = true
                        this.$confirm('此工艺卡有缓存，是否展示缓存数据', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        })
                            .then(() => {
                                this.$message({
                                    type: 'success',
                                    message: '已展示暂存数据!'
                                })
                                this.indata(
                                    'craft/get_detail',
                                    { material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`) },
                                    item.Array
                                )
                                // console.log('1');
                            })
                            .catch(() => {
                                this.indata('craft/get_detail', {
                                    material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`)
                                })
                                // console.log('2');
                                this.$message({
                                    type: 'info',
                                    message: '已取消'
                                })
                            })
                    }
                })
                if (!arrShow) {
                    this.indata('craft/get_detail', { material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`) })
                }
            } else {
                // console.log('4');
                this.indata('craft/get_detail', { material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`) })
            }
        },
        indata(url, page, op) {
            this.pictLoading = true
            this.Tabledoing = true
            this.$HTTP.get(url, page).then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    // this.$store.state.listObj.table = false
                    // this.$store.state.listObj.upload = false
                    this.objlist = res.result
                    if (op) {
                        res.result.crafts = op
                        this.craftsForm = res.result
                    } else {

                        this.craftsForm = res.result
                    }
                    this.pictLoading = false
                    this.Tabledoing = false
                }
            })
        }
    }
}
</script>
<style lang="scss" scoped>
.modal-container {
    // position: fixed;
    // top: 0;
    // right: 0;
    // background-color: #fff;
    // border-radius: 4px;
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    // width: 100%;
    // height: 100%;
    // max-width: 340px;
    // z-index: 10;
    position: fixed;
    top: 0;
    right: 0;
    /* 保持右侧定位 */
    height: 100%;
    z-index: 1001;
    /* 确保高于表格 */
}

.header_ta {
    margin: 20px 0 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.H2 {
    /* margin: 30px 106px 10px; */
    margin: 30px 0px 10px;
}

.nopadding h2 {
    /* margin: 30px 120px 10px; */
    margin: 30px 14px 10px;
}

.card-header {
    display: flex;
    margin: 30px 14px;
    flex-wrap: wrap;
    align-items: center;
    font-size: 14px;
}

.card-header div {
    width: 33.33%;
    height: 45px;
}

// #luckysheet {
//     margin: 0px;
//     padding: 0px;
//     position: absolute;
//     width: 100%;
//     left: 0px;
//     top: 60px;
//     bottom: 0px;
//     z-index: 9999;
// }
#luckysheet {
    margin: 0px;
    padding: 0px;
    position: absolute;
    // width: calc(100% - 8px);
    width: 1200px;
    /* 减少宽度避免覆盖 */
    left: 8px;
    /* 向右缩进8px */
    top: 60px;
    bottom: 0px;
    z-index: 9999 !important;
}

#uploadBtn {
    font-size: 16px;
}

.modal-container {
    position: fixed;
    top: 0;
    right: 0;
    width: 800px;
    /* 默认宽度，会被 :style 覆盖 */
    height: 100%;
    background: #fff;
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: width 0.2s;
    /* 平滑过渡 */
    overflow: visible;
}

.drag-handle {
    position: absolute;
    left: -8px;
    /* 向右移动8px */
    top: 0;
    bottom: 0;
    width: 13px;
    /* 5px可见区域 + 8px右侧延伸 */
    background: #dcdfe6;
    cursor: col-resize;
    z-index: 1002;
    /* 确保高于表格的z-index */

    &:hover {
        background: #c0c4cc;
    }
}
</style>
