<template>
    <el-main style="padding: 0 20px">
        <el-descriptions
            :column="1"
            border
        >
            <el-descriptions-item
                v-for="(item, index) in listA"
                :key="index"
                :label="item.lable"
                width="150px"
                >{{ data[item.key] }}</el-descriptions-item
            >
        </el-descriptions>
    </el-main>
</template>

<script>
export default {
    data() {
        return {
            data: {},
            listA: [
                {
                    lable: '材料名称',
                    key: 'title'
                },
                {
                    lable: '原材料编码',
                    key: 'number'
                },
                {
                    lable: '规格',
                    key: 'specs'
                },
                {
                    lable: '材质',
                    key: 'texture'
                },
                {
                    lable: '单位',
                    key: 'unit'
                }
            ]
        }
    },
    mounted() {},
    methods: {
        //注入数据
        setData(data) {
                        console.log(data,'data')

            this.data = data
        }
    }
}
</script>

<style></style>
