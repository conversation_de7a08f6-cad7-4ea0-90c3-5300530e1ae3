import { createRouter, createWebHashHistory } from 'vue-router';
import { ElNotification } from 'element-plus';
import config from "@/config"
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import tool from '@/utils/tool';
import systemRouter from './systemRouter';
import userRoutes from '@/config/route';
import { beforeEach, afterEach } from './scrollBehavior';

// 匹配views里面所有的.vue文件
const modules = import.meta.glob('./../views/**/*.vue')
const otherModules = {
	'404': () => import('../layout/other/404.vue'),
	'empty': () => import('../layout/other/empty.vue'),
}

//系统路由
const routes = systemRouter

//系统特殊路由
const routes_404 = {
	path: "/:pathMatch(.*)*",
	hidden: true,
	component: otherModules['404'],
}
let routes_404_r = () => { }

const router = createRouter({
	history: createWebHashHistory(),
	routes: routes
})

//设置标题
document.title = config.APP_NAME

//判断是否已加载过动态/静态路由
var isGetRouter = false;

router.beforeEach(async (to, from, next) => {
	NProgress.start()
	//动态标题
	document.title = to.meta.title ? `${to.meta.title} - ${config.APP_NAME}` : `${config.APP_NAME}`

	let token = tool.data.get("TOKEN");
	// let token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJjb3JwX3Byb2R1Y3QiOiJwcm9kdWNlIiwidHlwZXMiOiJpc3YiLCJjb3JwaWQiOiJkaW5nYjk2MTRkZjk0MzQyZjU3MGExMzIwZGNiMjVlOTEzNTEiLCJjb3JwX25hbWUiOiJcdTRlMDBcdTRlMDBcdTc5ZDFcdTYyODBcdTUxODVcdTkwZThcdTVmMDBcdTUzZDFcdTVlNzNcdTUzZjAiLCJ1c2VyaWQiOiIzMzM2MzUzMzIxMjQyMTA2NjQiLCJ1bmlvbmlkIjoiZElqZWUxWmF4SkdqalRrOWlTMXRka0FpRWlFIiwibmFtZSI6Ilx1NWYyMFx1NjMyZlx1NTMxNyIsInN0YWZmX25hbWUiOiJcdTVmMjBcdTYzMmZcdTUzMTciLCJzdGFmZmlkIjoiMzMzNjM1MzMyMTI0MjEwNjY0Iiwiam9iX251bWJlciI6IllZODg4OCIsImFkbWluIjoxfQ.wrHOeZh38sLt6FJaYnLb9VApETfko6B0HuXXhqHOT6Y'

	if (to.path === "/login") {
		//删除路由(替换当前layout路由)
		router.addRoute(routes[0])
		//删除路由(404)
		routes_404_r()
		isGetRouter = false;
		next();
		return false;
	}

	if (routes.findIndex(r => r.path === to.path) >= 0) {
		next();
		return false;
	}

	if (!token) {
		// console.log('无token')
		next({
			path: '/login',
			query: { ...to.query, redirect: to.path }
		});
		return false;
	}

	//整页路由处理
	if (to.meta.fullpage) {
		to.matched = [to.matched[to.matched.length - 1]]
	}
	//加载动态/静态路由
	if (!isGetRouter) {
		let apiMenu = tool.data.get("MENU") || []
		// let userInfo = tool.data.get("USER_INFO")
		// let userMenu = treeFilter(userRoutes, node => {
		// 	console.log(node)
		// 	// return node.meta.role ? node.meta.role.filter(item=>userInfo.role.indexOf(item)>-1).length > 0 : true
		// 	return true
		// })
		var permissionArr = tool.data.get('PERMISSION')
		var menu = [
			{
				id: 0,
				name: "index",
				path: "/index",
				component: "index/index",
				meta: {
					icon: "el-icon-Cpu",
					title: "首页",
				},
				children: [
					{
						path: "/index/crud/table",
						name: "indextable",
						component: "index/crud/table",
						meta: {
							hidden: true,
							title: '没有绑定原材料',
							role: ['SA']
						},
					},
					{
						path: "/index/crud/tableCard",
						name: "indextableCard",
						component: "index/crud/tableCard",
						meta: {
							hidden: true,
							title: '没有工艺卡',
							role: ['SA']
						},
					},
					{
						path: "/index/crud/tableDraw",
						name: "indextableDraw",
						component: "index/crud/tableDraw",
						meta: {
							hidden: true,
							title: '没有图纸',
							role: ['SA']
						},
					},
					{
						path: "/index/crud/bomUnreleasedVersion",
						name: "bomUnreleasedVersion",
						component: "index/crud/bomUnreleasedVersion",
						meta: {
							hidden: true,
							title: 'BOM未发布版本',
							role: ['SA']
						},
					},
					{
						path: "/index/crud/processCardUnreleasedVersion",
						name: "processCardUnreleasedVersion",
						component: "index/crud/processCardUnreleasedVersion",
						meta: {
							hidden: true,
							title: '工艺卡未发布版本',
							role: ['SA']
						},
					},
					{
						path: "/index/crud/cuttingStockDrawing",
						name: "cuttingStockDrawing",
						component: "index/crud/cuttingStockDrawing",
						meta: {
							hidden: true,
							title: '未上传下料图纸',
							role: ['SA']
						},
					},
					{
						path: "/index/crud/detection",
						name: "indexdetection",
						component: "index/crud/detection",
						meta: {
							hidden: true,
							title: '检测页面',
							role: ['SA']
						},
					},
					{
						path: "/product/pegging",
						name: "productpegging",
						component: "product/pegging",
						meta: {
							hidden: true,
							title: '反查页面',
							role: ['SA']
						},
					},
					{
						path: "/index/crud/openBom",
						name: "openBom",
						component: "product/openBom",
						meta: {
							hidden: true,
							title: '打开BOM页面',
							role: ['SA']
						},
					},
					{
						path: "/index/crud/IndexopenBom",
						name: "IndexopenBom",
						component: "product/openBomIndex",
						meta: {
							hidden: true,
							title: '打开BOM页面',
							role: ['SA']
						},
					},
					{
						path: "/index/crud/OAApprovalDetails",
						name: "OAApprovalDetails",
						component: "index/crud/OAApprovalDetails",
						meta: {
							hidden: true,
							title: 'OA审批详情页面',
							role: ['SA']
						},
					},
				]
			},
			// {
			// 	id: 25,
			// 	name: "costAccounting",
			// 	path: "/costAccounting",
			// 	component: "costAccounting/index",
			// 	meta: {
			// 		icon: "el-icon-Filter",
			// 		title: "成本核算",
			// 		role: ['SA']
			// 	},
			// 	children:[
			// 		{
			// 			name:"LaborRateManagement",
			// 			path:"/costAccounting/laborRateManagement",
			// 			component:"costAccounting/laborRateManagement/laborRate",
			// 			meta:{
			// 				title:"人工费率管理",
			// 				role: ['SA']
			// 			}
			// 		},
			// 		{
			// 			name:"ShopRate",
			// 			path:"/costAccounting/shopRateManagement",
			// 			component:"costAccounting/shopRateManagement/shopRate",
			// 			meta:{
			// 				title:"车间费率管理",
			// 				role: ['SA']
			// 			}
			// 		},
	
			// 	]
			// }
		]
		// var selectPermisson = []

		permissionArr.actions.forEach((el) => {
			// selectPermisson.push(el.id)
			switch (el.id) {
				// case 23:
				// 	menu.push(

				// 	)
				// 	break;
				case 11:
					menu.push(
						{
							id: 3,
							name: "searchfors",
							path: "/searchfors",
							component: "searchfors/list",
							meta: {
								icon: "el-icon-search",
								title: "搜索",
								role: ['SA']
							},

						},
						{
							id: 4,
							name: "recovery",
							path: "/recovery",
							component: "recovery/list",
							meta: {
								icon: "el-icon-delete",
								title: "回收站",
								role: ['SA']
							},

						}
					)
					break;
				case 3:
					menu.push(
						{
							id: 1,
							path: "/product/index",
							name: "productIndex",
							component: "product/index",
							meta: {
								icon: "el-icon-Edit",
								title: "产品库",
								primission: 1,
								role: ['SA']
							},
							children: [
								{
									name: "Search",
									path: "/search",
									component: "search/list",
									meta: {
										hidden: true,
										icon: "el-icon-search",
										title: "搜索",
										role: ['SA']
									},

								},
								{
									name: "search/tree",
									path: "/search/tree",
									component: "search/tree",
									meta: {
										hidden: true,
										icon: "el-icon-search",
										title: "反查",
										role: ['SA']
									},

								},
								{
									name: "Searchss",
									path: "/searchss",
									component: "searchss/list",
									meta: {
										hidden: true,
										icon: "el-icon-download",
										title: "打印",
										role: ['SA']
									},
									children: [
									]
								},
								{
									name: "cost",
									path: "/cost",
									component: "cost/list",
									meta: {
										hidden: true,
										icon: "el-icon-document",
										title: "成本",
										role: ['SA']
									},
									children: [
									]
								},
							]
						},
					)
					break;
				case 13:
					menu.push({
						id: 5,
						path: "/basic/audit",
						name: "BasicAudit",
						component: "basic/audit/list",
						meta: {
							icon: "el-icon-Tickets",
							title: "审计日志",
							primission: 13,
							role: ['SA']

						},
						children: [
							{
								path: "/basic/audit/detail",
								name: "BasicAuditDetail",
								component: "basic/audit/crud/detail",
								meta: {
									hidden: true,
									title: '审计日志详情',
									primission: 13,
									role: ['SA']
								},
							},
						],
					})
					break;
				case 21:
					menu.push({
						id: 6,
						name: "Information",
						path: "/information",
						component: "information/list",
						meta: {
							icon: "el-icon-setting",
							title: "基础信息",
							role: ['SA']
						},
						children: [
							{
								name: "InformationUnit",
								path: "/information/unit",
								component: "information/unit/list",
								meta: {
									// icon: "el-icon-setting",
									title: "单位",
									role: ['SA']
								},
							},
							{
								name: "InformationUnitAdd",
								path: "/information/unit/add",
								component: "information/unit/crud/add",
								meta: {
									hidden: true,
									// icon: "el-icon-setting",
									title: "新增单位",
									role: ['SA']
								},
							},
							{
								name: "InformationClass",
								path: "/information/class",
								component: "information/class/list",
								meta: {
									// icon: "el-icon-setting",
									title: "分类",
									role: ['SA']
								},
							},
							{
								name: "InformationClassAdd",
								path: "/information/class/add",
								component: "information/class/crud/add",
								meta: {
									hidden: true,
									// icon: "el-icon-setting",
									title: "新增分类",
									role: ['SA']
								},
							},
							{
								name: "InformationProcess",
								path: "/information/process",
								component: "information/process/list",
								meta: {
									// icon: "el-icon-setting",
									title: "工序",
									role: ['SA']
								},
							},
							{
								name: "InformationProcessAdd",
								path: "/information/process/add",
								component: "information/process/crud/add",
								meta: {
									hidden: true,
									// icon: "el-icon-setting",
									title: "新增工序",
									role: ['SA']
								},
							},
							{
								name: "InformationTool",
								path: "/information/tool",
								component: "information/tool/list",
								meta: {
									// icon: "el-icon-setting",
									title: "机床",
									role: ['SA']
								},
							},
							{
								name: "InformationToolAdd",
								path: "/information/tool/add",
								component: "information/tool/crud/add",
								meta: {
									hidden: true,
									// icon: "el-icon-setting",
									title: "新增机床",
									role: ['SA']
								},
							},
							{
								name: "InformationMaterial",
								path: "/information/material",
								component: "information/material/list",
								meta: {
									// icon: "el-icon-setting",
									title: "材质",
									role: ['SA']
								},
							},
							{
								name: "InformationMaterialAdd",
								path: "/information/material/add",
								component: "information/material/crud/add",
								meta: {
									hidden: true,
									// icon: "el-icon-setting",
									title: "新增材质",
									role: ['SA']
								},
							},
							{
								name: "materialrmationlist",
								path: "/information/materialrmation",
								component: "information/materialrmation/list",
								meta: {
									// icon: "el-icon-setting",
									title: "材料",
									role: ['SA']
								},
							},
							{
								name: "materialrmationAdd",
								path: "/information/materialrmation/add",
								component: "information/materialrmation/crud/add",
								meta: {
									hidden: true,
									// icon: "el-icon-setting",
									title: "新增材料",
									role: ['SA']
								},
							},
							{
								name: "InformationProcessContent",
								path: "/information/processcontent",
								component: "information/processcontent/list",
								meta: {
									// icon: "el-icon-setting",
									title: "工序内容",
									role: ['SA']
								},
							},
							{
								name: "InformationProcessContentAdd",
								path: "/information/processcontent/add",
								component: "information/processcontent/crud/add",
								meta: {
									hidden: true,
									// icon: "el-icon-setting",
									title: "新增工序内容",
									role: ['SA']
								},
							},
							{
								name: "InformationParts",
								path: "/information/parts",
								component: "information/parts/list",
								meta: {
									// icon: "el-icon-setting",
									title: "零件分类",
									role: ['SA']
								},
							},
							{
								name: "InformationPartsAdd",
								path: "/information/parts/add",
								component: "information/parts/crud/add",
								meta: {
									hidden: true,
									// icon: "el-icon-setting",
									title: "新增零件分类",
									role: ['SA']
								},
							},
							{
								name:"ShopRate",
								path:"/costAccounting/shopRateManagement",
								component:"costAccounting/shopRateManagement/shopRate",
								meta:{
									title:"车间费率管理",
									role: ['SA']
								}
							},
							{
								path: "/basic/whiteList",
								name: "whiteList",
								component: "basic/whiteList/cuttingStockDrawing",
								meta: {
									// icon: "el-icon-Setting",
									title: "下料图纸白名单",
									role: ['SA']
									// primission: 8,
								},
							},
							{
								path: "/basic/setting",
								name: "BasicSetting",
								component: "basic/setting/setting",
								meta: {
									// icon: "el-icon-Setting",
									title: "系统设置",
									role: ['SA'],
									primission: 24,
								},
							},
						]
					},)
					break;
				case 8:
					menu.push(
						{
							id: 2,
							path: "/basic/power",
							name: "BasicPower",
							component: "basic/power/list",
							meta: {
								icon: "el-icon-Key",
								title: "权限管理",
								primission: 9,
								role: ['SA']
							},
							children: [
								{
									path: "/basic/power/add",
									name: "BasicPowerAdd",
									component: "basic/power/crud/add",
									meta: {
										hidden: true,
										title: "新增权限管理",
										primission: 9,
									},
								},
								{
									path: "/basic/power/edit",
									name: "BasicPowerEdit",
									component: "basic/power/crud/edit",
									meta: {
										hidden: true,
										title: "编辑权限管理",
										primission: 9,
									},
								},
							],
						},

					)
					break;
			}
		})
		menu = bubbleSort(menu)
		// console.log(userRoutes,apiMenu);
		// 处理menu 顺序问题
		let menus = [...userRoutes, ...menu]
		var menuRouter = filterAsyncRouter(menus)
		// console.log(menu,menuRouter,'12312312312312312');
		menuRouter = flatAsyncRoutes(menuRouter)
		menuRouter.forEach(item => {
			router.addRoute("layout", item)
		})
		routes_404_r = router.addRoute(routes_404)
		if (to.matched.length == 0) {
			router.push(to.fullPath);
		}
		isGetRouter = true;
	}
	beforeEach(to, from)
	next();
});

router.afterEach((to, from) => {
	afterEach(to, from)
	NProgress.done()
});

router.onError((error) => {
	NProgress.done();
	ElNotification.error({
		title: '路由错误',
		message: error.message
	});
});

//入侵追加自定义方法、对象
router.sc_getMenu = () => {
	// var apiMenu = tool.data.get("MENU") || []
	// let userInfo = tool.data.get("USER_INFO")
	// let userMenu = treeFilter(userRoutes, node => {
	// 	// console.log(node.meta.role)
	// 	// return node.meta.role ? node.meta.role.filter(item=>userInfo.role.indexOf(item)>-1).length > 0 : true
	// 	return true
	// })
	// var menu = [...userMenu]
	var permissionArr = tool.data.get('PERMISSION')
	var menu = [
		{
			name: "index",
			path: "/index",
			component: "index/index",
			meta: {
				icon: "el-icon-Cpu",
				title: "首页",
			},
			children: [
				{
					path: "/index/crud/table",
					name: "indextable",
					component: "index/crud/table",
					meta: {
						hidden: true,
						title: '无绑定数据表格',
					},
				},
			]
		},
		// {
		// 	id: 25,
		// 	name: "CostAccounting",
		// 	path: "/costAccounting",
		// 	component: "costAccounting/index",
		// 	meta: {
		// 		icon: "el-icon-Filter",
		// 		title: "成本核算",
		// 		role: ['SA']
		// 	},
		// 	children:[
		// 		{
		// 			name:"LaborRateManagement",
		// 			path:"/costAccounting/laborRateManagement",
		// 			component:"costAccounting/laborRateManagement/laborRate",
		// 			meta:{
		// 				title:"人工费率管理",
		// 				role: ['SA']
		// 			}
		// 		},
		// 		{
		// 			name:"ShopRate",
		// 			path:"/costAccounting/shopRateManagement",
		// 			component:"costAccounting/shopRateManagement/shopRate",
		// 			meta:{
		// 				title:"车间费率管理",
		// 				role: ['SA']
		// 			}
		// 		},

		// 	]
		// }
	]
	// var selectPermisson = []
	permissionArr.actions.forEach((el) => {
		// selectPermisson.push(el.id)
		switch (el.id) {
			// case 23:
			// 	menu.push(

			// 	)
			// 	break;
			case 11:
				menu.push(
					{
						id: 3,
						name: "searchfors",
						path: "/searchfors",
						component: "searchfors/list",
						meta: {
							icon: "el-icon-search",
							title: "搜索",
							role: ['SA']
						},

					},
					{
						id: 4,
						name: "recovery",
						path: "/recovery",
						component: "recovery/list",
						meta: {
							icon: "el-icon-delete",
							title: "回收站",
							role: ['SA']
						},

					}
				)
				break;
			case 3:
				menu.push(
					{
						id: 1,
						path: "/product/index",
						name: "productIndex",
						component: "product/index",
						meta: {
							icon: "el-icon-Edit",
							title: "产品库",
							primission: 1,
							role: ['SA']
						},
						children: [
							{
								name: "Search",
								path: "/search",
								component: "search/list",
								meta: {
									hidden: true,
									icon: "el-icon-search",
									title: "搜索",
									role: ['SA']
								},

							},
							{
								name: "search/tree",
								path: "/search/tree",
								component: "search/tree",
								meta: {
									hidden: true,
									icon: "el-icon-search",
									title: "反查",
									role: ['SA']
								},

							},
							{
								name: "Searchss",
								path: "/searchss",
								component: "searchss/list",
								meta: {
									hidden: true,
									icon: "el-icon-download",
									title: "打印",
									role: ['SA']
								},
								children: [
								]
							},
							{
								name: "cost",
								path: "/cost",
								component: "cost/list",
								meta: {
									hidden: true,
									icon: "el-icon-document",
									title: "成本",
									role: ['SA']
								},
								children: [
								]
							},
						]
					},
				)
				break;
			case 13:
				menu.push({
					id: 5,
					path: "/basic/audit",
					name: "BasicAudit",
					component: "basic/audit/list",
					meta: {
						icon: "el-icon-Tickets",
						title: "审计日志",
						primission: 13,
						role: ['SA']

					},
					children: [
						{
							path: "/basic/audit/detail",
							name: "BasicAuditDetail",
							component: "basic/audit/crud/detail",
							meta: {
								hidden: true,
								title: '审计日志详情',
								primission: 13,
								role: ['SA']
							},
						},
					],
				})
				break;
			case 21:
				menu.push({
					id: 6,
					name: "Information",
					path: "/information",
					component: "information/list",
					meta: {
						icon: "el-icon-setting",
						title: "基础信息",
						role: ['SA']
					},
					children: [
						{
							name: "InformationUnit",
							path: "/information/unit",
							component: "information/unit/list",
							meta: {
								// icon: "el-icon-setting",
								title: "单位",
								role: ['SA']
							},
						},
						{
							name: "InformationUnitAdd",
							path: "/information/unit/add",
							component: "information/unit/crud/add",
							meta: {
								hidden: true,
								// icon: "el-icon-setting",
								title: "新增单位",
								role: ['SA']
							},
						},
						{
							name: "InformationClass",
							path: "/information/class",
							component: "information/class/list",
							meta: {
								// icon: "el-icon-setting",
								title: "分类",
								role: ['SA']
							},
						},
						{
							name: "InformationClassAdd",
							path: "/information/class/add",
							component: "information/class/crud/add",
							meta: {
								hidden: true,
								// icon: "el-icon-setting",
								title: "新增分类",
								role: ['SA']
							},
						},
						{
							name: "InformationProcess",
							path: "/information/process",
							component: "information/process/list",
							meta: {
								// icon: "el-icon-setting",
								title: "工序",
								role: ['SA']
							},
						},
						{
							name: "InformationProcessAdd",
							path: "/information/process/add",
							component: "information/process/crud/add",
							meta: {
								hidden: true,
								// icon: "el-icon-setting",
								title: "新增工序",
								role: ['SA']
							},
						},
						{
							name: "InformationTool",
							path: "/information/tool",
							component: "information/tool/list",
							meta: {
								// icon: "el-icon-setting",
								title: "机床",
								role: ['SA']
							},
						},
						{
							name: "InformationToolAdd",
							path: "/information/tool/add",
							component: "information/tool/crud/add",
							meta: {
								hidden: true,
								// icon: "el-icon-setting",
								title: "新增机床",
								role: ['SA']
							},
						},
						{
							name: "InformationMaterial",
							path: "/information/material",
							component: "information/material/list",
							meta: {
								// icon: "el-icon-setting",
								title: "材质",
								role: ['SA']
							},
						},
						{
							name: "InformationMaterialAdd",
							path: "/information/material/add",
							component: "information/material/crud/add",
							meta: {
								hidden: true,
								// icon: "el-icon-setting",
								title: "新增材质",
								role: ['SA']
							},
						},
						{
							name: "materialrmationlist",
							path: "/information/materialrmation",
							component: "information/materialrmation/list",
							meta: {
								// icon: "el-icon-setting",
								title: "材料",
								role: ['SA']
							},
						},
						{
							name: "materialrmationAdd",
							path: "/information/materialrmation/add",
							component: "information/materialrmation/crud/add",
							meta: {
								hidden: true,
								// icon: "el-icon-setting",
								title: "新增材料",
								role: ['SA']
							},
						},
						{
							name: "InformationProcessContent",
							path: "/information/processcontent",
							component: "information/processcontent/list",
							meta: {
								// icon: "el-icon-setting",
								title: "工序内容",
								role: ['SA']
							},
						},
						{
							name: "InformationProcessContentAdd",
							path: "/information/processcontent/add",
							component: "information/processcontent/crud/add",
							meta: {
								hidden: true,
								// icon: "el-icon-setting",
								title: "新增工序内容",
								role: ['SA']
							},
						},
						{
							name: "InformationParts",
							path: "/information/parts",
							component: "information/parts/list",
							meta: {
								// icon: "el-icon-setting",
								title: "零件分类",
								role: ['SA']
							},
						},
						{
							name: "InformationPartsAdd",
							path: "/information/parts/add",
							component: "information/parts/crud/add",
							meta: {
								hidden: true,
								// icon: "el-icon-setting",
								title: "新增零件分类",
								role: ['SA']
							},
						},
						{
							name:"ShopRate",
							path:"/costAccounting/shopRateManagement",
							component:"costAccounting/shopRateManagement/shopRate",
							meta:{
								title:"车间费率管理",
								role: ['SA']
							}
						},
						{
							path: "/basic/whiteList",
							name: "whiteList",
							component: "basic/whiteList/cuttingStockDrawing",
							meta: {
								// icon: "el-icon-Setting",
								title: "下料图纸白名单",
								role: ['SA']
								// primission: 8,
							},
						},
						{
							path: "/basic/setting",
							name: "BasicSetting",
							component: "basic/setting/setting",
							meta: {
								// icon: "el-icon-Setting",
								title: "系统设置",
								role: ['SA']
								// primission: 8,
							},
						},
					]
				},)
				break;
			case 8:
				menu.push(
					{
						id: 2,
						path: "/basic/power",
						name: "BasicPower",
						component: "basic/power/list",
						meta: {
							icon: "el-icon-Key",
							title: "权限管理",
							primission: 9,
							role: ['SA']
						},
						children: [
							{
								path: "/basic/power/add",
								name: "BasicPowerAdd",
								component: "basic/power/crud/add",
								meta: {
									hidden: true,
									title: "新增权限管理",
									primission: 9,
								},
							},
							{
								path: "/basic/power/edit",
								name: "BasicPowerEdit",
								component: "basic/power/crud/edit",
								meta: {
									hidden: true,
									title: "编辑权限管理",
									primission: 9,
								},
							},
						],
					},

				)
				break;
		}
	})
	menu = bubbleSort(menu)
	let menus = [...userRoutes, ...menu]
	return menus
}

//转换
function filterAsyncRouter(routerMap) {
	const accessedRouters = []
	routerMap.forEach(item => {
		item.meta = item.meta ? item.meta : {};
		//处理外部链接特殊路由
		if (item.meta.type == 'iframe') {
			item.meta.url = item.path;
			item.path = `/i/${item.name}`;
		}
		//MAP转路由对象
		var route = {
			path: item.path,
			name: item.name,
			meta: item.meta,
			redirect: item.redirect,
			children: item.children ? filterAsyncRouter(item.children) : null,
			component: loadComponent(item.component)
		}
		accessedRouters.push(route)
	})
	return accessedRouters
}
function loadComponent(component) {
	if (component) {
		for (const path in modules) {
			const dir = path.split('views/')[1].split('.vue')[0];
			if (dir === component || dir === component + '/index') {
				return () => modules[path]();
			}
		}
	}
	return otherModules['empty']
}

//路由扁平化
function flatAsyncRoutes(routes, breadcrumb = []) {
	let res = []
	routes.forEach(route => {
		const tmp = { ...route }
		if (tmp.children) {
			let childrenBreadcrumb = [...breadcrumb]
			childrenBreadcrumb.push(route)
			let tmpRoute = { ...route }
			tmpRoute.meta.breadcrumb = childrenBreadcrumb
			delete tmpRoute.children
			res.push(tmpRoute)
			let childrenRoutes = flatAsyncRoutes(tmp.children, childrenBreadcrumb)
			childrenRoutes.map(item => {
				res.push(item)
			})
		} else {
			let tmpBreadcrumb = [...breadcrumb]
			tmpBreadcrumb.push(tmp)
			tmp.meta.breadcrumb = tmpBreadcrumb
			res.push(tmp)
		}
	})
	return res
}

// 冒泡排序
function bubbleSort(arr) {
	var len = arr.length;
	for (var i = 0; i < len - 1; i++) {
		for (var j = 0; j < len - i - 1; j++) {
			if (arr[j].id > arr[j + 1].id) {
				var temp = arr[j];
				arr[j] = arr[j + 1];
				arr[j + 1] = temp;
			}
		}
	}
	return arr;
}


//过滤树
// function treeFilter(tree, func) {
// 	return tree.map(node => ({ ...node })).filter(node => {
// 		node.children = node.children && treeFilter(node.children, func)
// 		return func(node) || (node.children && node.children.length)
// 	})
// }

export default router

