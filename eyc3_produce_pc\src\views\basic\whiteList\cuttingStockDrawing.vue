<template>
    <el-container>
        <el-header>
            <el-button type="primary" icon="el-icon-plus" @click="add">添加</el-button>
            <div class="right-panel">
                <div class="right-panel-search">
                    <el-input v-model="query.keyword" placeholder="模糊查询" clearable></el-input>
                    <el-button type="primary" icon="el-icon-search" @click="getDataList"></el-button>
                </div>
            </div>
        </el-header>
        <el-main class="nopadding" v-loading="loading">
            <div class="table-container">
                <el-table :data="tableData" border style="width: 100%" max-height="760" table-layout="fixed">
                    <template #empty>
                    <el-empty
                        description="暂无数据"
                        :image-size="100"
                    ></el-empty>
                </template>
                    <el-table-column prop="material_title" label="物料名称"  />
                    <el-table-column prop="material_number" label="物料编码"  />
                    <!-- <el-table-column prop="specs" label="规格" /> -->
                    <!-- <el-table-column prop="texture_title" label="材质" /> -->
                    <!-- <el-table-column prop="unit_title" label="单位" /> -->
                    <el-table-column fixed="right" label="操作" min-width="120">
                        <template #default="scope">
                            <el-button link type="primary" size="small" @click="handleClick(scope.row)">
                                修改
                            </el-button>
                            <el-button link type="primary" size="small" @click="delItem(scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div style="margin-top: 10px;">
                <el-pagination v-model:current-page="query.page" v-model:page-size="query.pageSize"
                    :page-sizes="[20, 50, 100]" :size="size" :disabled="disabled" :background="background"
                    layout="sizes, prev, pager, next" :total="query.total" @size-change="handleSizeChange"
                    @current-change="handleCurrentChange" />
            </div>
        </el-main>
        <addForm ref="addFromRef" :getDataList="getDataList"></addForm>
    </el-container>
</template>
<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import http from '@/utils/request';
import addForm from './components/addForm.vue';
let query = ref({
    page: 1,
    per_page: 20,
    total: 0
})
let addFromRef = ref()
let tableData = ref([])
let loading = ref(false)
let size = ref('small')

function getDataList() {
    loading.value = true
    http.get("verify/get_ls",query.value).then((res)=>{
        console.log(res,8585)
        loading.value = false
        if(res.errcode == 0){
            tableData.value = res.result.data
            query.value.total = res.result.total
        }
    }).catch((err)=>{
        loading.value = false
    })
}
getDataList()

// 处理键盘事件，按回车键触发查询
function handleKeyDown(event) {
    // 如果按下的是回车键（keyCode 13）
    if (event.keyCode === 13 || event.key === 'Enter') {
        // 检查当前焦点是否在输入框上
        const activeElement = document.activeElement;
        const isInputFocused = activeElement.tagName === 'INPUT' &&
                              activeElement.classList.contains('el-input__inner');

        // 如果焦点在搜索输入框上，或者没有特定元素获得焦点
        if (isInputFocused || activeElement === document.body) {
            // 触发查询按钮点击事件
            getDataList();
        }
    }
}

// 在组件挂载时添加键盘事件监听器
onMounted(() => {
    document.addEventListener('keydown', handleKeyDown);
});

// 在组件卸载前移除键盘事件监听器
onBeforeUnmount(() => {
    document.removeEventListener('keydown', handleKeyDown);
});

//修改
function handleClick(e) {
    addFromRef.value.editItem("修改下料图纸白名单", e)
}
//删除
function delItem(e) {
    http.post("verify/post_del",{id:e.id}).then((res)=>{
        if(res.errcode == 0){
            getDataList()
        }
    })
}
//添加
function add() {
    addFromRef.value.open("新建下料图纸白名单")
}
const handleSizeChange = (val) => {
    query.value.per_page = val
    getDataList()
}
const handleCurrentChange = (val) => {
    query.value.page = val
    getDataList()
}
</script>
