/*
 * @author: 风源
 * @name: 类名
 * @desc: 介绍
 * @LastEditTime: 2023-01-12 01:16:07
 * @FilePath: \eyc3_canyin_pc\vite.config.js
 */
import { defineConfig } from 'vite'
import path from 'path'
import vue from '@vitejs/plugin-vue'
import viteCompression from 'vite-plugin-compression';

// https://vitejs.dev/config/
export default defineConfig({
  base:'./',
  plugins: [vue(),
    viteCompression({
      verbose:true,
      deleteOriginFile:false,
    })
  ],
  resolve: {
    // https://cn.vitejs.dev/config/#resolve-alias
    alias: {
      // 设置路径
      '~': path.resolve(__dirname, './'),
      // 设置别名
      '@': path.resolve(__dirname, './src')
    },
    // https://cn.vitejs.dev/config/#resolve-extensions
    extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
  },
  // vite 相关配置
  server: {
    port: 8088,
    host: true,
    open: true,
  },
  css: {
    postcss: {
      plugins: [
          {
            postcssPlugin: 'internal:charset-removal',
            AtRule: {
              charset: (atRule) => {
                if (atRule.name === 'charset') {
                  atRule.remove();
                }
              }
            }
          }
      ],
    },
  },
})
