<template>
	<el-main style="padding:0 20px;">
		<el-descriptions :column="1" border>
				<el-descriptions-item v-for="(item, index) in listA" :key="index" :label="item.lable" width="150px">{{data[item.key]}}</el-descriptions-item>
			</el-descriptions>
	</el-main>
</template>

<script>
	export default {
		data() {
			return {
				data: {
					
				},
				listA:[
					{
						lable:'单位名称',
						key:'title'
					},
				]
			}
		},
		mounted() {

		},
		methods: {
			//注入数据
			setData(data){
				this.data = data
			}
		}
	}
</script>

<style>
</style>
