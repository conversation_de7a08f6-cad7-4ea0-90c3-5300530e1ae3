<template>
    <h1 style="padding-left: 30px">基础</h1>
    <el-main style="padding: 0 10px"  class="el-main">
        <el-divider />
        <el-card
            shadow="never"
            style="border: none"
        >
            <el-form
                ref="ruleFormRef"
                :model="ruleForm"
                :rules="rules"
                class="demo-form-inline"
                :size="formSize"
                status-icon
                :inline="true"
                label-position="top"
            >
                <el-row>
                    <el-col :span="12">
                        <el-form-item
                            label="权限组名称"
                            prop="title"
                        >
                            <el-input
                                v-model="ruleForm.title"
                                placeholder="请输入"
                                style="min-width: 580px"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            label="权限人员"
                            prop="userlst"
                        >
                            <el-input
                                v-model="ruleForm.userNames"
                                placeholder="请输入"
                                style="min-width: 580px"
                                @click="onSelect"
                                readonly
                            />
                            <!-- <yy_selectuser
                                style="min-width: 580px"
                                v-model="ruleForm.userLst"
                            ></yy_selectuser> -->
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item
                            label="管理范围"
                            prop="manage_scope"
                        >
                        <el-select v-model="ruleForm.manage_scope" style="min-width: 580px" filterable placeholder="请选择">
                            <el-option
                            v-for="item in options"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value">
                            </el-option>
                        </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item
                            label="管理范围"
                            prop="actions"
                        >
                            <yy_selecttree
                                v-model:value.sync="ruleForm.actions"
                                :remote="permissionRemote"
                                :defaultProps="defaultProps"
                            ></yy_selecttree>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </el-card>
    </el-main>
    <el-form-item class="el-buttonA">
        <el-footer style="width: 100%;">
            <el-button
                type="primary"
                :loading="isSaveing"
                @click="submit()"
                >提交</el-button
            >
            <el-button @click="cancel">取 消</el-button>
        </el-footer>
    </el-form-item>
    <!-- <div style="width: 100%; border: 1px solid #DCDFE6;"> -->
    <!-- </div> -->
</template>
  
  <script>
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
export default {
    name: 'verificate',
    data() {
        return {
            ruleForm: {
                title: '',
                userlst: '',
                actions: '',
                manage_scope:''
            },
            options:[
                {
                    label: '全组织',
                    value: 0
                },
                {
                    label: '所在部门和下级部门',
                    value: 1
                },
                {
                    label: '所在部门',
                    value: 2
                }
            ],
            rules: {
                title: [
                    {
                        required: true,
                        message: '请输入权限组名称',
                        trigger: 'blur'
                    }
                ],
                userlst: [
                    {
                        required: true,
                        message: '请选择权限人员',
                        trigger: 'blur'
                    }
                ],
                actions: [
                    {
                        required: true,
                        message: '请选择权限范围',
                        trigger: 'blur'
                    }
                ],
                manage_scope: [
                    {
                        required: true,
                        message: '请选择权限范围',
                        trigger: 'change'
                    }
                ]
            },
            list: [],
            api: '', //接口
            page: {},
            permissionRemote: {
                api: `permission_action/get_all`
            },
            defaultProps: {
                children: 'acts',
                label: 'title'
            }
        }
    },
    methods: {
        list_post() {
            this.$HTTP
                .post('type/get_ls', this.page)
                .then((res) => {
                    this.list = res.result.data
                })
                .finally(() => {})
        },
        cancel() {
            this.$emit('saveclosure', false)
        },
        saveData(data, postdata) {
            // console.log('saveData', data)
            this.ruleForm.id = data.id
            this.ruleForm.title = data.title
            this.ruleForm.userlst = data.userlst
            this.ruleForm.userNames = data.userNames
            this.ruleForm.actions = data.actions
            this.ruleForm.manage_scope = data.manage_scope
            this.api = postdata.edit
            //可以和上面一样单个注入，也可以像下面一样直接合并进去
            Object.assign(this.page, postdata.page, postdata.per_page)
        },
        addsaveData(postdata) {
            console.log(postdata, '新增')
            this.api = postdata.add
            Object.assign(this.page, postdata.page, postdata.per_page)
        },
        //表单提交方法
        submit() {
            // console.log('表格提交', this.ruleForm)
            this.$refs.ruleFormRef.validate(async (valid) => {
                if (valid) {
                    this.$HTTP.post(this.api, this.ruleForm).then((res) => {
                        if (res.errcode == 0) {
                            this.$emit('saveclosure', false)
                            ElMessage.success('操作成功!')
                            this.$emit('transfer', '成功')
                        } else {
                            ElMessage.error(res.errmsg)
                        }
                    })
                } else {
                    console.log('error submit!!')
                    return false
                }
            })
        },
        onSelect() {
            let selectDeparments = []
            let selectUsers = []
            if (this.ruleForm.userlst.length >= 1) {
                let userLst = JSON.parse(this.ruleForm.userlst).users
                console.log('已选择人员', userLst)
                selectUsers = userLst.map((item) => item.userid)
            }
            // var selectDeparments = this.departments.map((item) => item.id)
            console.log('已选择人员', selectUsers)
            var that = this
            this.$TOOL.runtime.biz.contact.complexPicker({
                multiple: true, //是否多选：true多选 false单选； 默认true
                // users: ['10001', '10002', ...], //默认选中的用户列表，员工userid；成功回调中应包含该信息
                corpId: this.$TOOL.data.get('corpid'), //企业id
                pickedUsers: selectUsers,
                pickedDepartments: selectDeparments,
                appId: this.$TOOL.data.get('agentid'),
                maxUsers: 10000, //人数限制，当multiple为true才生效，可选范围1-1500
                onSuccess: function (data) {
                    // console.log('选中人员', data)
                    // that.users = data.users
                    // that.departments = data.departments
                    data.departments = data.departments.map((it) => {
                        return {
                            dept_id: it.id,
                            name: it.name
                        }
                    })
                    data.users = data.users.map((it) => {
                        return {
                            userid: it.emplId,
                            name: it.name,
                            avatar: it.avatar
                        }
                    })
                    that.ruleForm.userlst = JSON.stringify(data)
                    let userNames = data.users
                    let userName = ''
                    for (let i in userNames) {
                        // console.log(i==names.length-1);
                        if (i == userNames.length - 1) {
                            // console.log("最后一个");
                            userName = userName + userNames[i].name
                        } else {
                            userName = userName + userNames[i].name + '、'
                        }
                    }
                    that.ruleForm.userNames = userName
                },
                onFail: function (err) {
                    console.log(err)
                }
            })
        }
    }
}
</script>
  <style scoped>
.h1-div {
    width: 100%;
    height: 10px;
    border-bottom: 1px solid #dcdfe6;
    margin-left: 30px;
}

.el-main{
    margin-bottom: 50px;
}
.el-buttonA {
    width: 100%;
    margin: 20px 0 0 20px;
    background:#fff;
    position: fixed;
    bottom: 5px;
    z-index: 99999;
    box-sizing: border-box;
}
</style>