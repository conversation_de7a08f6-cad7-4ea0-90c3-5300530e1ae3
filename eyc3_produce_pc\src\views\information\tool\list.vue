<template>
    <el-container>
        <el-header>
            <div class="left-panel">
                <el-button
                    type="primary"
                    icon="el-icon-plus"
                    @click="add"
                >添加</el-button>
            </div>
            <div class="right-panel">
                <div class="right-panel-search">
                    <el-input
                        v-model="query.keyword"
                        placeholder="模糊查询"
                        clearable
                    ></el-input>
                    <el-button
                        type="primary"
                        icon="el-icon-search"
                        @click="Query_button"
                    ></el-button>
                </div>
            </div>
        </el-header>
        <el-main class="nopadding" v-loading="loading">
			<scTable ref="table" :data="post_datalist" :lthority="limauthority" row-key="id" hidePagination  hideDo>
				<el-table-column label="机床名称" prop="title" min-width="180"></el-table-column>
				<el-table-column label="机床编码" prop="code" min-width="180"></el-table-column>
				<el-table-column label="工艺名称" prop="process_title" min-width="180"></el-table-column>
				<el-table-column label="所属车间" prop="center" min-width="180"></el-table-column>
				<el-table-column label="车间费率" prop="hour_rate" min-width="180"></el-table-column>
				<!-- <el-table-column label="综合小时费率" prop="hourly_rate" min-width="120"></el-table-column> -->
				<el-table-column label="人工分钟费率" prop="process_hour_rate" min-width="120"></el-table-column>
				<el-table-column label="工时附件" prop="url" min-width="180">
                    <template #default="scope">
                        <span>{{scope.row.url?'已上传':'未上传'}}</span>
                    </template>
                </el-table-column>
				<el-table-column label="创建时间" prop="created_at" min-width="180"></el-table-column>
				<!-- <el-table-column label="文件路径" prop="url" min-width="150"></el-table-column> -->
				<el-table-column label="操作" align="center" min-width="200">
					<template #default="scope">
						<el-button type="primary" plain size="small" @click="table_show(scope.row)">详情</el-button>
						<el-button type="primary" plain size="small" @click="table_edit(scope.row)">编辑</el-button>
						<el-popconfirm title="确定删除吗？" @confirm="table_del(scope.row, scope.$index)">
							<template #reference>
								<el-button plain type="danger" size="small">删除</el-button>
							</template>
						</el-popconfirm>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
        <el-footer>
            <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-sizes="[10, 20, 30, 40]"
                :page-size="query.per_page"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                small
            >
            </el-pagination>
        </el-footer>
    </el-container>
    <el-drawer v-model="dialog.info" :size="500" title="详细" direction="rtl" destroy-on-close>
		<info ref="infoDialog"></info>
	</el-drawer>
    <el-drawer
        v-model="dialog.save"
        :size="800"
        :title="savetitle"
        direction="rtl"
        destroy-on-close
    >
        <save-dialog
            ref="saveDialog"
            @saveclosure="saveclosure"
            @success="handleSaveSuccess"
            @transfer="transfer"
        ></save-dialog>
    </el-drawer>

</template>

<script>
import info from './crud/info'
import saveDialog from './crud/add'
export default {
    name: 'dept',
    components: {
        saveDialog,
        info
    },
    data() {
        return {
            limauthority:false,
            dialog:{
			    save: false,
				info: false
			},
            query: {
                page: 1,
                per_page: 10,
            },
            loading:false,
            post_datalist: [],
            search: {
                keyword: null
            },
            total: 0,
            savetitle: '',
        }
    },
    computed: {
    },
    mounted(){
        this.list_post()
        // 添加键盘事件监听器，按回车键触发查询
        document.addEventListener('keydown', this.handleKeyDown)
    },
    beforeDestroy() {
        // 移除键盘事件监听器
        document.removeEventListener('keydown', this.handleKeyDown)
    },
    methods: {
        // 处理键盘事件，按回车键触发查询
        handleKeyDown(event) {
            // 如果按下的是回车键（keyCode 13）
            if (event.keyCode === 13 || event.key === 'Enter') {
                // 检查当前焦点是否在输入框上
                const activeElement = document.activeElement;
                const isInputFocused = activeElement.tagName === 'INPUT' &&
                                      activeElement.classList.contains('el-input__inner');

                // 如果焦点在搜索输入框上，或者没有特定元素获得焦点
                if (isInputFocused || activeElement === document.body) {
                    // 触发查询按钮点击事件
                    this.Query_button();
                }
            }
        },

        // 数据请求
        list_post(url,op){
            this.loading = true
            this.$HTTP.post('tool/get_ls',this.query).then((res) => {
                if (res.errcode != 0) {
                    this.loading = false
                    if(res.errcode!=510){
                        ElMessage.error(res.errmsg)
                    }else{
                        this.limauthority = true
                    }
                } else {
                    let resData = res.result
                    resData.data.map(press=>{
                        if(press.process){
                            const title = JSON.parse(press.process).map(item=>{
                                return item.process_title
                            })
                            press.process_title = title.join('，')
                        }
                    })
                    this.post_datalist = resData.data
                    this.total = resData.total
                    this.loading = false
                }
            })
        },
        // 添加
        add(){
            this.dialog.save = true
            this.savetitle = '新增'
            this.$nextTick(() => {
                this.$refs.saveDialog.addsaveData({ add: 'tool/post_add' })
            })
        },
        //窗口编辑
        table_edit(row) {
            this.dialog.save = true
            this.savetitle = '编辑'
            this.$nextTick(() => {
                this.$refs.saveDialog.saveData({ edit: 'tool/post_modify', id: row.id })
            })
        },
        // 点击添加提交成功重新调用列表接口
        transfer(op) {
            if (op == '成功') {
                this.list_post()
            }
        },
        // 删除
        table_del(row){
            this.$HTTP.get('tool/post_del', { id: row.id }).then((res) => {
                if (res.errcode == 0) {
                    this.list_post()
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    })
                }
            })
        },
        // 关闭弹窗
        saveclosure(op){
            this.dialog.save = op
        },
        //查看
		table_show(row){
			this.dialog.info = true
				this.$nextTick(() => {
				this.$refs.infoDialog.setData(row)
			})
		},
        // 分页事件
        handleSizeChange(val) {
            // console.log(`每页 ${val} 条`)
            this.query.per_page = val
            this.list_post()
        },
        handleCurrentChange(val) {
            // console.log(`当前页: ${val}`)
            this.query.page = val
            this.list_post()
        },
        // 搜索
        Query_button() {
            // console.log(this.query, '12321321')
            this.list_post()
        },
        // 重置查询表单
        resetQuery() {
            this.query = {
                per_page: 10,
                page: 1
            }
            this.list_post()
        }
    }
}
</script>

<style scoped>
.scTable:deep(.el-table__footer) .cell {
    font-weight: bold;
}
.scTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-horizontal {
    height: 12px;
    border-radius: 12px;
}
.scTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-vertical {
    width: 12px;
    border-radius: 12px;
}
</style>
