<!--
 * @author: 风源
 * @name: 类名
 * @desc: 介绍
 * @LastEditTime: 2022-12-13 17:36:48
 * @FilePath: \eyc3_canyin_pc\src\views\report\user\recharge.vue
-->
<template>
	<yp_list
		ref="table"
		:url="url"
		row-key="id"
		:columns="columns"
		stripe
		:add="add"
		:derive="derive"
		:formitems="formitems"
	>
	</yp_list>
</template>

<script>
export default {
	name: "ReportUserRecharge",
	data() {
		return {
			url: "report/get_rechage_ls",
			columns: [
				{
					label: "姓名",
					prop: "name",
				},
				{
					label: "部门名称",
					prop: "department_name",
				},

				{
					label: "充值类型",
					prop: "desc",
				},
				{
					label: "充值金额",
					prop: "money",
				},
				{
					label: "退款金额",
					prop: "refund_money",
				},
				{
					label: "账户余额",
					prop: "balance",
				},
				{
					label: "支付状态",
					prop: "status",
					component: "fillvalue",
					options: ['待支付', '已支付']
				},

				{
					label: "退款状态",
					prop: "refund_status",
					component: "fillvalue",
					options: ['未退款', '部分退款', '全部退款']
				},
				{
					label: "充值时间",
					prop: "updated_at",
				},
				
			],
			derive: {
				filename:"充值记录"
			},
			formitems: [
				{
					label: "日期范围",
					name: "date_interval",
					value: [
						this.$TOOL.dateFormat(new Date(), "yyyy-MM-01"),
						this.$TOOL.dateFormat(new Date(), "yyyy-MM-dd"),
					],
					component: "date",
					options: {
						type: "daterange",
						rangeseparator: "至",
						startplaceholder: "开始日期",
						endplaceholder: "结束日期",
						valueFormat: "YYYY-MM-DD",
					},
					rules: [
						{
							required: true,
							message: "Please input Data",
							trigger: "change",
						},
					],
				},
				{
					label: "选择人员",
					name: "userlst",
					value: "",
					component: "selectUser"
				},			
				{
					label: "离职人员",
					name: "isleave",
					value: false,
					component: "switch",
				},				
			],
		};
	},
};
</script>

<style></style>
