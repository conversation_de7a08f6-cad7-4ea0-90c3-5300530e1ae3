<template>
    <el-container>
        <el-header>
            <template v-if="topback">
                <el-page-header @back="goBack">
                    <template #content>
                        <span class="text-large font-600 mr-3"> {{ header }} </span>
                    </template>
                </el-page-header>
            </template>
            <template v-else>
                <h2>{{ header }}</h2>
            </template>
        </el-header>
        <el-main>
            <yy_form
                ref="form"
                :modelValue="form"
                :filed="formitems"
                :header="header"
                :status="status"
                @submit="submit"
                @goBack="goBack"
                @changeremoteitems="changeremoteitems"
                @slotsubmitemit="slotsubmitemit"
                :labelWidth="labelWidth"
                :derive="derive"
            >
                <template #bottombtn>
                    <slot name="bottombtn"></slot>
                </template>
            </yy_form>
        </el-main>
    </el-container>
</template>

<script>
import { ElMessage, ElNotification } from 'element-plus'
import useTabs from '@/utils/useTabs'
export default {
    name: 'yp_from',
    emits: ['forminfo', 'slotForm'],
    props: {
        // 表单域标签的宽度
        labelWidth: { type: String, default: '100px' },
        // 表单内容配置项
        columns: { type: Object, default: () => {} },
        // 参数
        post_data: { type: Object, default: () => {} },
        // 返回上级的路由
        backrouter: { type: String, default: '' },
        // 顶部是否有返回键
        topback: { type: Boolean, default: false },
        // 获取远程数据信息
        remote: { type: Object, default: () => {} },
        // 导出按钮配置
        derive: { type: Object, default: () => {} }
    },
    watch: {
        form: {
            handler(val, oldVal) {
                // console.log('-----form变化------')
                // console.log(val.entry_device)
                this.handlerformitems(val)
                // select 多选处理数据
                // this.initialFormData()
            },
            deep: true
        },
        watchFormClear: {
            handler(val, oldval) {
                // console.log('-------')
                // console.log(val, oldval)
                if (JSON.stringify(val) != JSON.stringify(oldval)) {
                    for (let i in val) {
                        if (val[i] != oldval[i]) {
                            this.columns.forEach((el) => {
                                if (el.watch) {
                                    if (el.watch.type == 'clear') {
                                        el.watch.relateName.forEach((it) => {
                                            this.form[it] = 0
                                        })
                                    } else if (el.watch.type == 'total') {
                                        // // 处理获取到的总数
                                        // var total = 0
                                        // var temtotal = this.form[el.watch.totalName]
                                        // if (el.watch.totalSpecial) {
                                        //     if (el.watch.totalSpecial == 'del2') {
                                        //         if (temtotal.length == 3) {
                                        //             total = temtotal.substring(0, 1)
                                        //         } else {
                                        //             total = temtotal.substring(0, 2)
                                        //         }
                                        //     }
                                        // } else {
                                        //     total = temtotal
                                        // }
                                        this.form[el.name] = 1
                                    }
                                }
                            })
                        }
                    }
                }
            }
        },
        watchFormTotal: {
            handler(val, oldval) {
                // if (JSON.stringify(val) != JSON.stringify(oldval)) {
                for (let i in val) {
                    // if (val[i] != oldval[i]) {
                    this.columns.forEach((el) => {
                        if (el.watch) {
                            if (el.watch.type == 'total') {
                                // 处理获取到的总数
                                var total = 0
                                var temtotal = this.form[el.watch.totalName]
                                if (el.watch.totalSpecial) {
                                    if (el.watch.totalSpecial == 'del2') {
                                        if (temtotal.length == 3) {
                                            total = temtotal.substring(0, 1)
                                        } else {
                                            total = temtotal.substring(0, 2)
                                        }
                                    }
                                } else {
                                    total = temtotal
                                }
                                // 进行数据处理
                                if (val[i] <= total) {
                                    console.log(1)
                                    this.form[el.watch.relateName] = total - val[i]
                                }
                            }
                        }
                    })
                    // }
                }
                // }
            }
        }
    },
    data() {
        return {
            // 页头文字
            header: null,
            // 表单loading判断
            renderLoading: false,
            // 表单值
            form: {},
            // 表单配置项
            formitems: [],
            // 底部按钮loading
            loading: false,
            // 表单校验规则
            rules: {},
            // 表单状态
            status: '',
            options: {},
            // 提交状态接口
            url: '',
            // 提交状态数据
            editdata: {},
            // 是否返回
            isback: true,
            // 旧form 表单信息
            oldFormData: {}
        }
    },
    computed: {
        watchFormClear: function () {
            var postdata = {}
            this.columns.forEach((el) => {
                if (el.watch) {
                    if (el.watch.type == 'clear') {
                        postdata[el.name] = this.form[el.name]
                    }
                }
            })
            return postdata
        },
        watchFormTotal: function () {
            var postdata = {}
            this.columns.forEach((el) => {
                if (el.watch) {
                    if (el.watch.type == 'total') {
                        postdata[el.name] = this.form[el.name]
                    }
                }
            })
            return postdata
        }
    },
    mounted() {
        // console.log('mounted')
        let obj = {}
        if (this.$route.params.remote) {
            obj = JSON.parse(this.$route.params.remote)
            let head = obj.label
            this.$store.commit('updateViewTagsTitle', head)
        } else if (this.remote) {
            obj = this.remote
            this.isback = false
        }
        // 根据状态更改头部文字
        if (obj.state == 'detail') {
            this.status = 'detail'
            this.header = '详情'
            this.getData(obj)
        } else if (obj.state == 'edit') {
            this.status = 'edit'
            this.header = '编辑'
            this.url = obj.edit
            this.editdata = obj.editdata
            // console.log(obj.editdata)
            this.getData(obj)
        } else if (obj.state == 'add') {
            this.status = 'add'
            this.header = '新增'
            this.url = obj.api
            this.form = obj.data
            this.formitems = this.columns
            for (let i in this.formitems) {
                var item = this.formitems[i]
                var name = item.name
                this.form[name] = ''
            }
            // 处理下拉框数据处理
            this.handelselectdata(this.formitems)
        }
    },
    methods: {
        // 获取单条用户数据
        async getData(option) {
            this.formitems = this.columns
            this.$HTTP
                .get(option.api, option.data)
                .then((res) => {
                    if (res.errcode != 0) {
                        ElMessage.error(res.errmsg)
                    } else {
                        this.form = res.result
                        // 处理表单项
                        this.handlerformitems(res.result)
                        // 处理下拉框数据处理
                        this.handelselectdata(this.formitems)
                        //处理子项的值相同
                        this.formitems.forEach((item) => {
                            if (item.children) {
                                item.children.forEach((itm) => {
                                    if (itm.options) {
                                        if (itm.options.merge == false) {
                                            if (itm.options.relation_name) {
                                                this.form[itm.name] = this.form[itm.options.relation_name]
                                            }
                                        }
                                    }
                                })
                            }
                        })
                        // 处理表单信息
                        this.handleform(this.formitems)
                        // select 多选处理数据
                        this.initialFormData()
                        // 发送数据
                        this.$emit('forminfo', this.form)
                    }
                })
                .finally(() => {})
        },
        // 处理formitems
        handlerformitems(form) {
            this.formitems.forEach((item) => {
                if (item.children) {
                    for (let i in item.children) {
                        let itm = item.children[i]
                        if (itm.options.mergecontent || itm.options.mergecontent == 0) {
                            if (form[item.name] === itm.options.mergecontent || form[item.name].length == 0) {
                                // console.log('数值一致消失', form[item.name], itm.options.mergecontent)
                                itm.options.merge = true
                                form[itm.name] = null
                            } else {
                                // console.log('数值不一致出现', form[item.name], itm.options.mergecontent)
                                itm.options.merge = false
                            }
                        } else if (itm.options.showcontent || itm.options.showcontent == 0) {
                            if (form[item.name] === itm.options.showcontent) {
                                // console.log('数值一致出现', form[item.name], itm.options.showcontent)
                                itm.options.merge = false
                            } else {
                                // console.log('数值不一致消失', form[item.name], itm.options.showcontent)
                                itm.options.merge = true
                                if (!itm.options.notClean) {
                                    form[itm.name] = null
                                }
                            }
                        }
                    }
                }
            })
        },
        // 提交信息
        submit(form) {
            var temFormStr = JSON.stringify(form)
            this.loading = true
            Object.assign(this.form, this.post_data)
            this.form = form
            // 提交前处理数据转换赋值id
            this.formitems.forEach((item) => {
                if (item.options) {
                    //  item.options.apiselect
                    if (item.options.remote) {
                        if (item.options.remote.mergetype) {
                            item.options.remoteitems.forEach((inde) => {
                                if (this.form[item.name] == inde.title) {
                                    this.form[item.name] = inde.id
                                }
                            })
                        }
                    } else if (item.options.datapost) {
                        if (Array.isArray(item.options.datapost)) {
                            var dealdata = []
                            item.options.datapost.forEach((itm) => {
                                // console.log(itm)
                                var dealdataobj = {}
                                dealdataobj[itm] = this.form[item.name]
                                dealdata.push(dealdataobj)
                            })
                            this.form[item.name] = JSON.stringify(dealdata)
                        }
                    } else if (item.options.arrays) {
                        //提交数据替换
                        if (item.options.remote) {
                            if (item.options.remote.Array) {
                                this.$HTTP.get(item.options.remote.api).then((res) => {
                                    let array = res.result
                                    var arrays = []
                                    this.form[item.name].forEach((res) => {
                                        array.forEach((array) => {
                                            if (array.id == res) {
                                                let rs = item.options.apipost
                                                rs.forEach((obj) => {
                                                    let apipost = {}
                                                    for (const key in obj) {
                                                        if (obj[key].substring(0, 1) == '$') {
                                                            apipost[key] = `${array[obj[key].substring(1)]}`
                                                            if (apipost[key] == '') return
                                                        } else {
                                                            apipost[key] = `${obj[key]}`
                                                        }
                                                    }
                                                    arrays.push(apipost)
                                                })
                                                this.form[item.name] = arrays
                                            }
                                        })
                                    })
                                })
                            }
                        } else {
                            var arrays = []
                            let array = item.options.items
                            this.form[item.name].forEach((res) => {
                                array.forEach((array) => {
                                    if (array.value == res) {
                                        let rs = item.options.apipost
                                        rs.forEach((obj) => {
                                            let apipost = {}
                                            for (const key in obj) {
                                                if (obj[key].substring(0, 1) == '$') {
                                                    apipost[key] = array[obj[key].substring(1)]
                                                    if (apipost[key] == '') return
                                                } else {
                                                    apipost[key] = obj[key]
                                                }
                                            }
                                            arrays.push(apipost)
                                        })
                                        this.form[item.name] = arrays
                                    }
                                })
                            })
                        }
                    }
                }

                // // 对选择权限字段特殊处理去重
                // if (item.name == 'actions') {
                //     let actions = []
                //     let actionsArray = []
                //     JSON.parse(this.form[item.name]).forEach((el) => {
                //         if (actionsArray.includes(el.id)) {
                //             // 包含
                //         } else {
                //             // 不包含
                //             actions.push(el)
                //             actionsArray.push(el.id)
                //         }
                //     })
                //     this.form[item.name] = JSON.stringify(actions)
                //     console.log('处理权限查看--->', this.form[item.name])
                //     console.log('处理权限查看--->', actions)
                // }
            })
            // 对数据进行数据转化处理
            this.postdataswitch(this.formitems)
            // 关联数据(多个子项对应同一个值)
            this.formitems.forEach((item) => {
                if (item.children) {
                    item.children.forEach((itm) => {
                        if (itm.options) {
                            if (itm.options.merge == false) {
                                if (itm.options.relation_name) {
                                    this.form[itm.options.relation_name] = this.form[itm.name]
                                }
                            }
                        }
                    })
                }
            })
            // 对数据进行添加处理
            if (this.editdata) {
                this.form = Object.assign(this.form, this.editdata)
            }
            // 获取数据
            this.$HTTP
                .post(this.url, this.form)
                .then((res) => {
                    if (res.errcode != 0) {
                        if (res.errmsg) {
                            ElMessage.error(res.errmsg)
                        } else {
                            ElMessage.error('提交失败,请重新填写表格!')
                        }
                        this.form = JSON.parse(temFormStr)
                        // this.handleform(this.formitems)
                    } else {
                        // 数据获取成功
                        ElNotification({
                            title: '操作成功',
                            type: 'success'
                        })
                        if (!this.isback) {
                            useTabs.refresh()
                        } else {
                            this.goBack()
                        }
                    }
                })
                .finally(() => {
                    this.loading = false
                    this.$refs.form.changebuttonloading()
                })
        },
        // 修改提交数据类型
        postdataswitch(formitems) {
            formitems.forEach((item) => {
                if (item.children) {
                    this.postdataswitch(item.children)
                }
                if (item.options) {
                    if (item.options.posttype) {
                        if (item.options.posttype == 'string') {
                            // console.log('走了么54', this.form[item.name])
                            this.form[item.name] = JSON.stringify(this.form[item.name])
                            // this.form[item.name] = JSON.parse(this.form[item.name])
                        } else if (item.options.posttype == 'jsonobj_string') {
                            if (typeof this.form[item.name] == 'string') {
                                this.form[item.name] = `[${this.form[item.name]}]`
                            } else if (Array.isArray(this.form[item.name])) {
                                if (this.form[item.name].length >= 1) {
                                    // console.log('21312')
                                    var postData = '['
                                    for (let i in this.form[item.name]) {
                                        // var obj = this.form[item.name]
                                        // console.log(this.form[item.name][i])
                                        var itm = this.form[item.name][i]
                                        // console.log(num, this.form[item.name].length)
                                        if (i == this.form[item.name].length - 1) {
                                            postData = `${postData}${itm}]`
                                        } else {
                                            postData = `${postData}${itm},`
                                        }
                                    }
                                    this.form[item.name] = postData
                                    // console.log(this.form[item.name])
                                } else {
                                    // console.log('-----------43')
                                    this.form[item.name] = '[]'
                                }
                            }
                        } else if (item.options.posttype == 'jsonArray') {
                            if (this.form[item.name]) {
                                var arrPost = []
                                this.form[item.name].forEach((el) => {
                                    // console.log(typeof el)
                                    if (typeof el == 'string') {
                                        // console.log('------------')
                                        // el = JSON.parse(el)
                                        // console.log(el)
                                        arrPost.push(JSON.parse(el))
                                    } else {
                                        arrPost.push(el)
                                    }
                                })
                                if (arrPost.length == 0) {
                                    this.form[item.name] = ''
                                } else {
                                    this.form[item.name] = JSON.stringify(arrPost)
                                }
                            }
                        } else if (item.options.posttype == 'switchName') {
                            if (this.form[item.name] == item.options.switchOldValue) {
                                this.form[item.options.switchName] = item.options.switchNewValue
                            }
                        } else if (item.options.posttype == 'addDate') {
                            var date = new Date()
                            var year = date.getFullYear()
                            var month = date.getMonth() + 1
                            var weekday = date.getDate()
                            if (month < 10) {
                                month = '0' + month
                            }
                            if (weekday < 10) {
                                weekday = '0' + weekday
                            }
                            var splicStr = year + '-' + month + '-' + weekday + ' '
                            this.form[item.name] = splicStr + this.form[item.name]
                        } else if (item.options.posttype == 'jsonSpecifyData') {
                            if (this.form[item.name]) {
                                var postArray = []
                                this.form[item.name].forEach((el) => {
                                    if (typeof el == 'string') {
                                        el = JSON.parse(el)
                                    }
                                    var specifyData = item.options.specifyData
                                    var postData = {}
                                    for (let key in specifyData) {
                                        if (specifyData[key].substring(0, 1) == '$') {
                                            postData[key] = el[specifyData[key].substring(1)]
                                        } else {
                                            postData[key] = specifyData[key]
                                        }
                                    }
                                    postArray.push(postData)
                                })
                                this.form[item.name] = JSON.stringify(postArray)
                            }
                        } else if (item.options.posttype == 'strArray') {
                            if (this.form[item.name]) {
                                var postArr = []
                                postArr.push(this.form[item.name])
                                this.form[item.name] = JSON.stringify(postArr)
                            }
                        } else if (item.options.posttype == 'splitobj') {
                            if (this.form[item.name]) {
                                var specifyData = item.options.splitItems
                                var el = this.form[item.name]
                                var postData = {}
                                for (let key in specifyData) {
                                    if (specifyData[key].substring(0, 1) == '$') {
                                        postData[key] = el[specifyData[key].substring(1)]
                                    } else {
                                        postData[key] = specifyData[key]
                                    }
                                }
                                Object.assign(this.form, postData)
                            }
                            // console.log('-----------424----------')
                            // console.log(postData)
                            // console.log(item.options.splitItems)
                        }
                    }
                }
            })
        },
        // 返回按键
        goBack() {
            if (this.backrouter) {
                // console.log('--------------------')
                // console.log(this.backrouter)
                useTabs.closeNext((tags) => {
                    this.$router.push(this.backrouter)
                    this.$route.is = true
                })
            } else {
                useTabs.close()
            }
        },
        // 处理下拉框信息
        async handelselectdata(formitems) {
            formitems.forEach((item) => {
                if (item.children) {
                    // console.log('第二层')
                    this.handelselectdata(item.children)
                }
                if (item.options) {
                    // console.log('第一层')
                    if (item.options.remote) {
                        if (item.options.remote.datapost) {
                            var remote = item.options.remote
                            //  处理post数据
                            var postData = {}
                            var rs = remote.data
                            for (const key in rs) {
                                if (rs[key].substring(0, 1) == '$') {
                                    postData[key] = this.form[rs[key].substring(1)]
                                    if (postData[key] == '') return
                                } else {
                                    postData[key] = rs[key]
                                }
                            }
                            // 发起网络数据请求
                            this.$HTTP.get(remote.api, postData).then((res) => {
                                if (Array.isArray(res.result)) {
                                    item.options.remoteitems = res.result
                                } else {
                                    item.options.remoteitems = res.result.data
                                }
                                // 判断是否处理数据
                                item.options.remoteitems.forEach((itm) => {
                                    if (Array.isArray(item.options.remote.datapost)) {
                                        var pData = []
                                        item.options.remote.datapost.forEach((im) => {
                                            var postData = {}
                                            var rs = im
                                            for (const key in rs) {
                                                if (rs[key].substring(0, 1) == '$') {
                                                    postData[key] = itm[rs[key].substring(1)]
                                                    if (postData[key] == '') return
                                                } else {
                                                    postData[key] = rs[key]
                                                }
                                            }
                                            pData.push(postData)
                                        })
                                        itm[item.options.remote.value] = JSON.stringify(pData)
                                    } else if (item.options.remote.datapost instanceof Object) {
                                        var postData = {}
                                        var rs = item.options.remote.datapost
                                        for (const key in rs) {
                                            if (rs[key].substring(0, 1) == '$') {
                                                postData[key] = itm[rs[key].substring(1)]
                                                if (postData[key] == '') return
                                            } else {
                                                postData[key] = rs[key]
                                            }
                                        }
                                        itm[item.options.remote.value] = JSON.stringify(postData)
                                    } else {
                                        item.options.remoteitems.forEach((res) => {
                                            if (res.id == this.form[item.name]) {
                                                this.form[item.name] = res.title
                                            }
                                        })
                                    }
                                })
                            })
                        } else if (item.options.remote.apiposts) {
                            var remote = item.options.remote
                            //  处理post数据
                            var postData = {}
                            var rs = remote.data
                            for (const key in rs) {
                                if (rs[key].substring(0, 1) == '$') {
                                    postData[key] = this.form[rs[key].substring(1)]
                                    if (postData[key] == '') return
                                } else {
                                    postData[key] = rs[key]
                                }
                            }
                            this.$HTTP.get(item.options.remote.api, postData).then((res) => {
                                item.options.remoteitems = res.result
                                // 判断是否处理数据
                                item.options.remoteitems.forEach((itm) => {
                                    if (Array.isArray(item.options.apipost)) {
                                        item.options.apipost.forEach((im) => {
                                            var postData = {}
                                            var rs = im
                                            for (const key in rs) {
                                                if (rs[key].substring(0, 1) == '$') {
                                                    postData[key] = `${itm[rs[key].substring(1)]}`
                                                    if (postData[key] == '') return
                                                } else {
                                                    postData[key] = `${rs[key]}`
                                                }
                                            }
                                            // console.log(postData, 'postData')
                                            itm[item.options.remote.value] = JSON.stringify(postData)
                                        })
                                    }
                                })
                            })
                        } else {
                            var remote = item.options.remote
                            //  处理post数据
                            var postData = {}
                            var rs = remote.data
                            for (const key in rs) {
                                if (rs[key].substring(0, 1) == '$') {
                                    postData[key] = this.form[rs[key].substring(1)]
                                    if (postData[key] == '') return
                                } else {
                                    postData[key] = rs[key]
                                }
                            }
                            this.$HTTP.get(remote.api, postData).then((res) => {
                                if (res.errcode != 0) {
                                    ElMessage.error(res.errmsg)
                                } else {
                                    item.options.remoteitems = res.result
                                }
                            })
                        }
                    } else if (item.options.type == 'locality') {
                        var itemsarray = item.options.items
                        itemsarray.forEach((obj) => {
                            if (obj.value == this.form[item.name]) {
                                this.form[item.name] = obj.label
                            }
                        })
                    } else if (item.options.datapost) {
                        item.options.items.forEach((itm) => {
                            var postData = {}
                            var rs = item.options.datapost
                            for (const key in rs) {
                                if (rs[key].substring(0, 1) == '$') {
                                    postData[key] = itm[rs[key].substring(1)]
                                    // if (postData[key] == '') return
                                } else {
                                    postData[key] = rs[key]
                                }
                            }
                            itm.value = JSON.stringify(postData)
                        })
                    }
                }
            })
        },
        // 处理表单信息
        handleform(formitems) {
            formitems.forEach((item) => {
                if (item.children) {
                    this.handleform(item.children)
                }
                if (item.options) {
                    if (item.options.posttype) {
                        // console.log('----------------')
                        // console.log(item)
                        if (item.options.posttype == 'jsonobj_string') {
                            // console.log(89889, this.form[item.name])
                            if (typeof this.form[item.name] == 'string') {
                                this.form[item.name] = JSON.parse(this.form[item.name])
                                // console.log(22222222222222222)
                                // console.log(this.form)
                                // console.log(this.form[item.name])
                                if (this.form[item.name]) {
                                    var getdata = []
                                    this.form[item.name].forEach((itm) => {
                                        // console.log(item.options)
                                        if (item.options.remoteitems) {
                                            // console.log(1111111111111111111)
                                            item.options.remoteitems.forEach((im) => {
                                                // console.log(itm.id, im.id)
                                                if (itm.id == im.id) {
                                                    getdata.push(im[item.options.remote.value])
                                                }
                                            })
                                        } else if (item.options.items) {
                                            item.options.items.forEach((im) => {
                                                if (itm.id == im.id) {
                                                    getdata.push(im.value)
                                                }
                                            })
                                        }
                                    })
                                    if (getdata.length >= 1) {
                                        this.form[item.name] = getdata
                                        console.log(this.form[item.name])
                                    }
                                }
                            }
                        } else if (item.options.posttype == 'string') {
                            this.form[item.name] = JSON.stringify(JSON.parse(this.form[item.name]))
                        } else if (item.options.posttype == 'jsonArray') {
                            if (this.form[item.name]) {
                                this.form[item.name] = JSON.parse(this.form[item.name])
                            }
                        } else if (item.options.posttype == 'switchName') {
                            if (this.form[item.options.switchName] == item.options.switchNewValue) {
                                this.form[item.name] = item.options.switchOldValue
                            }
                        } else if (item.options.posttype == 'addDate') {
                            this.form[item.name] = this.form[item.name].split(' ')[1]
                        } else if (item.options.posttype == 'jsonSpecifyData') {
                            this.form[item.name] = JSON.parse(this.form[item.name])
                        } else if (item.options.posttype == 'strArray') {
                            // if (this.form[item.name]) {
                            //     var handleData = this.form[item.name].split('["')[1]
                            //     handleData = handleData.split('"]')[0]
                            //     console.log('--------426-------')
                            //     console.log(handleData)
                            //     this.form[item.name] = handleData
                            // }
                        } else if (item.options.posttype == 'splitobj') {
                            var postData = {}
                            var specifyData = item.options.splitItems
                            for (let key in specifyData) {
                                console.log(specifyData[key].substring(1))
                                console.log(key)
                                if (specifyData[key].substring(0, 1) == '$') {
                                    postData[specifyData[key].substring(1)] = this.form[key]
                                } else {
                                    postData[specifyData[key].substring(1)] = specifyData[key]
                                }
                            }
                            this.form[item.name] = postData
                            // // if (this.form[item.name]) {
                            // var el = this.form[item.name]
                            // Object.assign(this.form, postData)
                            // // }
                            // // console.log(item.options.splitItems)
                        } else if (item.options.posttype == 'delspace') {
                            this.form[item.name] = JSON.stringify(JSON.parse(this.form[item.name]))
                        }
                    } else if (item.options.format) {
                        // console.log(324324324324)
                        // console.log(item)
                        var arrformat = item.options.format.split('+')
                        this.form[item.name] = ''
                        arrformat.forEach((itm) => {
                            this.form[item.name] = this.form[item.name] + this.form[itm]
                        })
                        // console.log(this.form)
                    }
                }
            })
        },
        // 处理下拉框数据
        changeremoteitems(api) {
            this.changeremoteitems1(this.formitems, api)
        },
        changeremoteitems1(formitems, api) {
            formitems.forEach((item) => {
                if (item.children) {
                    this.changeremoteitems1(item.children, api)
                }
                if (item.options) {
                    if (item.options.remote) {
                        if (item.options.remote.api == api) {
                            console.log('------314------')
                            console.log(item.options.remoteitems)
                            // console.log(api)
                            item.options.remoteitems = {}
                        }
                    }
                }
            })
        },
        initialFormData() {
            for (let i in this.form) {
                let item = this.form[i]
                if (Array.isArray(item)) {
                    var getdata = []
                    item.forEach((itm) => {
                        // console.log(itm)
                        if (itm instanceof Object) {
                            getdata.push(JSON.stringify(itm))
                        } else {
                            getdata.push(itm)
                        }
                    })
                    this.form[i] = getdata
                    // console.log('--------420------')
                    // console.log(this.form[i])
                    // console.log(getdata)
                    // this.columns.forEach((el) => {
                    //     if (el.name == i) {
                    //         var getdata = []
                    //         item.forEach((itm) => {
                    //             // console.log(itm)
                    //             if (itm instanceof Object) {
                    //                 getdata.push(JSON.stringify(itm))
                    //             }
                    //         })
                    //         if (getdata.length >= 1) {
                    //             this.form[i] = getdata
                    //         }
                    //     }
                    // })
                }
            }
        },
        // 插槽重置
        slotreset() {
            // console.log('---ypform 重置----')
            this.$refs.form.resetFields()
        },
        // 插槽提交
        slotsubmit() {
            console.log('----提交2----')
            this.$refs.form.slotsubmit()
        },
        slotsubmitemit(form) {
            console.log('----提交4----')
            this.$emit('slotForm', form)
        }
        // // 重置
        // resetForm(formName) {
        //     this.$refs[formName].resetFields()
        // },
        // 处理表单校验规则
        // handelrules() {
        //     this.formitems.forEach((item) => {
        //         if (item.rules) {
        //             this.rules[item.name] = item.rules
        //         }
        //     })
        // },
    }
}
</script>

<style lang="scss" scoped>
.childitems {
    display: flex;
    .secondarr {
        position: relative;
        // background: rgb(134, 134, 134);
        width: 18px;
        height: 18px;
        top: 5px;
        border-left: 3px solid rgb(177, 177, 177);
        border-bottom: 3px solid rgb(177, 177, 177);
        // left: 0.5px;
        margin-right: 8px;
        margin-right: 10px;
    }
}
.tip {
    width: 100%;
    color: rgb(134, 134, 134);
}
</style>