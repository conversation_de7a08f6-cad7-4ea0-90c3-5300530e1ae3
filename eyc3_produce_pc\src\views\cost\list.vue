<template>
    <div style="margin: 10px;float:right;"><yy_exportmaterial
        v-if="derive"
        :url="derive.url"
        :fileName="derive.filename"
        showData
        :column="derive.columns"
        :fileTypes="['xlsx']"
        :dynamicColumns="derive.dynamicColumns"
        :query="derive.queryform"
        :showsummary="derive.showsummary"
        :data="derive.data"
        :handleStr="derive.handleStr"
    ></yy_exportmaterial></div>
    <el-container class="teble-content">
        <el-main class="nopadding" v-loading="pictLoading">
            <el-table
                ref="multipleTableRef"
                :data="list"
                style="width: 100%; height: 100%"
                :fit="true"
                :highlight-current-row="true"
                @row-contextmenu="rowContextmenu"
            >
                <template #empty>
                    <el-empty
                        description="暂无数据"
                        :image-size="200"
                    ></el-empty>
                </template>
                <el-table-column
                    prop="cost_sum"
                    label="总成本"
                    min-width="100"
                    align="center"
                >
                    <template #default="scope">
                        <span>{{ scope.row.cost_sum }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="artificial_cost"
                    label="人工成本"
                    min-width="100"
                    align="center"
                >
                    <template #default="scope">
                        <span>{{ scope.row.artificial_cost }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="tool_cost"
                    label="设备成本/制造成本"
                    min-width="100"
                    align="center"
                >
                    <template #default="scope">
                        <span>{{ scope.row.tool_cost }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="material_cost"
                    label="材料成本"
                    min-width="100"
                    align="center"
                >
                    <template #default="scope">
                        <span>{{ scope.row.material_cost }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="error_reason"
                    label="计算失败原因"
                    min-width="100"
                    align="center"
                >
                    <template #default="scope">
                        <span>{{ scope.row.error_reason }}</span>
                    </template>
                </el-table-column>
            </el-table>
        </el-main>
    </el-container>
</template>

<script>
import { mapState } from 'vuex'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
export default {
    data() {
        return {
			pictLoading:false,
            menuVisible: false,
            menu_left: 0,
            menu_top: 0,
            currentPage: 1, //初始页
            pagesize: 10, //    每页的数据
            list: [],
            form: {
                cost_sum: '',
                artificial_cost: '',
                tool_cost: '',
                material_cost: '',
                tableData:[
                    {
                        cost_sum: '123',
                        artificial_cost: '123',
                        tool_cost: '123',
                        material_cost: '123'
                    }
                ]
            },
            derive: {
                filename:
                    '成本表' +
                    new Date().getFullYear() +
                    '-' +
                    (new Date().getMonth() + 1) +
                    '-' +
                    new Date().getDate(),
                titleName: '综合BOM表',
                url: 'bom/get_cost',
                fileTypes: ['xlsx', 'pdf'],
                queryform: {
                    material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`)
                },
                columns: [
                    {
                        label: '总成本',
                        prop: 'cost_sum'
                    },
                    {
                        label: '人工成本',
                        prop: 'artificial_cost'
                    },
                    {
                        label: '设备成本/制造成本',
                        prop: 'tool_cost'
                    },
                    {
                        label: '材料成本',
                        prop: 'material_cost'
                    },
                    {
                        label: '计算失败原因',
                        prop: 'error_reason'
                    },
                ]
            }
        }
    },
    computed: {},
    created() {
        this.post_data()
        this.derive.queryform.material_number =this.$store.state.cost_number
    },
    mounted() {},
    methods: {
        post_data() {
			this.pictLoading = true
            this.$HTTP.post('bom/get_cost', { material_number: this.$store.state.cost_number }).then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
					this.pictLoading = false
                } else {
					this.pictLoading = false
                    this.list=res.result
                }
            })
        }
    }
}
</script>

<style scoped lang="scss">
.teble-content {
    width: 100%;
    height: 95%;
}
.pagin {
    position: absolute;
    bottom: 20px;
    min-width: 200px;
}
.box-menu {
    width: 100px;
    position: absolute;
    z-index: 1000;
    background-color: #fff;
    box-shadow: 0px 0px 10px #ccc, 0px 0px 20px #ccc, 0px 0px 30px #ccc;
    padding: 10px;

    div {
        cursor: pointer;
        line-height: 30px;
    }
}

.demo-drawer__footer {
    position: fixed;
    bottom: 10px;
    right: 10px;
}
.demo-form-inline {
    padding: 0 20px;
}
.formType {
    margin-bottom: 50px;
    .title {
        font-size: 17px;
        font-weight: 550;
        padding: 20px;
        border-bottom: 2px solid #e9e9e9;
        margin: 0 0 10px 0;
    }
}
</style>
