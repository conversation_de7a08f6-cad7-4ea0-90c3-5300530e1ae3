<template>
    <el-container>
        <el-header>
            <div class="left-panel">
                <el-button
                    type="primary"
                    icon="el-icon-plus"
                    @click="add"
                    >新增权限组</el-button
                >
            </div>
        </el-header>
        <el-main
            class="nopadding"
            v-loading="loading"
        >
            <scTable
                ref="table"
                :data="list"
                row-key="id"
                @selection-change="selectionChange"
                :lthority="limauthority"
                stripe
                border
                :header-cell-style="{ background: '#EBEEF5' }"
                hideDo
                :bottomSlot="true"
            >
                <el-table-column
                    label="权限组名称"
                    prop="title"
                    min-width="200"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="权限人员"
                    prop="userNames"
                    min-width="200"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    label="权限范围"
                    prop="actionTiles"
                    min-width="220"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="操作"
                    min-width="100"
                    align="center"
                >
                    <template #default="scope">
                        <el-button
                            text
                            size="small"
                            type="primary"
                            @click="table_show(scope.row)"
                            class="btn"
                            >详情</el-button
                        >
                        <el-button
                            class="btn"
                            text
                            type="primary"
                            size="small"
                            @click="table_edit(scope.row)"
                            >编辑</el-button
                        >
                        <el-popconfirm
                            title="确定删除吗？"
                            @confirm="table_del(scope.row, scope.$index)"
                        >
                            <template #reference>
                                <el-button
                                    class="btn"
                                    text
                                    type="primary"
                                    size="small"
                                    >删除</el-button
                                >
                            </template>
                        </el-popconfirm>
                    </template>
                </el-table-column>
                <template #pagination>
                    <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="query.page"
                        :page-sizes="[10, 20, 30, 40]"
                        :page-size="query.per_page"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="total"
                        small
                    >
                    </el-pagination>
                </template>
                <template #button>
                    <el-button
                        @click="refresh"
                        icon="el-icon-refresh"
                        circle
                        style="margin-left: 15px"
                    ></el-button>
                </template>
            </scTable>
        </el-main>
    </el-container>
    <el-drawer
        v-model="dialog.info"
        :size="1300"
        title="详情"
        direction="rtl"
        destroy-on-close
    >
        <info ref="infoDialog"></info>
    </el-drawer>
    <!-- 编辑 -->
    <el-drawer
        v-model="dialog.savelist"
        :size="1300"
        :title="savetitle"
        direction="rtl"
        destroy-on-close
    >
        <save-dialog
            ref="saveDialog"
            @saveclosure="saveclosure"
            @success="handleSaveSuccess"
            @transfer="transfer"
        ></save-dialog>
    </el-drawer>
</template>

<script>
import saveDialog from './crud/save'
import info from './crud/info'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
export default {
    name: 'listCrud',
    components: {
        saveDialog,
        info
    },
    data() {
        return {
            savetitle: '',
            dialog: {
                save: false,
                info: false,
                add: false
            },
            list: [],
            selection: [],
            query: {
                page: 1,
                per_page: 10
            },
            total: 0,
            array: [
                {
                    label: 'id',
                    prop: 'id'
                },
                {
                    label: 'title',
                    prop: 'title'
                },
                {
                    label: 'userNames',
                    prop: 'userNames'
                },
                {
                    label: 'manage_scope',
                    prop: 'manage_scope'
                },
                {
                    label: 'actions',
                    prop: 'actions'
                }
            ],
            loading: false
        }
    },
    created() {
        this.list_post()
    },
    methods: {
        // list列表
        list_post() {
            this.loading = true
            this.$HTTP
                .post('permission_group/get_ls', this.query)
                .then((res) => {
                    if (res.errcode != 0) {
                        if(res.errcode!=510){
                            ElMessage.error(res.errmsg)
                        }else{
                            this.limauthority = true
                        }
                    } else {
                        this.list = res.result.data
                        this.total = res.result.total
                        this.list.forEach((item) => {
                            let userNames = JSON.parse(item.userlst).users
                            let userName = ''
                            for (let i in userNames) {
                                // console.log(i==names.length-1);
                                if (i == userNames.length - 1) {
                                    // console.log("最后一个");
                                    userName = userName + userNames[i].name
                                } else {
                                    userName = userName + userNames[i].name + '、'
                                }
                            }
                            item.userNames = userName
                            let actions = JSON.parse(item.actions)
                            let action = ''
                            for (let i in actions) {
                                // console.log(i==names.length-1);
                                if (i == actions.length - 1) {
                                    // console.log("最后一个");
                                    action = action + actions[i].title
                                } else {
                                    action = action + actions[i].title + '、'
                                }
                            }
                            item.actionTiles = action
                            this.loading = false
                        })
                    }
                })
                .finally(() => {
                    this.loading = false
                })
        },
        //窗口新增
        add() {
            this.dialog.savelist = true
            this.savetitle = '新增权限组'
            this.$nextTick(() => {
                this.$refs.saveDialog.addsaveData({ add: 'permission_group/post_add', page: this.page })
            })
        },
        //窗口编辑
        table_edit(row) {
            this.dialog.savelist = true
            this.savetitle = '编辑'
            this.$nextTick(() => {
                this.$refs.saveDialog.saveData(row, { edit: 'permission_group/post_modify', page: this.page })
            })
        },
        // 编辑取消
        saveclosure(op) {
            this.dialog.savelist = op
        },
        // 点击添加提交成功重新调用列表接口
        transfer(op) {
            if (op == '成功') {
                this.list_post()
            }
        },
        //详情
        table_show(row) {
            const newObj = {}
            // 遍历对象的每个属性
            this.array.map((res) => {
                Object.keys(row).forEach((key) => {
                    if (key === res.prop) {
                        // 将新的属性名和原来的属性值存储到新的对象中
                        newObj[res.label] = row[key]
                    }
                })
                // 检查属性名是否为要替换的旧key
            })
            this.dialog.info = true
            // console.log('详情', row, newObj)
            this.$nextTick(() => {
                this.$refs.infoDialog.setData(newObj)
            })
        },
        //删除
        async table_del(row) {
            var reqData = { id: row.id }
            this.$HTTP.get('permission_group/post_del', reqData).then((res) => {
                if (res.errcode == 0) {
                    ElMessage.success('操作成功!')
                    this.list_post()
                } else {
                    ElMessage.error(res.errmsg)
                }
            })
        },
        //批量删除
        /*    async batch_del() {
            var confirmRes = await this.$confirm(`确定删除选中的 ${this.selection.length} 项吗？`, '提示', {
                type: 'warning',
                confirmButtonText: '删除',
                confirmButtonClass: 'el-button--danger'
            }).catch(() => {})

            if (!confirmRes) {
                return false
            }

            var ids = this.selection.map((v) => v.id)
            this.$refs.table.removeKeys(ids)
            this.$message.success('操作成功')
        }, */
        //表格选择后回调事件
        selectionChange(selection) {
            this.selection = selection
        },
        //本地更新数据
        handleSaveSuccess(data, mode) {
            //为了减少网络请求，直接变更表格内存数据
            if (mode == 'add') {
                this.$refs.table.unshiftRow(data)
            } else if (mode == 'edit') {
                this.$refs.table.updateKey(data)
            }

            //当然也可以暴力的直接刷新表格
            // this.$refs.table.refresh()
        },
        // 分页事件
        handleSizeChange(val) {
            console.log(`每页 ${val} 条`)
            this.query.per_page = val
            this.list_post()
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`)
            this.query.page = val
            this.list_post()
        },
        // 刷新
        refresh() {
            this.query.per_page = 10
            this.query.page = 1
            this.list_post()
        }
    }
}
</script>

<style>
.btn {
    border: none;
    color: #409eff;
    background: none;
}

.nopadding {
    position: relative;
}

.scTable-page {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 50px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
    background-color: #fff;
}
.scTable-do {
    white-space: nowrap;
}
.scTable:deep(.el-table__footer) .cell {
    font-weight: bold;
}
.scTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-horizontal {
    height: 12px;
    border-radius: 12px;
}
.scTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-vertical {
    width: 12px;
    border-radius: 12px;
}
.button_lst {
    display: flex;
    justify-content: space-between;
}
.button_left {
    display: flex;
}
.button_left_div {
    margin-right: 15px;
}
</style>
