<template>
    <el-container>
        <el-header>
            <el-page-header @back="goBack" content="BOM未发布版本">
            </el-page-header>
        </el-header>
        <el-main class="nopadding" v-loading="pictLoading">
            <el-table :data="tableData" stripe style="width: 100%">
                <template #empty>
                    <el-empty description="暂无数据" :image-size="200"></el-empty>
                </template>
                <el-table-column prop="id" label="ID" width="80" />
                <el-table-column prop="material_number" label="代号" min-width="180" />
                <el-table-column prop="material_title" label="名称" min-width="120">
                    <template #default="scope">
                        <span style="color:#4496ee;cursor:pointer;" @click="getDetil(scope.row)">{{
                            scope.row.material_title }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="user_name" label="更新人" min-width="120" />
                <el-table-column prop="updated_at" label="更新时间" min-width="160" />
            </el-table>
            <el-pagination v-model:current-page="pageSet.page" v-model:page-size="pageSet.perpage"
                :page-sizes="[10, 20, 30, 40]" :total="pageSet.total" layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </el-main>
    </el-container>
    <el-drawer v-model="drawer" :title="DetileTile" :with-header="true" size="50%">
        <div style="padding: 0 20px;">
            <el-table :data="tableDetailData" stripe style="width: 100%">
                <template #empty>
                    <el-empty description="暂无数据" :image-size="200"></el-empty>
                </template>

                <el-table-column prop="type" width="80" label="图标">
                    <template #default="scope">
                        <el-icon size="16px" color="color" v-if="scope.row.type == 3">
                            <!-- 原材料 -->
                            <sc-icon-raw-material />
                        </el-icon>
                        <el-icon size="16px" color="color" v-if="scope.row.type == 0">
                            <!-- 产品 -->
                            <sc-icon-product />
                        </el-icon>
                        <el-icon size="16px" color="color" v-if="scope.row.type == 2">
                            <!-- 零件 -->
                            <sc-icon-part />
                        </el-icon>
                        <el-icon size="16px" color="red" v-if="scope.row.type == 1">
                            <!-- 部件 -->
                            <sc-icon-mponent />
                        </el-icon>
                    </template>
                </el-table-column>
                <el-table-column prop="id" label="ID" width="80" />
                <el-table-column prop="number" label="代号" min-width="120" />
                <el-table-column prop="title" label="名称" min-width="120">
                </el-table-column>
                <el-table-column prop="drawing_number" label="图号" min-width="120" />
                <el-table-column prop="specs" label="规格" min-width="130" />
                <el-table-column prop="texture_title" label="材质" min-width="100" />
                <el-table-column prop="dosage" label="用量" min-width="100" />
            </el-table>
        </div>
    </el-drawer>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import HTTP from '@/utils/request'; // 导入HTTP实例

const router = useRouter();
const route = useRoute();
let drawer = ref(false)
let DetileTile = ref('详情')
const pictLoading = ref(false);
const tableData = ref([]);
let tableDetailData=ref([])
const pageSet = ref({
    perpage: 10,
    page: 1,
    total: 0
});

// 返回上一页
function goBack() {
    router.push({ path: "/index" });
}

if(route.params.op){
    pageSet.value.material_number=route.params.op
}
// 获取列表数据
function getPageList() {
    pictLoading.value = true;
    HTTP.get('datav/get_bomlog_ls', pageSet.value).then((res) => {
        console.log(res, 9999444);
        if (res.errcode === 0) {
            tableData.value = res.result.data;
            pageSet.value.total = res.result.total;
            pictLoading.value = false;
        } else {
            pictLoading.value = false
        }
    }).catch((err) => {
        pictLoading.value = false
    });
}

// 处理每页条数变化
function handleSizeChange(val) {
    pageSet.value.perpage = val;
    getPageList();
}

// 处理页码变化
function handleCurrentChange(val) {
    pageSet.value.page = val;
    getPageList();
}
//详情抽屉
function getDetil(op) {
    drawer.value = true
    tableDetailData.value=JSON.parse(op.son_materials)
    console.log(JSON.parse(op.son_materials), 9999);

}
// 页面加载时获取数据
onMounted(() => {
    getPageList();
});
</script>

<style scoped>
.nopadding {
    padding: 20px;
}
</style>