<!--
 * @Descripttion: 文件导入
 * @version: 1.0
 * @Author: sakuya
 * @Date: 2022年5月24日11:30:03
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-01-06 20:56:45
-->

<template>
    <div>
        <el-button type="primary" icon="sc-icon-upload" @click="open" v-show="uploadDrawing">单个图纸上传</el-button>
    </div>

    <el-dialog v-model="dialog" title="导入" :width="550" :close-on-click-modal="false" append-to-body destroy-on-close>
        <el-radio-group v-model="radio" @change="handleChange">
            <el-radio :label="1">非下料图纸</el-radio>
            <el-radio :label="2">下料图纸</el-radio>
            <el-radio :label="3">外购件</el-radio>
        </el-radio-group>
        <el-progress v-if="loading" v-loading="loading" :text-inside="true" :stroke-width="20" :percentage="percentage"
            style="margin-bottom: 15px" />
        <div>
            <div class="upload-demo">
                <div class="el-upload" @dragover.prevent @drop="handleFolderDrop">
                    <input class="el-upload-dragger" style="border: 1px solid red; width: 100%; height: 100%"
                        type="file" multiple @change="handleFileUpload" />
                </div>
                <div class="add">
                    <el-icon class="el-icon--upload" size="60px"><el-icon-upload-filled /></el-icon>
                    <div class="el-upload__text">将文件拖到此处，或<span>点击上传</span></div>
                </div>
            </div>
            <div v-for="(item, index) in folder_files" :key="index" class="files_css">
                <div class="file_name">
                    <el-icon size="15px"><el-icon-document /></el-icon>
                    <span>{{ item.name }}</span>
                </div>
                <el-icon class="iconX" size="15px" @click.stop="edit_fle(index)"><el-icon-close /></el-icon>
            </div>
            <div>总数：{{ folder_files.length }}件</div>
            <div style="text-align: right">
                <el-button type="primary" @click="add_files">确认上传</el-button>
            </div>
        </div>

        <sc-dialog v-model="dialog2" :title="switchisShow ? '图纸是否可以上传' : '工艺卡版本对比'" :loading="dialog2Loading">
            <el-descriptions direction="vertical" :column="2" :size="size" v-if="switchisShow" border>
                <el-descriptions-item :label="'总共' + `${folder_files.length}` + '个可以上传的图纸'">
                    <div v-for="item in folder_files" :key="item" style="margin-bottom: 10px">
                        <div class="addfontcolor">
                            <div>图纸名：{{ item.name }}</div>
                        </div>
                    </div>
                </el-descriptions-item>
                <el-descriptions-item :label="'总共' + `${differenceSet.length}` + '个不可以上传的图纸'">
                    <div v-for="item in differenceSet" :key="item" style="margin-bottom: 10px">
                        <div class="addfontcolor">
                            <div>图纸名：{{ item.name }}</div>
                        </div>
                    </div>
                </el-descriptions-item>
            </el-descriptions>
            <el-descriptions direction="vertical" :column="2" :size="size" v-else border>
                <el-descriptions-item label="对比工序信息"> </el-descriptions-item>
                <el-descriptions-item label="当前工序信息"> </el-descriptions-item>
            </el-descriptions>
        </sc-dialog>
        <!-- 图纸名称是否符合 -->
        <sc-dialog v-model="sheetName">
            <el-descriptions direction="vertical" :column="2" :size="size" border>
                <el-descriptions-item :label="'总共' + `${processedArray.length}` + '个符合图纸命名规则'">
                    <div v-for="item in processedArray" :key="item" style="margin-bottom: 10px">
                        <div class="addfontcolor">
                            <div :style="{ color: item.isMarked ? 'red' : 'black' }">图纸名：{{ item.name }}</div>
                        </div>
                    </div>
                </el-descriptions-item>
                <el-descriptions-item :label="'总共' + `${sheetNameOnMeet.length}` + '个不符合图纸命名规则'">
                    <div v-for="item in sheetNameOnMeet" :key="item" style="margin-bottom: 10px">
                        <div class="addfontcolor">
                            <div>图纸名：{{ item.name }}</div>
                        </div>
                    </div>
                </el-descriptions-item>
            </el-descriptions>
            <template v-slot:footer v-if="processedArray.length>0">
                <el-button type="primary" @click="confirmUploadInto" ref="myButton"
                    v-if="clickInto == '拉入'">确认上传</el-button>
                <el-button type="primary" @click="confirmUpload" ref="myButton" v-else>确认上传</el-button>
            </template>
        </sc-dialog>
    </el-dialog>
    <el-dialog title="上传成功" v-model="dialogTableVisible">
        <el-table :data="gridData">
            <el-table-column property="name" label="图纸名称" width="150"></el-table-column>
            <el-table-column property="" label="" width="200"></el-table-column>
            <el-table-column property="" label=""></el-table-column>
        </el-table>
    </el-dialog>
</template>

<script>
import { ElMessage } from 'element-plus'
export default {
    name: '',
    emits: ['success'],
    props: {
        postDataObj: { type: Object, default: () => { } },
        parentMessage: { type: Object, default: () => { } },
        files_url: { type: Array, default: () => null },
        FolderDrop: { type: Array, default: () => null },
        folder_files: { type: Array, default: () => null }
    },
    data() {
        return {
            dialog2: false,
            sheetName: false,
            dialog2Loading: false,
            // 点击上传的数据
            fileListAdd: [],
            // 符合图纸命名
            sheetNameMeet: [],
            // 不符合图纸命名
            sheetNameOnMeet: [],
            dialogTableVisible: false,
            //图纸权限
            uploadDrawing: null,
            dialog: false,
            loading: false,
            folder_files: [],
            gridData: [],
            differenceSet: [],
            files_url: [],
            FolderDrop: [],
            switchisShow: true, //对比切换
            FolderDrop_event: null,
            radio: 1,
            type: 0,
            fileType:1,
            // 区分是点击还是拉入
            clickInto: '',
            signLoading: false
        }
    },
    computed: {
        processedArray() {
            const prefixMap = {};
            const result = [];

            this.sheetNameMeet.forEach(item => {
                const parts = item.name.split(' ');
                const prefix = parts[0];

                if (!prefixMap[prefix]) {
                    prefixMap[prefix] = [];
                }

                prefixMap[prefix].push(item);

                // 标记如果前缀不止一个对象  
                item.isMarked = prefixMap[prefix].length > 1;

                result.push(item);
            });

            return result;
        }
    },
    mounted() {
        try {
            const array = localStorage.getItem('PERMISSION')
            // console.log(JSON.parse(array).content.actions, '打印的权限节点')
            //判断是否可以下载
            JSON.parse(array).content.actions.map((item) => {
                if (item.id == 9) {
                    item.acts.map((items) => {
                         if (items.title == '上传图纸') {
                            this.uploadDrawing = true
                        }
                    })
                }
            })
            // 处理jsonData
        } catch (error) {
            console.error('解析JSON时出错:', error)
            // 错误处理，比如显示用户友好的消息或使用默认数据
        }
        console.log(this.postDataObj, '>>>>>>')
        console.log(this.parentMessage, '>>>>>>props>>>>>>>')
    },
    methods: {
        handleChange(newRadioValue) {
            console.log('当前选择的值为：', newRadioValue)
            this.fileType=newRadioValue
            if (newRadioValue == 3) {
                this.type = 1
            }
            ; (this.folder_files = []), (this.files_url = []), (this.FolderDrop = []), (this.FolderDrop_event = null)
        },
        open() {
            this.dialog = true
                ; (this.folder_files = []), (this.files_url = []), (this.FolderDrop = [])
            // this.formData = {}
        },
        edit_fle(index) {
            this.folder_files.splice(index, 1)
            this.files_url.splice(index, 1)
        },
        // async handleFolderDrop(event) {
        //     this.clickInto = '拉入'
        //     event.preventDefault()
        //     this.FolderDrop_event = event.dataTransfer.items
        //     for (let i = 0; i < this.FolderDrop_event.length; i++) {
        //         if (this.FolderDrop_event[i].webkitGetAsEntry) {
        //             const entry = this.FolderDrop_event[i].webkitGetAsEntry()
        //             if (entry.isDirectory) {
        //                 this.readDirectory(entry)
        //             } else if (entry.isFile) {
        //                 entry.file((file) => {
        //                     this.FolderDrop.push(file)
        //                 })
        //             }
        //         }
        //     }
        //     const loading = this.$loading({
        //         lock: true,
        //         text: '上传中',
        //         spinner: 'el-icon-loading',
        //         background: 'rgba(0, 0, 0, 0.7)'
        //     })
        //     loading.close();
        //     console.log(this.FolderDrop, 'this.FolderDrop````````````````````````````');
        //     if(this.FolderDrop.length==1){
        //         const newData = this.FolderDrop.map((item) => ({ name: item.name }))
        //         console.log(newData, ' 全部图纸11111111111')
        //         if (this.radio == 1) {
        //             const valid = [];
        //             const invalid = [];
        //             // const regex = /^TMZ/;
        //             const regex = /^(?!\s*$).+?\s/
        //             newData.forEach(item => {
        //                 if (regex.test(item.name) && item.name.endsWith('.pdf')) {
        //                     valid.push(item);
        //                 } else {
        //                     invalid.push(item);
        //                 }
        //             });
        //             console.log('符合', valid);
        //             this.sheetNameMeet = valid
        //             this.sheetNameOnMeet = invalid
        //             console.log('不符合', invalid);
        //         } else if (this.radio == 2) {
        //             const valid = [];
        //             const invalid = [];
        //             const regex = /^TXL-/;
        //             const regexPdf = /\.pdf$/i;
        //             // const regex = /^(?!\s*$).+?\s/
        //             newData.forEach(item => {
        //                 if (regex.test(item.name) && !regexPdf.test(item.name)) {
        //                     valid.push(item);
        //                 } else {
        //                     invalid.push(item);
        //                 }
        //             });
        //             console.log('符合', valid);
        //             this.sheetNameMeet = valid
        //             this.sheetNameOnMeet = invalid
        //             console.log('不符合', invalid);
        //         } else if (this.radio == 3) {
        //             const valid = [];
        //             const invalid = [];
        //             const regex = /^\d{10}/;
        //             newData.forEach(item => {
        //                 if (regex.test(item.name) && item.name.endsWith('.pdf')) {
        //                     valid.push(item);
        //                 } else {
        //                     invalid.push(item);
        //                 }
        //             });
        //             console.log('符合', valid);
        //             this.sheetNameMeet = valid
        //             this.sheetNameOnMeet = invalid
        //         }
        //         this.sheetName = true

        //     }else{
        //         ElMessage.error('只允许上传一张图纸')
        //         this.FolderDrop=[]
        //     }
        // },
        readDirectory(directoryEntry) {
            const reader = directoryEntry.createReader()
            reader.readEntries((entries) => {
                entries.forEach((entry) => {
                    if (entry.isDirectory) {
                        this.readDirectory(entry)
                    } else if (entry.isFile) {
                        entry.file((file) => {
                            this.FolderDrop.push(file)
                        })
                    }
                })
            })
        },
        // 点击上传图纸
        async handleFileUpload(event) {
            const loading = this.$loading({
                lock: true,
                text: '上传中',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            })
            this.folder_files = []
            // 获取用户选择的所有文件
            const fileList = event.target.files
            this.fileListAdd = fileList
            console.log(this.fileListAdd.length==1,'this.fileListAdd1111111')
            loading.close();
            if(this.fileListAdd.length==1){
                const array = [];
                // for (let key in fileList) {  
                //     // 由于对象的键是字符串，但数组索引是整数，我们需要将键转换为整数（如果需要的话）  
                //     // 但在这个特定的例子中，由于我们只是想要将空对象添加到数组中，所以不需要转换键  
                //     // 直接将值（即空对象）添加到数组中  
                // arr.push(fileList[key]);  
                // }
                // 使用Object.keys()遍历对象的键  
                Object.keys(fileList).forEach(key => {
                    // 将每个键对应的值添加到数组中  
                    array.push(fileList[key]);
                });
                const newData = array.map((item) => ({ name: item.name }))
                console.log(newData, ' 全部图纸呀呀咿呀咿呀哟一一')
                if (this.radio == 1) {
                    const valid = [];
                    const invalid = [];
                    // const regex = /^TMZ/;
                    // const regex = /^(?!\s*$).+?\s/
                    newData.forEach(item => {
                        if (item.name.endsWith('.pdf')) {
                            valid.push(item);
                        } else {
                            invalid.push(item);
                        }
                    });
                    console.log('符合', valid);
                    this.sheetNameMeet = valid
                    this.sheetNameOnMeet = invalid
                    console.log('不符合', invalid);
                } else if (this.radio == 2) {
                    const valid = [];
                    const invalid = [];
                    const regex = /^TXL-/;
                    const regexPdf = /\.pdf$/i;
                    // const regex = /^(?!\s*$).+?\s/
                    newData.forEach(item => {
                        if (regex.test(item.name) && !regexPdf.test(item.name)) {
                            valid.push(item);
                        } else {
                            invalid.push(item);
                        }
                    });
                    console.log('符合', valid);
                    this.sheetNameMeet = valid
                    this.sheetNameOnMeet = invalid
                    console.log('不符合', invalid);
                } else if (this.radio == 3) {
                    const valid = [];
                    const invalid = [];
                    // const regex = /^\d{10}/;
                    newData.forEach(item => {
                        if (item.name.endsWith('.pdf')) {
                            valid.push(item);
                        } else {
                            invalid.push(item);
                        }
                    });
                    console.log('符合', valid);
                    this.sheetNameMeet = valid
                    this.sheetNameOnMeet = invalid
                }
                this.sheetName = true
                
            }else{
                ElMessage.error('只允许上传一张图纸')
                this.fileListAdd=[]
          
            }

        },
        //点击上传
        async confirmUpload() {
            this.sheetName = false
            const loading = this.$loading({
                lock: true,
                text: '上传中',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            })
            // this.dialog2 = true
            loading.close();
            // this.switchisShow = true
            // this.files_url传入后端的值
            console.log(this.fileListAdd, 'this.fileListAdd');
            for (let i = 0; i < this.fileListAdd.length; i++) {
                const file = this.fileListAdd[i]
                const fileInfo = {
                    name: file.name,
                    size: file.size,
                    type: file.type
                }
                if (!this.folder_files.some((f) => f.name === file.name)) {
                    this.folder_files.push(fileInfo)
                }
                // this.folder_files.push(fileInfo)
                const formData = new FormData()
                formData.append('file', this.fileListAdd[i])
                formData.append('upload_type', 'local')
                formData.append('file_name', file.name)
                if(this.radio==1){
                    formData.append('number', this.parentMessage.drawing_number)
                }else if(this.radio==2){
                    let array='TXL-'+this.parentMessage.drawing_number
                    formData.append('number', array)
                }else if(this.radio==3){
                    formData.append('number', this.postDataObj.material_number)

                }
                // 添加到 files_url 时也确保不重复

                this.files_url.push(formData)

            }
            // 调用递归函数处理文件夹中的文件
            if (this.files_url.length <= 0) {
                ElMessage.error('没有符合图纸编号的文件')
            }

        },
        async confirmUploadInto() {
            this.sheetName = false
            const loading = this.$loading({
                lock: true,
                text: '上传中',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            })
            // alert('11111111111111')
            this.dialog2 = true
            loading.close();
            this.switchisShow = true
            for (let i = 0; i < this.FolderDrop.length; i++) {
                // console.log(this.FolderDrop[i], ' 全部图纸');
                // const regex = /^(.*)\./;
                // const regex = /(\d+)(.*)/ // 匹配数字和后面的所有字符
                // const result = str.match(regex)[2] // 获取匹配结果的第二组
                const file = this.FolderDrop[i]
                // const fileNameWithoutExt = file.name.split('.')[0];
                // 检查当前文件是否已存在于上传列表中
                const fileInfo = {
                    name: file.name,
                    size: file.size,
                    type: file.type
                }
                // 防止 folder_files 中出现重复项
                if (!this.folder_files.some((f) => f.name === file.name)) {
                    this.folder_files.push(fileInfo)
                }
                // this.folder_files.push(fileInfo)
                const formData = new FormData()
                formData.append('file', this.FolderDrop[i])
                formData.append('upload_type', 'local')
                formData.append('file_name', file.name)
                if(this.radio==1){
                    formData.append('number', this.parentMessage.drawing_number)
                }else if(this.radio==2){
                    let array='TXL-'+this.parentMessage.drawing_number
                    formData.append('number', array)
                }else if(this.radio==3){
                    formData.append('number', this.postDataObj.material_number)

                }
                // 添加到 files_url 时也确保不重复
                // if (!this.files_url.some((f) => f.get('file_name') === file.name && f.get('number') === this.parentMessage.drawing_number)) {
                // }
                this.files_url.push(formData)
                // this.files_url.push(formData)
                // console.log(file,this.files_url,'qweqweqweqw')
                if (this.files_url.length <= 0) {
                    ElMessage.error('没有符合图纸编号的文件')
                }

            }
            const newData = this.FolderDrop.map((item) => ({ name: item.name }))
            // console.log(newData, ' 全部图纸')
            // console.log(this.folder_files[0], 'result')
            let difference = newData.filter((item2) => !this.folder_files.some((item1) => item1.name === item2.name))
            // console.log(difference, '差集');
            this.differenceSet = difference

        },
        add_files() { 
            this.files_url.forEach((item) => {
                this.$HTTP.post(`${import.meta.env.VITE_APP_FILE_UPLOAD}file/post_upload`, item).then((res) => {
                    if (res.errcode != 0) {
                        ElMessage.error(res.errmsg)
                    } else {
                        this.post_add_url(res.result, item)
                    }
                })
            })
        },
        post_add_url(op, item) {
            if(item.get('number')){
                let page = {
                    url: op,
                    file_name: item.get('file_name'),
                    number: item.get('number'),
                    type: this.type,
                    fileType:this.fileType
                }
                console.log(page, 'page');
                this.$HTTP.post('drawing/post_add', page).then((res) => {

                    if (res.errcode != 0) {
                        this.$notify({
                            title: item.get('file_name'),
                            message: '上传失败 ',
                            type: 'error',
                            duration: 5000
                        })
                        // ElMessage.error(res.errmsg)
                    } else {
                        this.dialogTableVisible = true
                        this.dialog = false
                        this.loading = false
                        this.$emit('success', true)
                        this.gridData = this.folder_files
                    }
                })
            }else{
                ElMessage.error('该物料没有对应的图号,无法上传图纸')
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.upload-demo {
    display: flex;
    justify-content: center;
    align-items: center;
}

.el-upload {
    width: 100%;
    position: relative;
    font-size: 14px;
    display: inline-block;
    z-index: 1;
    padding: 70px 0;
    border: 1px dashed #aaa;
    text-align: center;
    vertical-align: middle;

    &:hover {
        cursor: pointer;
    }
}

.el-upload-dragger {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    opacity: 0;

    &:hover {
        cursor: pointer;
    }
}

.el-upload-dragger:hover {
    border-color: #3594f4;
}

.add {
    position: absolute;
    text-align: center;
}

.el-upload__text span {
    color: #3594f4;
}

.files_css {
    width: 80%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .iconX {
        margin: 5px 0 0 20px;
        cursor: pointer;
    }
}

.el-icon {
    margin-right: 7px;
}

.file_name {
    margin-top: 5px;
    display: flex;
    align-items: center;
}
</style>
