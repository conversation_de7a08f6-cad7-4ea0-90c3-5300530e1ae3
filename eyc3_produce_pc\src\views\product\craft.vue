<template>
    <el-main v-loading="pictLoading">
        <h2 class="H2">工艺卡</h2>
        <div class="card-header">
            <div v-for="(item, i) in headerList" :key="i">
                {{ item.text }}：
                <span style="color: rgba(0, 0, 0, 0.6)" v-if="item.text !== '类型'">
                    {{ objlist[item.key] ? objlist[item.key] : '暂无' }}
                </span>
                <span v-else style="color: rgba(0, 0, 0, 0.6)">
                    {{
                        objlist[item.key] == 0
                            ? '产品'
                            : objlist[item.key] == 1
                                ? '部件'
                                : objlist[item.key] == 2
                                    ? '零件'
                                    : objlist[item.key] == 3
                                        ? '原材料'
                                        : '暂无'
                    }}
                </span>
            </div>
        </div>
    </el-main>
    <el-main v-loading="Tabledoing">
        <h2>工序过程</h2>
        <fromTable @showDrawer="showDrawer" @process_type="process_type" :crafts="craftsForm" v-if="objlist.crafts">
        </fromTable>
    </el-main>
    <div v-if="drawer" class="modal-container" :style="{ width: drawerWidth + 'px' }" @mouseup="stopDrag">
        <!-- 拖拽条（放在左侧边缘） -->
        <div class="drag-handle" @mousedown.prevent="startDrag"></div>
        <div class="header_ta">
            <h3>{{ drawerName }}</h3>
            <el-icon :size="25" @click="handleDownload" style="margin-right: 10px;">
                <el-icon-Download />
            </el-icon>
            <el-icon :size="25" @click="close_button()">
                <el-icon-Close />
            </el-icon>
        </div>
        <div class="modal-content">
            <div id="luckysheet"></div>
            <!-- style="margin: 0 0 10px 0"  -->
            <!-- <div v-show="isMaskShow" id="tip">Downloading</div> -->
        </div>
    </div>
</template>
<script>
import fromTable from './components/fromtable.vue'
import { exportExcel } from './components/export'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import luckysheet from 'luckysheet'
import LuckyExcel from 'luckyexcel'
import 'luckysheet/dist/css/luckysheet.css'
import 'luckysheet/dist/assets/iconfont/iconfont.css'
import * as XLSX from 'xlsx';
import { Workbook } from 'exceljs'
import { saveAs } from 'file-saver'

export default {
    name: 'craft',
    components: {
        fromTable
    },
    data() {
        return {
            drawerWidth: 340,  // 默认宽度
            isDragging: false,  // 是否正在拖拽
            startX: 0,         // 拖拽起始位置
            pictLoading: false,
            Tabledoing: false,
            drawer: false,
            headerList: [
                {
                    text: '编码',
                    key: 'number'
                },
                {
                    text: '名称',
                    key: 'title'
                },
                {
                    text: '图号',
                    key: 'drawing_number'
                },
                {
                    text: '类型',
                    key: 'type'
                },
                {
                    text: '原材料规格',
                    key: 'specs'
                },
                {
                    text: '单位',
                    key: 'unit_title'
                },
                {
                    text: '材质',
                    key: 'texture_title'
                },
                {
                    text: '原材料用量',
                    key: 'weight'
                },
                // {
                //     text: '编制',
                //     key: ''
                // },
                // {
                //     text: '校对',
                //     key: 'craft_modify_user_name'
                // },
                {
                    text: '编辑人',
                    key: ''
                }
            ],
            objlist: {},
            craftsForm: [],
            isMaskShow: false,
            drawerName: '',
            lastToolCode: null,
            material_numberAll: '',
        }
    },
    created() {
        this.material_numberAll = sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`)
        this.loadWidthFromStorage()
        this.$store.watch(
            (state) => state.listObj,
            (newValue) => {
                this.indata('craft/get_detail', { material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`) })
            }
        )
        this.$store.watch(
            (state) => state.listObj.table,
            (newValue) => {
                this.indata('craft/get_detail', { material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`) })
            }
        )
        // this.indata('craft/get_detail',{material_number:localStorage.getItem("parent_material_number")})
        this.post_data()
    },
    methods: {
        // 开始拖拽
        startDrag(e) {
            this.isDragging = true; // 添加状态标记
            this.startX = e.clientX;

            const dragMask = document.createElement('div');
            dragMask.style.cssText = `
      position: fixed;
      top:0;
      left:0;
      width:100vw;
      height:100vh;
      z-index:9998;  // 调整为低于拖拽条
      cursor: col-resize;
    `;

            document.body.appendChild(dragMask);

            const moveHandler = (e) => {
                this.handleDrag(e);
            };

            const upHandler = () => {
                this.isDragging = false; // 清除状态
                document.removeEventListener('mousemove', moveHandler);
                document.removeEventListener('mouseup', upHandler);
                dragMask.remove();
            };

            document.addEventListener('mousemove', moveHandler);
            document.addEventListener('mouseup', upHandler);
        },
        // 处理拖拽

        handleDrag(e) {
            if (!this.isDragging) return;

            // 1. 使用自然方向计算
            const deltaX = this.startX - e.clientX;

            // 2. 添加速度抑制系数
            const speedFactor = 0.8; // 缩小系数控制往左拖拽速度
            const adjustedDelta = deltaX > 0 ? deltaX : deltaX * speedFactor;

            // 3. 应用位移到宽度
            this.drawerWidth = Math.max(340, Math.min(1200, this.drawerWidth + adjustedDelta));

            // 4. 实时更新起始点
            this.startX = e.clientX;
            this.saveWidthToStorage();
            // 5. 边界弹性反馈
            // if (this.drawerWidth >= 1200 || this.drawerWidth <= 340) {
            //     this.$forceUpdate();
            // }
        },
        // 停止拖拽
        stopDrag() {
            console.log(this.drawerWidth, 777)
            this.isDragging = false;
            document.removeEventListener('mousemove', this.handleDrag);
            document.removeEventListener('mouseup', this.stopDrag);

        },
        // 新增加载方法
        loadWidthFromStorage() {
            try {
                const savedWidth = localStorage.getItem(
                    `craftsmanWidth`
                );
                if (savedWidth) {
                    this.drawerWidth = Math.max(340, Math.min(1200, parseInt(savedWidth)));
                }
            } catch (error) {
                console.error('读取存储失败:', error);
            }
        },
        saveWidthToStorage() {
            try {
                localStorage.setItem(
                    `craftsmanWidth`,
                    this.drawerWidth.toString()
                );
            } catch (error) {
                console.error('本地存储失败:', error);
            }
        },
        async handleDownload() {
            try {
                // 获取当前sheet的完整信息
                const sheets = luckysheet.getLuckysheetfile();
                if (!sheets || !sheets.length) {
                    ElMessage.warning('没有可导出的数据');
                    return;
                }

                // 创建工作簿
                const workbook = new Workbook();
                const worksheet = workbook.addWorksheet('Sheet1');

                // 获取当前sheet的数据
                const currentSheet = sheets[0];
                const sheetData = currentSheet.data;
                const config = currentSheet.config || {};
                const merges = currentSheet.config?.merge || {};

                // 设置列宽
                if (currentSheet.config && currentSheet.config.columnlen) {
                    worksheet.columns = Object.keys(currentSheet.config.columnlen).map(col => ({
                        width: currentSheet.config.columnlen[col] / 8 // 转换为Excel单位
                    }));
                }

                // 写入数据和样式
                sheetData.forEach((row, rowIndex) => {
                    const excelRow = worksheet.addRow(row.map(cell => {
                        if (!cell) return '';
                        // 处理公式
                        if (cell.f) {
                            // 检查公式计算结果是否为错误值或空值
                            if (cell.v === '#VALUE!' || cell.v === '#DIV/0!' || cell.v === '#NAME?' || 
                                cell.v === '#NULL!' || cell.v === '#NUM!' || cell.v === '#REF!' || 
                                cell.v === '#N/A' || cell.v === null || cell.v === undefined || cell.v === '') {
                                return 0; // 如果是错误值或空值，返回0
                            }
                            return { formula: cell.f.replace(/=/g, '') };
                        }
                        // 如果单元格有值，返回该值，否则返回空字符串
                        return cell.v !== null && cell.v !== undefined ? cell.v : '';
                    }));

                    // 设置行高
                    if (currentSheet.config && currentSheet.config.rowlen && currentSheet.config.rowlen[rowIndex]) {
                        excelRow.height = currentSheet.config.rowlen[rowIndex] / 1.5;
                    }

                    // 设置单元格样式
                    row.forEach((cell, colIndex) => {
                        if (!cell) return;
                        const excelCell = excelRow.getCell(colIndex + 1);
                        
                        // 设置对齐方式
                        if (cell.ct) {
                            excelCell.alignment = {
                                vertical: cell.vt || 'middle',
                                horizontal: cell.ht || 'center',
                                wrapText: cell.tb === '2'
                            };
                        }

                        // 设置边框
                        if (cell.bd) {
                            const borderStyle = { style: 'thin', color: { argb: 'FF000000' } };
                            excelCell.border = {
                                top: borderStyle,
                                left: borderStyle,
                                bottom: borderStyle,
                                right: borderStyle
                            };
                        }

                        // 设置字体
                        if (cell.ff || cell.fc || cell.bl || cell.it || cell.fs) {
                            excelCell.font = {
                                name: cell.ff || 'Arial',
                                size: cell.fs || 11,
                                bold: cell.bl === '1',
                                italic: cell.it === '1',
                                color: { argb: cell.fc ? cell.fc.replace('#', 'FF') : 'FF000000' }
                            };
                        }

                        // 设置背景色
                        if (cell.bg) {
                            excelCell.fill = {
                                type: 'pattern',
                                pattern: 'solid',
                                fgColor: { argb: cell.bg.replace('#', 'FF') }
                            };
                        }
                    });
                });

                // 处理合并单元格
                if (currentSheet.config && currentSheet.config.merge) {
                    Object.keys(currentSheet.config.merge).forEach(key => {
                        const merge = currentSheet.config.merge[key];
                        worksheet.mergeCells(
                            merge.r + 1,
                            merge.c + 1,
                            merge.r + merge.rs,
                            merge.c + merge.cs
                        );
                    });
                }

                // 生成文件并下载
                const buffer = await workbook.xlsx.writeBuffer();
                const blob = new Blob([buffer], { 
                    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
                });
                saveAs(blob, `${this.drawerName || '工艺卡'}.xlsx`);
                
                ElMessage.success('导出成功');
            } catch (error) {
                console.error('导出失败:', error);
                ElMessage.error('导出失败，请重试');
            }
        },
        close_button() {
            this.drawer = false
            // 销毁原来表格
            luckysheet.destroy()
        },
        process_type() {
            this.drawer = false
            this.lastToolCode = null
            // 销毁原来表格
            luckysheet.destroy()
            // console.log('111111111111111111');
        },
        showDrawer(op) {
            if (this.lastToolCode !== op.tool_code) {
                // 检查 tool_code 是否发生改变
                this.lastToolCode = op.tool_code // 更新为新的 tool_code
                this.$HTTP.get('tool/get_info', { code: op.tool_code }).then((res) => {
                    if (res.errcode != 0) {
                        ElMessage.error(res.errmsg)
                    } else {
                        if (res.result) {
                            if (res.result.url) {
                                this.$HTTP.get(res.result.url).then((item) => {
                                // this.$HTTP.get("https://files.qixuw.com/dingb9614df94342f570a1320dcb25e91351/huashan/2025-04-10/b7e956e42bcc75a2adafdc6d4e2b6e23.xlsx").then((item) => {
                                    if (item) {
                                        this.isMaskShow = true
                                        this.drawerName = res.result.process_title
                                        // if(this.drawer) return
                                        this.drawer = true
                                        // res.result.url="https://files.tflzg.com/2025-01-10/20250110021847885365xu2sfJqZnBuYrkvlvMOvhJgeRyty1DYe.xlsx"
                                        // console.log(res.result.url,999)
                                        // this.doingapi("https://files.qixuw.com/dingb9614df94342f570a1320dcb25e91351/huashan/2025-04-10/b7e956e42bcc75a2adafdc6d4e2b6e23.xlsx")
                                        this.doingapi(res.result.url)

                                    }
                                })
                            } else {
                                this.$notify({
                                    title: '警告',
                                    message: '暂无匹配的xlsx文件',
                                    type: 'warning'
                                })
                            }
                        } else {
                            this.$notify({
                                title: '警告',
                                message: '暂无匹配的xlsx文件',
                                type: 'warning'
                            })
                        }
                    }
                })
            }
        },
        doingapi(value, name) {
            LuckyExcel.transformExcelToLuckyByUrl(value, name, (exportJson, luckysheetfile) => {
                if (exportJson.sheets == null || exportJson.sheets.length == 0) {
                    alert('Failed to read the content of the excel file, currently does not support xls files!')
                    return
                }
                // jsonData.value = exportJson

                this.isMaskShow = false
                // 销毁原来表格
                // window.luckysheet.destroy()
                luckysheet.destroy();
                //重新创建新表格
                luckysheet.create({
                    container: 'luckysheet', //设定DOM容器id
                    showinfobar: false, //是否显示顶部信息栏
                    lang: "zh",
                    data: exportJson.sheets, //表格内容
                    title: exportJson.info.name, //表格标题
                    userInfo: exportJson.info.creator //
                })
            })
        },
        handleChange() {
            console.log(val)
        },
        post_data() {
            let Array = JSON.parse(localStorage.getItem(`craftStaging_${this.material_numberAll}`))
            let num = sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`)
            let arrShow = false
            if (Array) {
                Array.forEach((item) => {
                    if (item.material_number === num) {
                        arrShow = true
                        this.$confirm('此工艺卡有缓存，是否展示缓存数据', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        })
                            .then(() => {
                                this.$message({
                                    type: 'success',
                                    message: '已展示暂存数据!'
                                })
                                this.indata(
                                    'craft/get_detail',
                                    { material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`) },
                                    item.Array
                                )
                                // console.log('1');
                            })
                            .catch(() => {
                                this.indata('craft/get_detail', {
                                    material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`)
                                })
                                // console.log('2');
                                this.$message({
                                    type: 'info',
                                    message: '已取消'
                                })
                            })
                    }
                })
                if (!arrShow) {
                    this.indata('craft/get_detail', { material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`) })
                }
            } else {
                // console.log('4');
                this.indata('craft/get_detail', { material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`) })
            }
        },
        indata(url, page, op) {
            this.pictLoading = true
            this.Tabledoing = true
            this.$HTTP.get(url, page).then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    // this.$store.state.listObj.table = false
                    // this.$store.state.listObj.upload = false
                    this.objlist = res.result
                    if (op) {
                        res.result.crafts = op
                        this.craftsForm = res.result
                    } else {

                        this.craftsForm = res.result
                    }
                    this.pictLoading = false
                    this.Tabledoing = false
                }
            })
        }
    }
}
</script>
<style lang="scss" scoped>
.modal-container {
    // position: fixed;
    // top: 0;
    // right: 0;
    // background-color: #fff;
    // border-radius: 4px;
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    // width: 100%;
    // height: 100%;
    // max-width: 340px;
    // z-index: 10;
    position: fixed;
    top: 0;
    right: 0;
    /* 保持右侧定位 */
    height: 100%;
    z-index: 1001;
    /* 确保高于表格 */
}

.header_ta {
    margin: 20px 0 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.H2 {
    /* margin: 30px 106px 10px; */
    margin: 30px 0px 10px;
}

.nopadding h2 {
    /* margin: 30px 120px 10px; */
    margin: 30px 14px 10px;
}

.card-header {
    display: flex;
    margin: 30px 14px;
    flex-wrap: wrap;
    align-items: center;
    font-size: 14px;
}

.card-header div {
    width: 33.33%;
    height: 45px;
}

// #luckysheet {
//     margin: 0px;
//     padding: 0px;
//     position: absolute;
//     width: 100%;
//     left: 0px;
//     top: 60px;
//     bottom: 0px;
//     z-index: 9999;
// }
#luckysheet {
    margin: 0px;
    padding: 0px;
    position: absolute;
    // width: calc(100% - 8px);
    width: 1200px;
    /* 减少宽度避免覆盖 */
    left: 8px;
    /* 向右缩进8px */
    top: 60px;
    bottom: 0px;
    z-index: 9999 !important;
}

#uploadBtn {
    font-size: 16px;
}

.modal-container {
    position: fixed;
    top: 0;
    right: 0;
    width: 800px;
    /* 默认宽度，会被 :style 覆盖 */
    height: 100%;
    background: #fff;
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: width 0.2s;
    /* 平滑过渡 */
    overflow: visible;
}

.drag-handle {
    position: absolute;
    left: -8px;
    /* 向右移动8px */
    top: 0;
    bottom: 0;
    width: 13px;
    /* 5px可见区域 + 8px右侧延伸 */
    background: #dcdfe6;
    cursor: col-resize;
    z-index: 1002;
    /* 确保高于表格的z-index */

    &:hover {
        background: #c0c4cc;
    }
}
</style>
