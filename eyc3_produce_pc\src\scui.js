/*
 * @author: 风源
 * @name: 类名
 * @desc: 介绍
 * @LastEditTime: 2023-01-05 16:55:34
 * @FilePath: \eyc3_canyin_pc\src\scui.js
 */
import config from "./config"
import api from './api'
import tool from './utils/tool'
import http from "./utils/request"
import { permission, rolePermission } from './utils/permission'

import scTable from './components/scTable'
import scTableColumn from './components/scTable/column.js'
import scFilterBar from './components/scFilterBar'
import scUpload from './components/scUpload'
import scUploadMultiple from './components/scUpload/multiple'
import scUploadFile from './components/scUpload/file'
import scFormTable from './components/scFormTable'
import scTableSelect from './components/scTableSelect'
import scPageHeader from './components/scPageHeader'
import scSelect from './components/scSelect'
import scDialog from './components/scDialog'
import scForm from './components/scForm'
import scTitle from './components/scTitle'
import scWaterMark from './components/scWaterMark'
import scQrCode from './components/scQrCode'

import scStatusIndicator from './components/scMini/scStatusIndicator'
import scTrend from './components/scMini/scTrend'

import yp_list from '@/components/yysoft/yp/yp_list'
import yp_form from '@/components/yysoft/yp/yp_form'
import yy_form from '@/components/yysoft/yy/yy_form'
import yy_queryform from '@/components/yysoft/yy/yy_queryform'
import yy_export from '@/components/yysoft/yy/yy_export'
import yy_exportP from '@/components/yysoft/yy/yy_exportP'
import yy_upload from '@/components/yysoft/yy/yy_upload'
import yy_exportmaterial from '@/components/yysoft/yy/yy_exportmaterial'
import yy_upload_loading from '@/components/yysoft/yy/yy_upload_loading'
import yy_uploads from '@/components/yysoft/yy/yy_uploads'
import yy_select from '@/components/yysoft/yy/yy_select'
import yy_selectuser from '@/components/yysoft/yy/yy_selectuser'
import yy_selecttree from '@/components/yysoft/yy/yy_selecttree'
import yy_button from '@/components/yysoft/yy/yy_button'
import yy_checkbox from '@/components/yysoft/yy/yy_checkbox'
import yy_inputtap from '@/components/yysoft/yy/yy_inputtap'
import yy_scEcharts from '@/components/yysoft/yy/yy_scEcharts'
import yp_scEcharts from '@/components/yysoft/yp/yp_scEcharts'
import yy_address from '@/components/yysoft/yy/yy_address'
import yy_tab from '@/components/yysoft/yy/yy_tab'
import yy_dialog from '@/components/yysoft/yy/yy_dialog'
import yy_checkboxDialog from '@/components/yysoft/yy/yy_checkboxDialog'
import yy_descriptions from '@/components/yysoft/yy/yy_descriptions'
import yy_table_select from '@/components/yysoft/yy/yy_table_select'
import yp_list_tree from '@/components/yysoft/yp/yp_list_tree'


import auth from './directives/auth'
import role from './directives/role'
import time from './directives/time'
import copy from './directives/copy'
import errorHandler from './utils/errorHandler'

import * as elIcons from '@element-plus/icons-vue'
import * as scIcons from './assets/icons'

export default {
	install(app) {
		//挂载全局对象
		app.config.globalProperties.$CONFIG = config;
		app.config.globalProperties.$TOOL = tool;
		app.config.globalProperties.$HTTP = http;
		app.config.globalProperties.$API = api;
		app.config.globalProperties.$AUTH = permission;
		app.config.globalProperties.$ROLE = rolePermission;

		//注册全局组件
		app.component('scTable', scTable);
		app.component('scTableColumn', scTableColumn);
		app.component('scFilterBar', scFilterBar);
		app.component('scUpload', scUpload);
		app.component('scUploadMultiple', scUploadMultiple);
		app.component('scUploadFile', scUploadFile);
		app.component('scFormTable', scFormTable);
		app.component('scTableSelect', scTableSelect);
		app.component('scPageHeader', scPageHeader);
		app.component('scSelect', scSelect);
		app.component('scDialog', scDialog);
		app.component('scForm', scForm);
		app.component('scTitle', scTitle);
		app.component('scWaterMark', scWaterMark);
		app.component('scQrCode', scQrCode);
		app.component('scStatusIndicator', scStatusIndicator);
		app.component('scTrend', scTrend);

		app.component('yp_list', yp_list);
		app.component('yy_scEcharts', yy_scEcharts);
		app.component('yp_scEcharts', yp_scEcharts);
		app.component('yp_form', yp_form);
		app.component('yy_form', yy_form);
		app.component('yy_queryform', yy_queryform);
		app.component('yy_export', yy_export);
		app.component('yy_exportP', yy_exportP);
		app.component('yy_exportmaterial', yy_exportmaterial);
		app.component('yy_upload', yy_upload);
		app.component('yy_upload_loading', yy_upload_loading);
		app.component('yy_uploads', yy_uploads);
		app.component('yy_select', yy_select);
		app.component('yy_selectuser', yy_selectuser);
		app.component('yy_selecttree', yy_selecttree);
		app.component('yy_button', yy_button);
		app.component('yy_checkbox', yy_checkbox);
		app.component('yy_inputtap', yy_inputtap);
		app.component('yy_address', yy_address);
		app.component('yy_tab', yy_tab);
		app.component('yy_dialog', yy_dialog);
		app.component('yy_checkboxDialog', yy_checkboxDialog);
		app.component('yy_descriptions', yy_descriptions);
		app.component('yp_list_tree', yp_list_tree);
		app.component('yy_table_select', yy_table_select);

		//注册全局指令
		app.directive('auth', auth)
		app.directive('role', role)
		app.directive('time', time)
		app.directive('copy', copy)

		//统一注册el-icon图标
		for (let icon in elIcons) {
			app.component(`ElIcon${icon}`, elIcons[icon])
		}
		//统一注册sc-icon图标
		for (let icon in scIcons) {
			app.component(`ScIcon${icon}`, scIcons[icon])
		}
		tool.data.remove("TOKEN")

		//关闭async-validator全局控制台警告
		window.ASYNC_VALIDATOR_NO_WARNING = 1

		//全局代码错误捕捉
		app.config.errorHandler = errorHandler

		if (config.RUN_TEST) {
			tool.data.set("TOKEN", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJjb3JwX3Byb2R1Y3QiOiJwcm9kdWNlIiwidHlwZXMiOiJpc3YiLCJjb3JwaWQiOiJkaW5nYjk2MTRkZjk0MzQyZjU3MGExMzIwZGNiMjVlOTEzNTEiLCJjb3JwX25hbWUiOiJcdTRlMDBcdTRlMDBcdTc5ZDFcdTYyODBcdTUxODVcdTkwZThcdTVmMDBcdTUzZDFcdTVlNzNcdTUzZjAiLCJ1c2VyaWQiOiIzMzM2MzUzMzIxMjQyMTA2NjQiLCJ1bmlvbmlkIjoiZElqZWUxWmF4SkdqalRrOWlTMXRka0FpRWlFIiwibmFtZSI6Ilx1NWYyMFx1NjMyZlx1NTMxNyIsInN0YWZmX25hbWUiOiJcdTVmMjBcdTYzMmZcdTUzMTciLCJzdGFmZmlkIjoiMzMzNjM1MzMyMTI0MjEwNjY0Iiwiam9iX251bWJlciI6IllZODg4OCIsImFkbWluIjoxfQ.wrHOeZh38sLt6FJaYnLb9VApETfko6B0HuXXhqHOT6Y");
		}

	},
}

