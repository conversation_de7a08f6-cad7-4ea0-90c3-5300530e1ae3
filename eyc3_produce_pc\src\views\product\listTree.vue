<template>
    <div class="common-layout">
        <el-container v-if="isShow">
            <el-container>
                <el-main class="mainscss">
                    <el-container>
                        <el-main class="tabs-content">
                            <el-container>
                                <el-header class="content-header">
                                    <div class="tabs">
                                        <!-- <el-menu
                                            :default-active="curIndex"
                                            class="el-menu-demo"
                                            @select="handleSelect"
                                        >
                                            <el-menu-item index="1">BOM清单</el-menu-item>
                                            <el-menu-item
                                                index="2"
                                                v-if="!(types == 3 || ((types == 1 || types == 2) && part_type == 1))"
                                                >工艺卡</el-menu-item
                                            >
                                            <el-menu-item index="3">图纸</el-menu-item>
                                            <el-menu-item index="4">详情</el-menu-item>
                                        </el-menu> -->
                                        <template
                                            v-if="!(types == 3 || ((types == 1 || types == 2) && part_type == 1))">
                                            <el-tabs v-model="curIndex" @tab-click="handleSelect">
                                                <el-tab-pane label="BOM清单" :name="1"></el-tab-pane>
                                                <el-tab-pane label="工艺卡" :name="2"></el-tab-pane>
                                                <el-tab-pane label="图纸" :name="3"></el-tab-pane>
                                                <el-tab-pane label="详情" :name="4"></el-tab-pane>
                                            </el-tabs>
                                        </template>
                                        <!-- <template
                                            v-if="part_type == 0">
                                            <el-tabs v-model="curIndex" @tab-click="handleSelect">
                                                <el-tab-pane label="BOM清单" :name="1"></el-tab-pane>
                                                <el-tab-pane label="工艺卡" :name="2"></el-tab-pane>
                                                <el-tab-pane label="图纸" :name="3"></el-tab-pane>
                                                <el-tab-pane label="详情" :name="4"></el-tab-pane>
                                            </el-tabs>
                                        </template> -->
                                        <template v-else>
                                            <el-tabs v-model="curIndex" @tab-click="handleSelect">
                                                <el-tab-pane label="BOM清单" :name="1"></el-tab-pane>

                                                <el-tab-pane label="图纸" :name="3"></el-tab-pane>
                                                <el-tab-pane label="详情" :name="4"></el-tab-pane>
                                            </el-tabs>
                                        </template>
                                    </div>
                                    <div class="btns" v-if="curIndex === 1">
                                        <el-dropdown trigger="click" v-show="showButton">
                                            <el-button class="el-dropdown-link" icon="el-icon-arrow-down">
                                                按钮菜单
                                            </el-button>
                                            <template #dropdown>
                                                <el-dropdown-menu class="dropdown_menu">
                                                    <yy_export v-if="derive" :url="derive.url" :label="derive.label"
                                                        :fileName="derive.filename" showData :column="derive.columns"
                                                        :fileTypes="['xlsx']" :dynamicColumns="derive.dynamicColumns"
                                                        :query="derive.queryform" :showsummary="derive.showsummary"
                                                        :data="derive.data" :handleStr="derive.handleStr"></yy_export>
                                                    <yy_exportmaterial v-if="Summary" :url="Summary.url" :label="Summary.label"
                                                        :fileName="Summary.filename" showData :column="Summary.columns"
                                                        :fileTypes="['xlsx']" :dynamicColumns="Summary.dynamicColumns"
                                                        :query="Summary.queryform" :showsummary="Summary.showsummary"
                                                        :data="Summary.data" :handleStr="Summary.handleStr">
                                                    </yy_exportmaterial>
                                                    <!-- <yy_exportP
                                                        v-if="detail"
                                                        :url="detail.url"
                                                        :label="detail.label"
                                                        :fileName="detail.filename"
                                                        showData
                                                        :column="detail.columns"
                                                        :fileTypes="['xlsx']"
                                                        :dynamicColumns="detail.dynamicColumns"
                                                        :query="detail.queryform"
                                                        :showsummary="detail.showsummary"
                                                        :data="detail.data"
                                                        :handleStr="detail.handleStr"
                                                    ></yy_exportP> -->
                                                    <yy_exportP v-if="caparts" :url="caparts.url" :label="caparts.label"
                                                        :fileName="caparts.filename" showData :column="caparts.columns"
                                                        :fileTypes="['xlsx']" :dynamicColumns="caparts.dynamicColumns"
                                                        :query="caparts.queryform" :showsummary="caparts.showsummary"
                                                        :data="caparts.data" :handleStr="caparts.handleStr">
                                                    </yy_exportP>
                                                </el-dropdown-menu>
                                            </template>
                                        </el-dropdown>
                                        <!-- <el-button
                                            class="btn"
                                            type="primary"
                                            icon="el-icon-plus"
                                            @click="onSubmit"
                                            >添加</el-button
                                        > -->
                                        <!-- <el-button
                                            class="btn"
                                            type="info"
                                            plain
                                            icon="el-icon-Delete"
                                            >停用</el-button
                                        > -->
                                    </div>
                                    <!-- <div class="btns" v-if="curIndex === 4">
                                        <el-button class="btn" type="primary" @click="Synch_cloud">同步云星空</el-button>
                                        <div v-for="(item, index) in messages" :key="index"
                                            style="float: right; margin-right: 20px">
                                            {{ item }}
                                        </div>
                                    </div> -->
                                </el-header>
                                <el-main class="mainscss">
                                    <Table v-if="curIndex === 1" ref="tableRef" @rownotification="rownotification" :pageType="pageType"></Table>
                                    <Craft v-else-if="curIndex === 2" ref="craftRef" :key="craftKey" :pageType="pageType"></Craft>
                                    <pdfImg v-else-if="curIndex === 3" :key="componentKey" :pageType="pageType"></pdfImg>
                                    <Info v-else-if="curIndex === 4" :pageType="pageType"></Info>
                                </el-main>
                            </el-container>
                        </el-main>
                    </el-container>
                </el-main>
            </el-container>
            <div v-if="curIndex == 1 || curIndex == 2">
                <el-container style="background: #fff">
                    <VersinQuery @versinQuery="versinQuery" @reload="reload" ref="childRef" :pageType="pageType"></VersinQuery>
                </el-container>
            </div>
        </el-container>
        <div v-else-if="isShowA"></div>
    </div>
</template>

<script>
import Table from './tableTree.vue'
import Info from './infoTree.vue'
import Craft from './craftTree.vue'
import VersinQuery from './versinQueryTree.vue'
import pdfImg from './pdfImg.vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { mapState } from 'vuex'

export default {
    name: 'Product',
    components: {
        Table,
        Info,
        VersinQuery,
        Craft,
        pdfImg
    },
    props: {
        // 添加一个页面标识符属性，用于区分不同页面
        pageType: {
            type: String,
            default: 'default'
        }
    },
    data() {
        return {
            componentKey: 1,
            craftKey: 1,
            showButton:null,
            isShow: false, //页面显示
            isShowA: true,
            curIndex: 1, //面包屑处于下标
            count: 1,
            messages: [],
            // 产品
            derive: {
                label: 'BOM导出',
                filename:
                    '综合BOM表' +
                    new Date().getFullYear() +
                    '-' +
                    (new Date().getMonth() + 1) +
                    '-' +
                    new Date().getDate(),
                titleName: '综合BOM表',
                url: 'bom/get_detail',
                fileTypes: ['xlsx', 'pdf'],
                queryform: {
                    material_number: null
                },
                handleStr: [
                    {
                        name: 'crafts',
                        type: 'switchJson'
                    }
                ],
                columns: [
                    {
                        label: 'BOM属性',
                        type: 'tow',
                        children: [
                            {
                                label: '层号',
                                prop: ''
                            },
                            {
                                label: '父ID',
                                prop: ''
                            },
                            {
                                label: 'ZID',
                                prop: ''
                            },
                            {
                                label: '父编码',
                                prop: 'parent_number'
                            },
                            {
                                label: '子编码',
                                prop: 'number'
                            },
                            {
                                label: '单层BOM数量',
                                prop: 'dosage'
                            },
                            {
                                label: '总BOM数量',
                                prop: 'sum_dosage'
                            }
                        ]
                    },
                    {
                        label: '物料属性',
                        type: 'tow',
                        children: [
                            {
                                label: '科希盟编码',
                                prop: ''
                            },
                            {
                                label: '名称',
                                prop: 'title'
                            },
                            {
                                label: '文件夹名称',
                                prop: 'category_title'
                            },
                            {
                                label: '图号',
                                prop: 'drawing_number'
                            },
                            {
                                label: '规格',
                                prop: 'specs'
                            },
                            {
                                label: '材质',
                                prop: 'texture_title'
                            },
                            {
                                label: '单重',
                                prop: 'weight'
                            },
                            {
                                label: '单位',
                                prop: 'unit_title'
                            },
                            {
                                label: '工艺重量',
                                prop: ''
                            },
                            {
                                label: '零件类型',
                                prop: 'part_type_title'
                            },
                            {
                                label: '节点类型',
                                prop: 'type'
                            },
                            {
                                label: '库存',
                                prop: 'stock'
                            },
                            {
                                label: '库位',
                                prop: ''
                            },
                            {
                                label: '计划价',
                                prop: 'price'
                            }
                        ]
                    },
                    {
                        label: '工艺信息',
                        prop: 'crafts',
                        type: 'expand',
                        options: [
                            {
                                label: '卡片ID',
                                prop: ''
                            },
                            {
                                label: '代号',
                                prop: 'material_number'
                            },
                            {
                                label: '名称',
                                prop: ''
                            },
                            {
                                label: '图号',
                                prop: ''
                            },
                            {
                                label: '科希盟编码',
                                prop: ''
                            },
                            {
                                label: '状态',
                                prop: ''
                            },
                            {
                                label: '大版本',
                                prop: ''
                            },
                            {
                                label: '小版本',
                                prop: ''
                            },
                            {
                                label: '工序号',
                                prop: 'sort'
                            },
                            {
                                label: '工序名称',
                                prop: 'process_title'
                            },
                            {
                                label: '工序内容',
                                prop: 'process_content'
                            },
                            {
                                label: '机床代号',
                                prop: 'tool_code'
                            },
                            {
                                label: '工时',
                                prop: ''
                            },
                            {
                                label: '成本中心',
                                prop: ''
                            },
                            {
                                label: '费率',
                                prop: 'tool_hour_rate'
                            },
                            {
                                label: '单层BOM数量',
                                prop: ''
                            },
                            {
                                label: '多层BOM数量',
                                prop: ''
                            }
                        ]
                    }
                ]
            },
            // 物料用量汇总
            Summary: {
                label: '原材料用量汇总',
                filename:
                    '原材料用量汇总表' +
                    new Date().getFullYear() +
                    '-' +
                    (new Date().getMonth() + 1) +
                    '-' +
                    new Date().getDate(),
                titleName: '原材料用量汇总表',
                url: 'material/get_bom_raw',
                fileTypes: ['xlsx', 'pdf'],
                queryform: {
                    material_number: null
                },
                columns: [
                    {
                        label: '原材料编码',
                        prop: 'number'
                    },
                    {
                        label: '原材料名称',
                        prop: 'title'
                    },
                    {
                        label: '类型',
                        prop: 'type'
                    },
                    {
                        label: '图号',
                        prop: 'drawing_number'
                    },
                    {
                        label: '规格',
                        prop: 'specs'
                    },
                    {
                        label: '材质',
                        prop: 'texture_title'
                    },
                    {
                        label: '单重',
                        prop: 'weight'
                    },
                    {
                        label: '单位',
                        prop: 'unit_title'
                    },
                    {
                        label: '材料定额',
                        prop: 'quota'
                    },
                    {
                        label: '零件类型',
                        prop: 'part_type'
                    },

                    {
                        label: '计划价',
                        prop: 'price'
                    },
                    {
                        label: '标签',
                        prop: 'tags'
                    },
                    {
                        label: '库存',
                        prop: 'stock'
                    },
                    {
                        label: '总量',
                        prop: 'dosage'
                    }
                ]
            },
            // 清单明细
            detail: {
                label: '清单明细',
                filename:
                    '原材料用量清单明细表' +
                    new Date().getFullYear() +
                    '-' +
                    (new Date().getMonth() + 1) +
                    '-' +
                    new Date().getDate(),
                titleName: '原材料用量清单明细表',
                url: 'material/get_bom_ls',
                fileTypes: ['xlsx', 'pdf'],
                queryform: {
                    material_number: null
                },
                columns: [
                    {
                        label: '原材料编码',
                        prop: 'material_number'
                    },
                    {
                        label: '零件编码',
                        prop: 'parent_material_number'
                    },
                    {
                        label: '零件总数',
                        prop: 'dosage'
                    },
                    {
                        label: '零件数量',
                        prop: 'bom_dosage'
                    },
                    {
                        label: '零件名称',
                        prop: 'title'
                    },
                    {
                        label: '零件图号',
                        prop: 'drawing_number'
                    },
                    {
                        label: '原材料材质',
                        prop: 'texture_title'
                    },
                    {
                        label: '原材料规格',
                        prop: 'specs'
                    },
                    {
                        label: '单重',
                        prop: 'weight'
                    },
                    {
                        label: '零件父编码',
                        prop: 'part_parent_number'
                    }
                ]
            },
            // 零部件用量汇总
            caparts: {
                label: '零部件用量汇总',
                filename:
                    '零部件用量汇总' +
                    new Date().getFullYear() +
                    '-' +
                    (new Date().getMonth() + 1) +
                    '-' +
                    new Date().getDate(),
                titleName: '零部件用量汇总',
                url: 'material/get_bom_info',
                fileTypes: ['xlsx', 'pdf'],
                queryform: {
                    material_number: null
                },
                columns: [
                    {
                        label: '编码',
                        prop: 'number'
                    },
                    {
                        label: '名称',
                        prop: 'title'
                    },
                    {
                        label: '类型',
                        prop: 'type'
                    },
                    {
                        label: '图号',
                        prop: 'drawing_number'
                    },
                    {
                        label: '规格',
                        prop: 'specs'
                    },
                    {
                        label: '材质',
                        prop: 'texture_title'
                    },
                    {
                        label: '单重',
                        prop: 'weight'
                    },
                    {
                        label: '单位',
                        prop: 'unit_title'
                    },
                    {
                        label: '材料定额',
                        prop: 'quota'
                    },
                    {
                        label: '零件类型',
                        prop: 'part_type_title'
                    },
                    {
                        label: '计划价',
                        prop: 'price'
                    },
                    {
                        label: '标签',
                        prop: 'tags'
                    },
                    {
                        label: '库存',
                        prop: 'stock'
                    },
                    {
                        label: '总量',
                        prop: 'dosage'
                    }
                ]
            },
            part_type: '',
            types: ''
        }
    },
    computed: {
        ...mapState(['listObjTree'])
    },
    created() {
        try {
            const array = localStorage.getItem('PERMISSION')
            // console.log(JSON.parse(array).content.actions, '打印的权限节点')
            //判断是否可以下载
            JSON.parse(array).content.actions.map((item) => {
                console.log(item,'item>>>>>>');
                if (item.id == 11) {
                    item.acts.map((items) => {
                        if (items.title == '导出') {
                            // console.log('你来了吗??')
                            this.showButton = true
                        }
                    })
                }
                // } else if (item.id == 1) {
                //     item.acts.map((items) => {
                //         // console.log('走了吗?111111111111111111111111111', items)
                //         if (items.title == '删除') {
                //             // console.log('你来了吗??')
                //             this.deactivate = true
                //         }
                //     })
                // }
            })
            // 处理jsonData
        } catch (error) {
            console.error('解析JSON时出错:', error)
            // 错误处理，比如显示用户友好的消息或使用默认数据
        }
        if (this.$store.state.listObjArray) {
            this.curIndex = 1
        } else {
            this.$store.watch(
                (state) => state.arrayTress,
                (newValue) => {
                    this.isShowA = true
                    console.log(newValue, 'newValue')
                }
            )
            this.$store.watch(
                (state) => state.listObjTree,
                (newValue) => {
                    // 使用页面标识符来区分不同页面的数据
                    const storeKey = `${this.pageType}_listObjTree`;
                    const curIndexValue = sessionStorage.getItem(`handeValues_${this.pageType}`)

                    console.log(this.$store.state.listObjTree.items,'llllllllllllll12121212')
                    this.isShow = newValue.isShowTree
                    this.derive.queryform.material_number = this.$store.state.listObjTree.items.number
                    this.part_type = this.$store.state.listObjTree.items.part_type
                    this.types = this.$store.state.listObjTree.items.type

                    // 使用页面标识符来获取正确的数据
                    const materialNumberKey = `parent_material_number_${this.pageType}_${sessionStorage.getItem('windowId')}`;

                    // 如果sessionStorage中没有数据，则从store中获取并设置
                    if (!sessionStorage.getItem(materialNumberKey) && this.$store.state.listObjTree.items.number) {
                        // 设置当前页面的数据
                        sessionStorage.setItem(materialNumberKey, this.$store.state.listObjTree.items.number);
                        sessionStorage.setItem(`material_id_${this.pageType}_${sessionStorage.getItem('windowId')}`, this.$store.state.listObjTree.items.id);

                        // 清除其他页面的数据
                        if (this.pageType === 'openBom') {
                            sessionStorage.removeItem(`parent_material_number_pegging_${sessionStorage.getItem('windowId')}`);
                            sessionStorage.removeItem(`material_id_pegging_${sessionStorage.getItem('windowId')}`);
                        } else if (this.pageType === 'pegging') {
                            sessionStorage.removeItem(`parent_material_number_openBom_${sessionStorage.getItem('windowId')}`);
                            sessionStorage.removeItem(`material_id_openBom_${sessionStorage.getItem('windowId')}`);
                        }
                    }

                    this.Summary.queryform.material_number = sessionStorage.getItem(materialNumberKey)
                    this.detail.queryform.material_number = sessionStorage.getItem(materialNumberKey)
                    this.caparts.queryform.material_number = sessionStorage.getItem(materialNumberKey)

                    console.log(
                        this.$store.state.curIndexA,
                        sessionStorage.getItem(materialNumberKey),
                        'this.$store.state.curIndex'
                    )

                    // 对于openBom页面，强制设置curIndex为1（BOM清单）
                    if (this.pageType === 'openBom') {
                        // 确保store中的值也是1
                        const curIndexKey = `curIndexA_${this.pageType}`;
                        this.$store.state[curIndexKey] = 1;
                        this.$store.state.curIndexA = 1;
                        // 确保sessionStorage中的值也是1
                        sessionStorage.setItem(`handeValues_${this.pageType}`, "1");
                        this.curIndex = 1;
                    } else if(newValue.items.part_type==1 && Number(curIndexValue)==2){
                        this.curIndex = 1;
                    } else {
                        if (sessionStorage.getItem(`handeValues_${this.pageType}`)) {
                            // 使用页面标识符来获取正确的curIndex
                            const curIndexKey = `curIndexA_${this.pageType}`;
                            this.curIndex = this.$store.state[curIndexKey] || this.$store.state.curIndexA;

                            if (this.curIndex == 2) {
                                this.craftKey += 1;
                            }
                            if (this.curIndex == 3) {
                                this.componentKey += 1;
                            }
                        } else {
                            this.curIndex = 1;
                        }
                    }

                    if (this.curIndex == 1) {
                        this.$store.state.listObjTree.varApi = {
                            get_ls: 'bom_log/get_ls', //版本
                            get_contrast: 'bom_log/get_contrast' //对比
                        }
                    } else if (this.curIndex == 2) {
                        this.$store.state.listObjTree.varApi = {
                            get_ls: 'craft_log/get_ls', //版本
                            get_contrast: 'craft_log/get_contrast' //对比
                        }
                    }
                }
            )
        }

        if (this.$store.state.listObjTree) {
            // console.log('页面进入');
            this.isShow = this.$store.state.listObjTree.isShowTree

            // 使用页面标识符来获取正确的curIndex
            const curIndexKey = `curIndexA_${this.pageType}`;

            // 对于openBom、openBomIndex和pegging页面，强制设置curIndex为1（BOM清单）
            if (this.pageType === 'openBom' || this.pageType === 'openBomIndex' || this.pageType === 'pegging') {
                // 确保store中的值也是1
                this.$store.state[curIndexKey] = 1;
                this.$store.state.curIndexA = 1;
                // 确保sessionStorage中的值也是1
                sessionStorage.setItem(`handeValues_${this.pageType}`, "1");
                this.curIndex = 1;
            }
            // 如果没有设置当前页面的curIndex，则设置默认值为1
            else if (!sessionStorage.getItem(`handeValues_${this.pageType}`)) {
                sessionStorage.setItem(`handeValues_${this.pageType}`, "1");
                this.$store.state[curIndexKey] = 1;
                this.$store.state.curIndexA = 1;
                this.curIndex = 1;
            } else {
                const curIndexValue = this.$store.state[curIndexKey] || this.$store.state.curIndexA || 1;
                this.curIndex = curIndexValue;
            }

            if (this.curIndex == 1) {
                this.$store.state.listObjTree.varApi = {
                    get_ls: 'bom_log/get_ls', //版本
                    get_contrast: 'bom_log/get_contrast' //对比
                }
            } else if (this.curIndex == 2) {
                this.$store.state.listObjTree.varApi = {
                    get_ls: 'craft_log/get_ls', //版本
                    get_contrast: 'craft_log/get_contrast' //对比
                }
            }

            // 确保在页面加载时调用reload方法，加载数据
            this.$nextTick(() => {
                // 使用页面标识符来获取正确的数据
                const materialNumberKey = `parent_material_number_${this.pageType}_${sessionStorage.getItem('windowId')}`;

                // 如果当前页面没有数据，但store中有数据，则从store中获取
                if (!sessionStorage.getItem(materialNumberKey) &&
                    this.$store.state.listObjTree &&
                    this.$store.state.listObjTree.items &&
                    this.$store.state.listObjTree.items.number) {

                    // 设置当前页面的数据
                    sessionStorage.setItem(materialNumberKey, this.$store.state.listObjTree.items.number);
                    if (this.$store.state.listObjTree.items.id) {
                        sessionStorage.setItem(`material_id_${this.pageType}_${sessionStorage.getItem('windowId')}`, this.$store.state.listObjTree.items.id);
                    }

                    // 清除其他页面的数据
                    if (this.pageType === 'openBom') {
                        sessionStorage.removeItem(`parent_material_number_pegging_${sessionStorage.getItem('windowId')}`);
                        sessionStorage.removeItem(`material_id_pegging_${sessionStorage.getItem('windowId')}`);
                        sessionStorage.removeItem(`parent_material_number_openBomIndex_${sessionStorage.getItem('windowId')}`);
                        sessionStorage.removeItem(`material_id_openBomIndex_${sessionStorage.getItem('windowId')}`);
                    } else if (this.pageType === 'pegging') {
                        sessionStorage.removeItem(`parent_material_number_openBom_${sessionStorage.getItem('windowId')}`);
                        sessionStorage.removeItem(`material_id_openBom_${sessionStorage.getItem('windowId')}`);
                        sessionStorage.removeItem(`parent_material_number_openBomIndex_${sessionStorage.getItem('windowId')}`);
                        sessionStorage.removeItem(`material_id_openBomIndex_${sessionStorage.getItem('windowId')}`);
                    } else if (this.pageType === 'openBomIndex') {
                        sessionStorage.removeItem(`parent_material_number_openBom_${sessionStorage.getItem('windowId')}`);
                        sessionStorage.removeItem(`material_id_openBom_${sessionStorage.getItem('windowId')}`);
                        sessionStorage.removeItem(`parent_material_number_pegging_${sessionStorage.getItem('windowId')}`);
                        sessionStorage.removeItem(`material_id_pegging_${sessionStorage.getItem('windowId')}`);
                    }
                }

                // 无论如何都调用reload方法，即使没有数据也会尝试加载
                this.reload();
            });
        }
    },
    mounted() { },
    methods: {
        rownotification(op) {
            this.$emit('drawer', op)
        },
        versinQuery(params) {
            console.log('e2131231-->', params)
            if (params == false) {
                this.$emit('drawer', params)
            }
        },
        reload() {
            // 使用页面标识符来获取正确的数据
            const materialNumberKey = `parent_material_number_${this.pageType}_${sessionStorage.getItem('windowId')}`;
            const materialNumber = sessionStorage.getItem(materialNumberKey);

            // 如果没有找到当前页面的数据，尝试从store中获取
            if (!materialNumber && this.$store.state.listObjTree && this.$store.state.listObjTree.items) {
                const number = this.$store.state.listObjTree.items.number;
                if (number) {
                    // 设置当前页面的数据
                    sessionStorage.setItem(materialNumberKey, number);
                    if (this.$store.state.listObjTree.items.id) {
                        sessionStorage.setItem(`material_id_${this.pageType}_${sessionStorage.getItem('windowId')}`, this.$store.state.listObjTree.items.id);
                    }

                    // 清除其他页面的数据
                    if (this.pageType === 'openBom') {
                        sessionStorage.removeItem(`parent_material_number_pegging_${sessionStorage.getItem('windowId')}`);
                        sessionStorage.removeItem(`material_id_pegging_${sessionStorage.getItem('windowId')}`);
                        sessionStorage.removeItem(`parent_material_number_openBomIndex_${sessionStorage.getItem('windowId')}`);
                        sessionStorage.removeItem(`material_id_openBomIndex_${sessionStorage.getItem('windowId')}`);
                    } else if (this.pageType === 'pegging') {
                        sessionStorage.removeItem(`parent_material_number_openBom_${sessionStorage.getItem('windowId')}`);
                        sessionStorage.removeItem(`material_id_openBom_${sessionStorage.getItem('windowId')}`);
                        sessionStorage.removeItem(`parent_material_number_openBomIndex_${sessionStorage.getItem('windowId')}`);
                        sessionStorage.removeItem(`material_id_openBomIndex_${sessionStorage.getItem('windowId')}`);
                    } else if (this.pageType === 'openBomIndex') {
                        sessionStorage.removeItem(`parent_material_number_openBom_${sessionStorage.getItem('windowId')}`);
                        sessionStorage.removeItem(`material_id_openBom_${sessionStorage.getItem('windowId')}`);
                        sessionStorage.removeItem(`parent_material_number_pegging_${sessionStorage.getItem('windowId')}`);
                        sessionStorage.removeItem(`material_id_pegging_${sessionStorage.getItem('windowId')}`);
                    }
                }
            }

            // 再次获取materialNumber，因为可能已经在上面的代码中设置了
            const updatedMaterialNumber = sessionStorage.getItem(materialNumberKey);

            // 如果仍然没有materialNumber，则不调用API，避免报错
            if (!updatedMaterialNumber) {
                console.log(`No material_number found for ${this.pageType}, skipping API calls`);
                return;
            }

            if (this.curIndex == 1) {
                console.log(`Calling bom/get_material with material_number: ${updatedMaterialNumber} for page ${this.pageType}`);
                this.$refs.tableRef.indata('bom/get_material', {
                    material_number: updatedMaterialNumber
                })
            } else if (this.curIndex == 2 && this.$refs.craftRef) {
                console.log(`Calling craft/get_detail with material_number: ${updatedMaterialNumber} for page ${this.pageType}`);
                this.$refs.craftRef.indata('craft/get_detail', {
                    material_number: updatedMaterialNumber
                })
            }
        },
        // 面包屑切换
        handleSelect(e) {
            console.log('e2131231-->', e)
            this.curIndex = e.paneName

            // 使用页面标识符来存储数据
            sessionStorage.setItem(`handeValues_${this.pageType}`, this.curIndex)

            // 使用页面标识符来设置curIndex
            const curIndexKey = `curIndexA_${this.pageType}`;

            // 确保在切换到工艺卡页面时，工艺卡组件能够获取到物料编号
            if (this.curIndex === 2) { // 工艺卡页面
                // 获取当前页面的物料编号
                const windowId = sessionStorage.getItem('windowId') || '';
                const materialNumberKey = `parent_material_number_${this.pageType}_${windowId}`;
                const materialNumber = sessionStorage.getItem(materialNumberKey);

                if (materialNumber) {
                    console.log(`切换到工艺卡页面，当前物料编号: ${materialNumber}`);

                    // 确保工艺卡页面可以获取到物料编号
                    const craftMaterialNumberKey = `parent_material_number_craft_${windowId}`;
                    sessionStorage.setItem(craftMaterialNumberKey, materialNumber);

                    // 设置特定页面的curIndex
                    this.$store.state[curIndexKey] = 2;
                    this.$store.state.curIndexA = 2;
                    this.$store.state.listObjTree.varApi = {
                        get_ls: 'craft_log/get_ls', //版本
                        get_contrast: 'craft_log/get_contrast' //对比
                    }
                } else {
                    console.error(`切换到工艺卡页面，但无法获取物料编号`);
                    ElMessage.warning('无法获取物料编号，可能影响工艺卡显示');

                    // 尝试从其他页面获取物料编号
                    let foundMaterialNumber = null;

                    // 尝试从openBom页面获取
                    const openBomKey = `parent_material_number_openBom_${windowId}`;
                    foundMaterialNumber = sessionStorage.getItem(openBomKey);

                    // 如果仍然没有，尝试从pegging页面获取
                    if (!foundMaterialNumber) {
                        const peggingKey = `parent_material_number_pegging_${windowId}`;
                        foundMaterialNumber = sessionStorage.getItem(peggingKey);
                    }

                    // 如果仍然没有，尝试从openBomIndex页面获取
                    if (!foundMaterialNumber) {
                        const openBomIndexKey = `parent_material_number_openBomIndex_${windowId}`;
                        foundMaterialNumber = sessionStorage.getItem(openBomIndexKey);
                    }

                    // 如果找到了物料编号，将其保存到当前页面的键中
                    if (foundMaterialNumber) {
                        console.log(`从其他页面找到物料编号: ${foundMaterialNumber}`);
                        sessionStorage.setItem(materialNumberKey, foundMaterialNumber);

                        // 确保工艺卡页面可以获取到物料编号
                        const craftMaterialNumberKey = `parent_material_number_craft_${windowId}`;
                        sessionStorage.setItem(craftMaterialNumberKey, foundMaterialNumber);
                    }

                    // 设置特定页面的curIndex
                    this.$store.state[curIndexKey] = 2;
                    this.$store.state.curIndexA = 2;
                    this.$store.state.listObjTree.varApi = {
                        get_ls: 'craft_log/get_ls', //版本
                        get_contrast: 'craft_log/get_contrast' //对比
                    }
                }
            } else if (this.curIndex === 1) { // BOM清单页面
                // 设置特定页面的curIndex
                this.$store.state[curIndexKey] = 1;
                this.$store.state.curIndexA = 1;
                this.$store.state.listObjTree.varApi = {
                    get_ls: 'bom_log/get_ls', //版本
                    get_contrast: 'bom_log/get_contrast' //对比
                }
            }

            this.$forceUpdate()
        },
        Synch_cloud() {
            const loading = this.$loading({
                lock: true,
                text: '同步中',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            })

            // 使用页面标识符来获取正确的数据
            const materialNumberKey = `parent_material_number_${this.pageType}_${sessionStorage.getItem('windowId')}`;
            const materialNumber = sessionStorage.getItem(materialNumberKey);

            // 如果没有找到当前页面的数据，尝试从store中获取
            if (!materialNumber && this.$store.state.listObjTree && this.$store.state.listObjTree.items) {
                const number = this.$store.state.listObjTree.items.number;
                if (number) {
                    // 设置当前页面的数据
                    sessionStorage.setItem(materialNumberKey, number);
                }
            }

            let page = { material_number: sessionStorage.getItem(materialNumberKey) }

            if (!page.material_number) {
                loading.close();
                ElMessage.error('缺少物料编号，无法同步');
                return;
            }

            this.$HTTP.get('material/post_sync_kingdee', page).then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    this.$notify({
                        title: '云星空同步',
                        message: '操作成功 ',
                        type: 'success',
                        duration: 2000
                    })
                    this.messages.push(`同步成功${this.count}次`)
                    this.count++
                    loading.close()
                    this.objlist = res.result
                    this.pictLoading = false
                }
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.mainscss {
    padding: 15px 0 0 0;
    margin: 0;
}

.dropdown_menu {
    margin: 0 10px;
}

.common-layout {
    width: 100%;
    height: 100%;

    .product-tree {
        background-color: #fff;

        .keyword {
            padding: 8px 5px;
            height: 50px;
        }

        .nav-menu-item {
            height: 26px;
            font-size: 14px;
        }
    }

    .header {
        height: 36px;
        background: none;
        border: none;
    }

    .tabs-content {
        width: 100%;
        // margin-right: 20px;
        background-color: #fff;

        .content-header {
            border: none;
            height: 50px;

            .tabs {
                width: 50%;

                .el-menu-demo {
                    // width: 00px !important;
                    height: 30px;
                }
            }

            .btns {
                width: 30%;

                :nth-child(1) {
                    display: inline-block;
                }

                ::v-deep .el-button {
                    margin-left: 12px !important;
                }
            }
        }
    }

    .version-query {
        background-color: #fff;
        border: none;
        // width: 180px;
    }
}
</style>
