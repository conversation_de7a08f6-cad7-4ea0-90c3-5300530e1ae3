<template>
    <div class="common-layout">
        <el-container v-if="isShow">
            <el-container>
                <el-main class="mainscss">
                    <el-container>
                        <el-main class="tabs-content">
                            <el-container>
                                <el-header class="content-header">
                                    <div class="tabs">
                                        <!-- <el-menu
                                            :default-active="curIndex"
                                            class="el-menu-demo"
                                            @select="handleSelect"
                                        >
                                            <el-menu-item index="1">BOM清单</el-menu-item>
                                            <el-menu-item
                                                index="2"
                                                v-if="!(types == 3 || ((types == 1 || types == 2) && part_type == 1))"
                                                >工艺卡</el-menu-item
                                            >
                                            <el-menu-item index="3">图纸</el-menu-item>
                                            <el-menu-item index="4">详情</el-menu-item>
                                        </el-menu> -->
                                        <template
                                            v-if="!(types == 3 || ((types == 1 || types == 2) && part_type == 1))">
                                            <el-tabs v-model="curIndex" @tab-click="handleSelect">
                                                <el-tab-pane label="BOM清单" :name="1"></el-tab-pane>
                                                <el-tab-pane label="工艺卡" :name="2"></el-tab-pane>
                                                <el-tab-pane label="图纸" :name="3"></el-tab-pane>
                                                <el-tab-pane label="详情" :name="4"></el-tab-pane>
                                            </el-tabs>
                                        </template>
                                        <!-- <template
                                            v-if="part_type == 0">
                                            <el-tabs v-model="curIndex" @tab-click="handleSelect">
                                                <el-tab-pane label="BOM清单" :name="1"></el-tab-pane>
                                                <el-tab-pane label="工艺卡" :name="2"></el-tab-pane>
                                                <el-tab-pane label="图纸" :name="3"></el-tab-pane>
                                                <el-tab-pane label="详情" :name="4"></el-tab-pane>
                                            </el-tabs>
                                        </template> -->
                                        <template v-else>
                                            <el-tabs v-model="curIndex" @tab-click="handleSelect">
                                                <el-tab-pane label="BOM清单" :name="1"></el-tab-pane>
                                                <el-tab-pane label="图纸" :name="3"></el-tab-pane>
                                                <el-tab-pane label="详情" :name="4"></el-tab-pane>
                                            </el-tabs>
                                        </template>
                                    </div>
                                    <div class="btns" v-if="curIndex === 1">
                                        <el-button type="primary" @click="syncKingdee(1)">
                                            同步金蝶
                                        </el-button>
                                        <el-dropdown trigger="click" v-show="showButton">
                                            <el-button class="el-dropdown-link" icon="el-icon-arrow-down">
                                                按钮菜单
                                            </el-button>
                                            <template #dropdown>
                                                <el-dropdown-menu class="dropdown_menu">
                                                    <yy_export v-if="derive" :url="derive.url" :label="derive.label"
                                                        :fileName="derive.filename" showData :column="derive.columns"
                                                        :fileTypes="['xlsx']" :dynamicColumns="derive.dynamicColumns"
                                                        :query="derive.queryform" :showsummary="derive.showsummary"
                                                        :data="derive.data" :handleStr="derive.handleStr"></yy_export>
                                                    <yy_exportmaterial v-if="Summary" :url="Summary.url"
                                                        :label="Summary.label" :fileName="Summary.filename" showData
                                                        :column="Summary.columns" :fileTypes="['xlsx']"
                                                        :dynamicColumns="Summary.dynamicColumns"
                                                        :query="Summary.queryform" :showsummary="Summary.showsummary"
                                                        :data="Summary.data" :handleStr="Summary.handleStr">
                                                    </yy_exportmaterial>
                                                    <!-- <yy_exportP
                                                        v-if="detail"
                                                        :url="detail.url"
                                                        :label="detail.label"
                                                        :fileName="detail.filename"
                                                        showData
                                                        :column="detail.columns"
                                                        :fileTypes="['xlsx']"
                                                        :dynamicColumns="detail.dynamicColumns"
                                                        :query="detail.queryform"
                                                        :showsummary="detail.showsummary"
                                                        :data="detail.data"
                                                        :handleStr="detail.handleStr"
                                                    ></yy_exportP> -->
                                                    <yy_exportP v-if="caparts" :url="caparts.url" :label="caparts.label"
                                                        :fileName="caparts.filename" showData :column="caparts.columns"
                                                        :fileTypes="['xlsx']" :dynamicColumns="caparts.dynamicColumns"
                                                        :query="caparts.queryform" :showsummary="caparts.showsummary"
                                                        :data="caparts.data" :handleStr="caparts.handleStr">
                                                    </yy_exportP>
                                                </el-dropdown-menu>
                                            </template>
                                        </el-dropdown>
                                        <!-- <el-button
                                            class="btn"
                                            type="primary"
                                            icon="el-icon-plus"
                                            @click="onSubmit"
                                            >添加</el-button
                                        > -->
                                        <!-- <el-button
                                            class="btn"
                                            type="info"
                                            plain
                                            icon="el-icon-Delete"
                                            >停用</el-button
                                        > -->
                                    </div>
                                    <div class="btns" v-if="curIndex === 2">
                                        <el-button type="primary" @click="syncKingdee(2)">
                                            同步金蝶
                                        </el-button>
                                    </div>
                                </el-header>
                                <el-main class="mainscss" >
                                    <Table v-if="curIndex === 1" ref="tableRef" @rownotification="rownotification">
                                    </Table>
                                    <Craft v-else-if="curIndex === 2" ref="craftRef" :key="craftKey"></Craft>
                                    <pdfImg v-else-if="curIndex === 3" :key="componentKey"></pdfImg>
                                    <Info v-else-if="curIndex === 4"></Info>
                                </el-main>
                            </el-container>
                        </el-main>
                    </el-container>
                </el-main>
            </el-container>
            <div v-if="curIndex == 1 || curIndex == 2">
                <el-container style="background: #fff">
                    <VersinQuery @versinQuery="versinQuery" @reload="reload"></VersinQuery>
                </el-container>
            </div>
        </el-container>
        <div v-else-if="isShowA"></div>
    </div>
</template>

<script>
import Table from './table.vue'
import Info from './info.vue'
import Craft from './craft.vue'
import VersinQuery from './versinQuery.vue'
import pdfImg from './pdfImgindex.vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { mapState } from 'vuex'

export default {
    name: 'Product',
    components: {
        Table,
        Info,
        VersinQuery,
        Craft,
        pdfImg
    },
    data() {
        return {
            componentKey: 1,
            showButton: null,
            craftKey: 1,
            isCloud: false,
            isShow: false, //页面显示
            isShowA: true,
            curIndex: 1, //面包屑处于下标
            count: 1,
            messages: [],
            // 产品
            derive: {
                label: 'BOM导出',
                filename:
                    '综合BOM表' +
                    new Date().getFullYear() +
                    '-' +
                    (new Date().getMonth() + 1) +
                    '-' +
                    new Date().getDate(),
                titleName: '综合BOM表',
                url: 'bom/get_detail',
                fileTypes: ['xlsx', 'pdf'],
                queryform: {
                    material_number: null
                },
                handleStr: [
                    {
                        name: 'crafts',
                        type: 'switchJson'
                    }
                ],
                columns: [
                    {
                        label: 'BOM属性',
                        type: 'tow',
                        children: [
                            {
                                label: '层号',
                                prop: ''
                            },
                            {
                                label: '父ID',
                                prop: ''
                            },
                            {
                                label: 'ZID',
                                prop: ''
                            },
                            {
                                label: '父编码',
                                prop: 'parent_number'
                            },
                            {
                                label: '子编码',
                                prop: 'number'
                            },
                            {
                                label: '单层BOM数量',
                                prop: 'dosage'
                            },
                            {
                                label: '总BOM数量',
                                prop: 'sum_dosage'
                            }
                        ]
                    },
                    {
                        label: '物料属性',
                        type: 'tow',
                        children: [
                            {
                                label: '科希盟编码',
                                prop: ''
                            },
                            {
                                label: '名称',
                                prop: 'title'
                            },
                            {
                                label: '文件夹名称',
                                prop: 'category_title'
                            },
                            {
                                label: '图号',
                                prop: 'drawing_number'
                            },
                            {
                                label: '规格',
                                prop: 'specs'
                            },
                            {
                                label: '材质',
                                prop: 'texture_title'
                            },
                            {
                                label: '单重',
                                prop: 'weight'
                            },
                            {
                                label: '单位',
                                prop: 'unit_title'
                            },
                            {
                                label: '工艺重量',
                                prop: ''
                            },
                            {
                                label: '零件类型',
                                prop: 'part_type_title'
                            },
                            {
                                label: '节点类型',
                                prop: 'type'
                            },
                            {
                                label: '库存',
                                prop: 'stock'
                            },
                            {
                                label: '库位',
                                prop: ''
                            },
                            {
                                label: '计划价',
                                prop: 'price'
                            }
                        ]
                    },
                    {
                        label: '工艺信息',
                        prop: 'crafts',
                        type: 'expand',
                        options: [
                            {
                                label: '卡片ID',
                                prop: ''
                            },
                            {
                                label: '代号',
                                prop: 'material_number'
                            },
                            {
                                label: '名称',
                                prop: ''
                            },
                            {
                                label: '图号',
                                prop: ''
                            },
                            {
                                label: '科希盟编码',
                                prop: ''
                            },
                            {
                                label: '状态',
                                prop: ''
                            },
                            {
                                label: '大版本',
                                prop: ''
                            },
                            {
                                label: '小版本',
                                prop: ''
                            },
                            {
                                label: '工序号',
                                prop: 'sort'
                            },
                            {
                                label: '工序名称',
                                prop: 'process_title'
                            },
                            {
                                label: '工序内容',
                                prop: 'process_content'
                            },
                            {
                                label: '机床代号',
                                prop: 'tool_code'
                            },
                            {
                                label: '工时',
                                prop: ''
                            },
                            {
                                label: '成本中心',
                                prop: ''
                            },
                            {
                                label: '费率',
                                prop: 'tool_hour_rate'
                            },
                            {
                                label: '单层BOM数量',
                                prop: ''
                            },
                            {
                                label: '多层BOM数量',
                                prop: ''
                            }
                        ]
                    }
                ]
            },
            // 物料用量汇总
            Summary: {
                label: '原材料用量汇总',
                filename:
                    '原材料用量汇总表' +
                    new Date().getFullYear() +
                    '-' +
                    (new Date().getMonth() + 1) +
                    '-' +
                    new Date().getDate(),
                titleName: '原材料用量汇总表',
                url: 'material/get_bom_raw',
                fileTypes: ['xlsx', 'pdf'],
                queryform: {
                    material_number: null
                },
                columns: [
                    {
                        label: '原材料编码',
                        prop: 'number'
                    },
                    {
                        label: '原材料名称',
                        prop: 'title'
                    },
                    {
                        label: '类型',
                        prop: 'type'
                    },
                    {
                        label: '图号',
                        prop: 'drawing_number'
                    },
                    {
                        label: '规格',
                        prop: 'specs'
                    },
                    {
                        label: '材质',
                        prop: 'texture_title'
                    },
                    {
                        label: '单重',
                        prop: 'weight'
                    },
                    {
                        label: '单位',
                        prop: 'unit_title'
                    },
                    {
                        label: '材料定额',
                        prop: 'quota'
                    },
                    {
                        label: '零件类型',
                        prop: 'part_type'
                    },

                    {
                        label: '计划价',
                        prop: 'price'
                    },
                    {
                        label: '标签',
                        prop: 'tags'
                    },
                    {
                        label: '库存',
                        prop: 'stock'
                    },
                    {
                        label: '总量',
                        prop: 'dosage'
                    }
                ]
            },
            // 清单明细
            detail: {
                label: '清单明细',
                filename:
                    '原材料用量清单明细表' +
                    new Date().getFullYear() +
                    '-' +
                    (new Date().getMonth() + 1) +
                    '-' +
                    new Date().getDate(),
                titleName: '原材料用量清单明细表',
                url: 'material/get_bom_ls',
                fileTypes: ['xlsx', 'pdf'],
                queryform: {
                    material_number: null
                },
                columns: [
                    {
                        label: '原材料编码',
                        prop: 'material_number'
                    },
                    {
                        label: '零件编码',
                        prop: 'parent_material_number'
                    },
                    {
                        label: '零件总数',
                        prop: 'dosage'
                    },
                    {
                        label: '零件数量',
                        prop: 'bom_dosage'
                    },
                    {
                        label: '零件名称',
                        prop: 'title'
                    },
                    {
                        label: '零件图号',
                        prop: 'drawing_number'
                    },
                    {
                        label: '原材料材质',
                        prop: 'texture_title'
                    },
                    {
                        label: '原材料规格',
                        prop: 'specs'
                    },
                    {
                        label: '单重',
                        prop: 'weight'
                    },
                    {
                        label: '零件父编码',
                        prop: 'part_parent_number'
                    }
                ]
            },
            // 零部件用量汇总
            caparts: {
                label: '零部件用量汇总',
                filename:
                    '零部件用量汇总' +
                    new Date().getFullYear() +
                    '-' +
                    (new Date().getMonth() + 1) +
                    '-' +
                    new Date().getDate(),
                titleName: '零部件用量汇总',
                url: 'material/get_bom_info',
                fileTypes: ['xlsx', 'pdf'],
                queryform: {
                    material_number: null
                },
                columns: [
                    {
                        label: '编码',
                        prop: 'number'
                    },
                    {
                        label: '名称',
                        prop: 'title'
                    },
                    {
                        label: '类型',
                        prop: 'type'
                    },
                    {
                        label: '图号',
                        prop: 'drawing_number'
                    },
                    {
                        label: '规格',
                        prop: 'specs'
                    },
                    {
                        label: '材质',
                        prop: 'texture_title'
                    },
                    {
                        label: '单重',
                        prop: 'weight'
                    },
                    {
                        label: '单位',
                        prop: 'unit_title'
                    },
                    {
                        label: '材料定额',
                        prop: 'quota'
                    },
                    {
                        label: '零件类型',
                        prop: 'part_type_title'
                    },
                    {
                        label: '计划价',
                        prop: 'price'
                    },
                    {
                        label: '标签',
                        prop: 'tags'
                    },
                    {
                        label: '库存',
                        prop: 'stock'
                    },
                    {
                        label: '总量',
                        prop: 'dosage'
                    }
                ]
            },
            part_type: '',
            types: ''
        }
    },
    computed: {
        ...mapState(['listObj'])
    },
    // watch: {
    //     curIndex(newValue, oldValue) {
    //         if (newValue == 2 && oldValue == 3) {
    //             if (newValue == 1) {
    //             this.$store.state.listObj.varApi = {
    //                 get_ls: 'bom_log/get_ls', //版本
    //                 get_contrast: 'bom_log/get_contrast' //对比
    //             }
    //         } else if (newValue == 2) {
    //             this.$store.state.listObj.varApi = {
    //                 get_ls: 'craft_log/get_ls', //版本
    //                 get_contrast: 'craft_log/get_contrast' //对比
    //             }
    //         }
    //         }
    //     },
    // },
    created() {
        try {
            const array = localStorage.getItem('PERMISSION')
            // console.log(JSON.parse(array).content.actions, '打印的权限节点')
            //判断是否可以下载
            JSON.parse(array).content.actions.map((item) => {
                // console.log(item,'item>>>>>>');
                if (item.id == 11) {
                    item.acts.map((items) => {
                        if (items.title == '导出') {
                            // console.log('你来了吗??')
                            this.showButton = true
                        }
                    })
                }
                // } else if (item.id == 1) {
                //     item.acts.map((items) => {
                //         // console.log('走了吗?111111111111111111111111111', items)
                //         if (items.title == '删除') {
                //             // console.log('你来了吗??')
                //             this.deactivate = true
                //         }
                //     })
                // }
            })
            // 处理jsonData
        } catch (error) {
            console.error('解析JSON时出错:', error)
            // 错误处理，比如显示用户友好的消息或使用默认数据
        }
        if (this.$store.state.listObjArray) {
            this.curIndex = 1
        } else {
            this.$store.watch(
                (state) => state.arrayTress,
                (newValue) => {
                    this.isShowA = true
                    console.log(newValue, 'newValue')
                }
            )
            this.$store.watch(
                (state) => state.listObj,
                (newValue) => {
                    // alert(newValue)
                    console.log(this.$store.state.listObj,7778788);
                    const curIndexValue = sessionStorage.getItem('handeValue')
                    // this.$store.state.curIndex = 1
                    this.isShow = newValue.isShow
                    this.derive.queryform.material_number = this.$store.state.listObj.items.number
                    this.part_type = this.$store.state.listObj.items.part_type
                    this.types = this.$store.state.listObj.items.type
                    this.Summary.queryform.material_number = sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`)
                    this.detail.queryform.material_number = sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`)
                    this.caparts.queryform.material_number = sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`)
                    console.log(
                        this.$store.state.curIndex,
                        sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`),
                        'this.$store.state.curIndex'
                    )
                    if (newValue.items.part_type == 1 && Number(curIndexValue) == 2) {
                        this.curIndex = 1
                    } else {
                        if (sessionStorage.getItem('handeValue')) {
                            this.curIndex = this.$store.state.curIndex
                            if (this.curIndex == 2) {
                                this.craftKey += 1;
                            }
                            if (this.curIndex == 3) {

                                this.componentKey += 1;
                            }
                        } else {
                            this.curIndex = 1
                        }

                    }
                    if (this.curIndex == 1) {
                        this.$store.state.listObj.varApi = {
                            get_ls: 'bom_log/get_ls', //版本
                            get_contrast: 'bom_log/get_contrast' //对比
                        }
                    } else if (this.curIndex == 2) {
                        this.$store.state.listObj.varApi = {
                            get_ls: 'craft_log/get_ls', //版本
                            get_contrast: 'craft_log/get_contrast' //对比
                        }
                    }
                }
            )
        }

        // if (this.$store.state.listObj) {
        //     // console.log('页面进入');
        //     this.isShow = this.$store.state.listObj.isShow
        //     if (this.curIndex == 1) {
        //         this.$store.state.listObj.varApi = {
        //             get_ls: 'bom_log/get_ls', //版本
        //             get_contrast: 'bom_log/get_contrast' //对比
        //         }
        //     } else if (this.curIndex == 2) {
        //         this.$store.state.listObj.varApi = {
        //             get_ls: 'craft_log/get_ls', //版本
        //             get_contrast: 'craft_log/get_contrast' //对比
        //         }
        //     }
        // }
    },
    mounted() {
    },
    methods: {
        rownotification(op) {
            this.$emit('drawer', op)
        },
        versinQuery(params) {
            console.log('e2131231-->', params)
            if (params == false) {
                this.$emit('drawer', params)
            }
        },
        reload() {
            if (this.curIndex == 1) {
                this.$refs.tableRef.indata('bom/get_material', { material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`) })
            } else {
                this.$refs.craftRef.indata('craft/get_detail', {
                    material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`)
                })
            }

        },
        //同步金蝶
        syncKingdee(type) {
            const loading = this.$loading({
                lock: true,
                text: '同步中',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            })
            //bom同步金蝶
            if(type==1){
                this.$HTTP.post('bom/post_sync_kingdee', { material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`) }).then((res) => {
                    console.log(res)
                    res.errcode == 0 ? ElMessage.success('同步成功') : ElMessage.error(res.errmsg)
                    loading.close()
                }).catch((err) => {
                    ElMessage.error('同步失败')
                })
            }else{
                //工艺卡同步金蝶
                this.$HTTP.post('craft/post_sync_kingdee', { material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`) }).then((res) => {
                    console.log(res)
                    res.errcode == 0 ? ElMessage.success('同步成功') : ElMessage.error(res.errmsg)
                    loading.close()
                }).catch((err) => {
                    ElMessage.error('同步失败')
                })
            }
        },
        // 面包屑切换
        handleSelect(e) {
            console.log('e2131231-->', e)
            this.curIndex = e.paneName
            sessionStorage.setItem('handeValue', this.curIndex)
            if (this.curIndex === 1) {
                this.$store.state.curIndex = 1
                this.$store.state.listObj.varApi = {
                    get_ls: 'bom_log/get_ls', //版本
                    get_contrast: 'bom_log/get_contrast' //对比
                }
            } else if (this.curIndex === 2) {
                this.$store.state.curIndex = 2
                this.$store.state.listObj.varApi = {
                    get_ls: 'craft_log/get_ls', //版本
                    get_contrast: 'craft_log/get_contrast' //对比
                }
            }
            this.$forceUpdate()
        },
        Synch_cloud() {
            const loading = this.$loading({
                lock: true,
                text: '同步中',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            })
            let page = { material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`) }
            this.$HTTP.get('material/post_sync_kingdee', page).then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    this.$notify({
                        title: '云星空同步',
                        message: '操作成功 ',
                        type: 'success',
                        duration: 2000
                    })
                    this.messages.push(`同步成功${this.count}次`)
                    this.count++
                    loading.close()
                    this.objlist = res.result
                    this.pictLoading = false
                }
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.mainscss {
    padding: 15px 0 0 0;
    margin: 0;
}

.dropdown_menu {
    margin: 0 10px;
}

.common-layout {
    width: 100%;
    height: 100%;

    .product-tree {
        background-color: #fff;

        .keyword {
            padding: 8px 5px;
            height: 50px;
        }

        .nav-menu-item {
            height: 26px;
            font-size: 14px;
        }
    }

    .header {
        height: 36px;
        background: none;
        border: none;
    }

    .tabs-content {
        width: 100%;
        // margin-right: 20px;
        background-color: #fff;

        .content-header {
            border: none;
            height: 50px;

            .tabs {
                width: 50%;

                .el-menu-demo {
                    // width: 00px !important;
                    height: 30px;
                }
            }

            .btns {
                width: 30%;

                :nth-child(1) {
                    display: inline-block;
                }

                ::v-deep .el-button {
                    margin-left: 12px !important;
                }
            }
        }
    }

    .version-query {
        background-color: #fff;
        border: none;
        // width: 180px;
    }
}
</style>
