<template>
    <el-container>
        <el-header>
            <el-page-header @back="goBack" :content="title">
            </el-page-header>
        </el-header>
        <el-main class="nopadding" v-loading="pictLoading"  v-if="type==0">
            <el-table :data="BOMtableData" stripe style="width: 100%">
                <template #empty>
                    <el-empty description="暂无数据" :image-size="200"></el-empty>
                </template>
                <!-- <el-table-column prop="id" label="ID" width="80" /> -->
                <el-table-column prop="number" label="代号" min-width="120" />
                <el-table-column prop="title" label="名称" min-width="120">
                </el-table-column>
                <!-- <el-table-column prop="drawing_number" label="图号" min-width="120" /> -->
                <!-- <el-table-column prop="specs" label="规格" min-width="130" /> -->
                <!-- <el-table-column prop="texture_title" label="材质" min-width="100" /> -->
                <el-table-column prop="dosage" label="用量" min-width="100" />
            </el-table>
        </el-main>
        <el-main class="nopadding" v-loading="pictLoading" v-else>
            <el-table :data="tableData" stripe style="width: 100%">
                <template #empty>
                    <el-empty description="暂无数据" :image-size="200"></el-empty>
                </template>
                <el-table-column prop="material_number" label="编码" min-width="120" />
                <el-table-column prop="process_title" label="工序名称" min-width="120">
                </el-table-column>
                <el-table-column prop="process_content" label="工序内容" min-width="150" />
                <el-table-column prop="tool_code" label="机床代号" min-width="130" />
                <!-- <el-table-column prop="tool_title" label="机床名称" min-width="100" /> -->
                <el-table-column prop="unit_hour" label="单件工时" min-width="100" />
            </el-table>
        </el-main>
    </el-container>
</template>
<script setup>
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import http from '@/utils/request';
let type = ref(1)
let router = useRouter();
let route = useRoute();
let pictLoading = ref(false);
let BOMtableData=ref([])
let tableData=ref([])
let id=ref(null)
function goBack() {
    router.push({ path: "/index" });
}
let title=ref('BOM未发布版本对比')
let query=route.query
type.value=query.type
// id.value=query.id
id.value=101
if(type.value==1){
    title.value='工艺卡未发布版本对比'
}else{
    title.value='BOM未发布版本对比'
}
function getBom(){
    pictLoading.value=true
    if(type.value==0){
        http.get("bom_log/get_detail",{id:id.value}).then((res)=>{
            console.log(res,9999)
            if(res.errcode==0){
                BOMtableData.value=res.result
            }
            console.log(BOMtableData.value,9999)
            console.log(type.value,2222)
            pictLoading.value=false
        }).catch(err=>{
            pictLoading.value=false
        })
    }else{
        http.get("craft_log/get_detail",{id:id.value}).then((res)=>{
            console.log(res,9999)
            if(res.errcode==0){
                tableData.value=res.result.crafts
            }
            console.log(tableData.value,9999)
            pictLoading.value=false
        }).catch(err=>{
            pictLoading.value=false
        })
    }
}
getBom()
console.log(query,8888)
console.log(window.location.href, 7777)
</script>
<style scoped>
.nopadding {
    padding: 20px;
}
</style>