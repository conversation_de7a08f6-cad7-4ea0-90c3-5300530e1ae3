<template>
    <el-container>
        <el-header>
            <el-page-header @back="goBack" content="工艺卡未发布版本">
            </el-page-header>
        </el-header>
        <el-main class="nopadding" v-loading="pictLoading">
            <el-table :data="tableData" stripe style="width: 100%" @row-contextmenu="rowContextmenu">
                <template #empty>
                    <el-empty description="暂无数据" :image-size="200"></el-empty>
                </template>
                <el-table-column prop="id" label="ID" width="80" />
                <el-table-column prop="material_number" label="代号" min-width="180" />
                <el-table-column prop="material_title" label="名称" min-width="120">
                    <template #default="scope">
                        <span style="color:#4496ee;cursor:pointer;" @click="returnBomClick(scope.row)">{{
                            scope.row.material_title }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="user_name" label="更新人" min-width="120" />
                <el-table-column prop="updated_at" label="更新时间" min-width="160" />
            </el-table>
            <el-pagination v-model:current-page="pageSet.page" v-model:page-size="pageSet.per_page"
                :page-sizes="[10, 20, 30, 40]" :total="pageSet.total" layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </el-main>
    </el-container>
    <div class="box-menu" v-if="menuVisible" :style="{ left: menu_left + 'px', top: menu_top + 'px' }">
        <div @click.stop="pegging_click()">
            <el-button type="link" icon="el-icon-edit">BOM反查</el-button>
        </div>
    </div>
</template>

<script setup>
import { useRouter, useRoute } from 'vue-router';
import http from '@/utils/request';
import { onMounted, ref } from 'vue';
import { useStore } from 'vuex';

let router = useRouter();
const store = useStore();
const pageSet = ref({
    per_page: 10,
    page: 1,
    total: 0
});
const route = useRoute();
let tableData = ref([]);
const pictLoading = ref(false);
const menuVisible = ref(false);
const menu_left = ref(0);
const menu_top = ref(0);
const checkBom = ref(null);

// 返回上一页
function goBack() {
    router.push({ path: "/index" });
}

if(route.params.op){
    pageSet.value.material_number=route.params.op
}

function getTableData() {
    pictLoading.value = true;
    http.get('datav/get_craftlog_ls',pageSet.value).then(res => {
        if(res.errcode === 0){
            tableData.value = res.result.data;
            pageSet.value.total = res.result.total;
            pictLoading.value = false;
        }else{
            pictLoading.value = false;
        }
    }).catch((err)=>{
        pictLoading.value = false;
    })
}

// 处理每页条数变化
function handleSizeChange(val) {
    pageSet.value.per_page = val;
    getTableData();
}

// 处理页码变化
function handleCurrentChange(val) {
    pageSet.value.page = val;
    getTableData();
}

// 返回BOM页面
function returnBomClick(op) {
    console.log(op, '返回BOm页面');
    let myArray = {
        id: op.id,
        ctype: "product",
        drawing_number: op.drawing_number,
        title: op.material_title,
        number: op.material_number,
        type: 2,
    }
    console.log(myArray, 'myArray');
    store.state.bomAddIndex = myArray;
    router.push({path:'/index/crud/IndexopenBom'});
}

// 鼠标右击事件
function rowContextmenu(row, column, event) {
    store.state.menuVisibletop = true;
    checkBom.value = {
        ctype: 'product',
        drawing_number: row.drawing_number,
        id: row.id,
        title: row.material_title,
        number: row.material_number,
        type: 2,
    }
    store.state.border_bom = [{
        id: row.id,
        title: row.material_title,
        number: row.material_number
    }];
    store.state.cost_number = row.material_number;
    menuVisible.value = true;
    menu_left.value = event.clientX + 50;
    menu_top.value = event.clientY - 0;
    document.addEventListener('click', foo);
}

// 关闭右键菜单
function foo() {
    menuVisible.value = false;
    document.removeEventListener('click', foo);
}

// BOM反查
function pegging_click() {
    store.state.isShowOutTree = false;
    store.state.listObj_bom = {
        isShow: true,
        isShowTree: true,
        items: checkBom.value,
        table: false,
        upload: false
    }
    router.push({path:'/search/tree'});
    menuVisible.value = false;
}

onMounted(() => {
    getTableData()
})
</script>

<style scoped>
.nopadding {
    padding: 20px;
}
.box-menu {
    position: absolute;
    z-index: 1000;
    background-color: #fff;
    box-shadow: 0px 0px 10px #ccc, 0px 0px 20px #ccc, 0px 0px 30px #ccc;
    padding: 10px;
}
.box-menu div {
    cursor: pointer;
    line-height: 30px;
}
</style>