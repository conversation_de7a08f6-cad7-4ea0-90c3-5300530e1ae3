<template>
    <el-container>
        <el-header>
            <el-page-header @back="goBack" content="未上传下料图纸">
            </el-page-header>
        </el-header>
        <el-main class="nopadding" v-loading="pictLoading">
            <el-table @row-contextmenu="rowContextmenu" :data="post_list" stripe style="width: 100%; height: 100%;">
                <template #empty>
                    <el-empty description="暂无数据" :image-size="200"></el-empty>
                </template>
                <el-table-column width="50" label="图标" #default="scope">
                    <el-icon size="16px" color="color" v-if="scope.row.type == 3"> <!-- 原材料 -->
                        <sc-icon-raw-material />
                    </el-icon>
                    <el-icon size="16px" color="color" v-if="scope.row.type == 0"> <!-- 产品 -->
                        <sc-icon-product />
                    </el-icon>
                    <el-icon size="16px" color="color" v-if="scope.row.type == 2"> <!-- 零件 -->
                        <sc-icon-part />
                    </el-icon>
                    <el-icon size="16px" color="red" v-if="scope.row.type == 1"> <!-- 部件 -->
                        <sc-icon-mponent />
                    </el-icon>
                </el-table-column>
                <el-table-column label="ID" min-width="70">
                    <template #default="scope">
                        <span>{{ scope.row.id }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="number" label="代号" min-width="180" align="center">
                    <template #default="scope">
                        <span>{{ scope.row.number }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="title" label="名称" min-width="100" align="center">
                    <template #default="scope">
                        <span style="color:#4496ee;cursor:pointer;" @click="returnBomClick(scope.row)">{{
                            scope.row.title }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="drawing_number" label="图号" min-width="100" align="center">
                    <template #default="scope">
                        <span>{{ scope.row.drawing_number }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="specs" label="规格" min-width="100" align="center">
                    <template #default="scope">
                        <span>{{ scope.row.specs }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="texture_title" label="材质" min-width="100" align="center">
                    <template #default="scope">
                        <span>{{ scope.row.texture_title }}</span>
                    </template>
                </el-table-column>
            </el-table>
        </el-main>
        <el-footer>
            <el-pagination v-model:current-page="pageSet.page" v-model:page-size="pageSet.per_page"
                :page-sizes="[10, 20, 30, 40]" :total="pageSet.total" layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </el-footer>
    </el-container>
    <div
        class="box-menu"
        v-if="menuVisible"
        :style="{ left: menu_left + 'px', top: menu_top + 'px' }"
    >
        <div @click.stop="pegging_click()">
            <el-button
                type="link"
                icon="el-icon-edit"
                >BOM反查</el-button
            >
        </div>
    </div>
</template>
<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import http from '@/utils/request';
import { useStore } from 'vuex';
import { ElMessage } from 'element-plus';

const router = useRouter();
const route = useRoute();
const store = useStore();

let pictLoading = ref(false);
const menuVisible = ref(false);
const menu_left = ref(0);
const menu_top = ref(0);
const checkBom = ref(null);
const form = ref({
    parent_material_number: '',
    material_number: '',
    title: '',
    type: '',
    drawing_number: '',
    specs: '',
    weight: '',
    unit_title: '',
    unit_id: '',
    texture_id: '',
    texture_title: '',
    price: '',
    quota: '',
    part_type_id: '',
    part_type_title: '',
    dosage: '',
    category_title: '',
    category_id: ''
});

const pageSet = ref({
    per_page: 10,
    page: 1,
    total: 0
});

const post_list = ref([]);

// 返回上一页
function goBack() {
    router.push({ path: "/index" });
}

// BOM反查
function pegging_click() {
    console.log(checkBom.value, 'this.BomObj');
    store.state.isShowOutTree = false;
    form.value.parent_number = checkBom.value.number;
    store.state.listObj_bom = {
        isShow: true,
        isShowTree: true,
        items: checkBom.value,
        table: false,
        upload: false
    };
    router.push({ path: '/search/tree' });
    menuVisible.value = false;
}

// 鼠标右击事件
function rowContextmenu(row, column, event) {
    store.state.menuVisibletop = true;
    console.log(store.state, store.state.menuVisibletop, 'this.$store.menuVisibletop');
    checkBom.value = {
        ctype: 'product',
        drawing_number: row.drawing_number,
        id: row.id,
        title: row.title,
        son: row.son,
        type: row.type,
        number: row.number,
        dosage: row.dosage,
        part_type: row.part_type,
        status: row.status,
        specs: row.specs
    };
    store.state.border_bom = [
        {
            id: row.id,
            title: row.title,
            number: row.number
        }
    ];
    store.state.cost_number = row.number;
    menuVisible.value = true;
    
    // 节点数据
    form.value = {
        parent_material_number: row.number,
        material_number: '',
        title: '',
        type: '',
        drawing_number: '',
        specs: '',
        weight: '',
        unit_title: '',
        unit_id: '',
        texture_id: '',
        texture_title: '',
        price: '',
        quota: '',
        part_type_id: '',
        part_type_title: '',
        dosage: '',
        category_title: '',
        category_id: ''
    };
    
    // 将菜单显示在鼠标点击旁边定位
    menu_left.value = event.clientX + 50;
    menu_top.value = event.clientY - 0;
    document.addEventListener('click', foo);
}

function foo() {
    menuVisible.value = false;
    document.removeEventListener('click', foo);
}

// 返回BOM页面
function returnBomClick(op) {
    console.log(op, '返回BOm页面');
    let myArray = {
        // id: op.material_id,
        id: op.id,
        craft_small_ver: op.craft_small,
        ctype: "product",
        drawing_number: op.drawing_number,
        title: op.title,
        number: op.number,
        type: op.type,
    };
    
    store.state.bomAddIndex = myArray;
    console.log(myArray, 'myArray');
    console.log(op, 'op');
    router.push({ path: '/index/crud/IndexopenBom' });
}

if (route.params.op) {
    pageSet.value.material_number = route.params.op;
    console.log(route.params.op,888)
}

function getPageList() {
    pictLoading.value = true;
    http.get('datav/get_txl_ls',pageSet.value).then(res => {
        if(res.errcode === 0){
            post_list.value = res.result.data;
            pageSet.value.total = res.result.total;
            pictLoading.value = false;
        }else{
            pictLoading.value = false;
        }
    }).catch((err)=>{
        pictLoading.value = false;
    })
}

// 处理每页条数变化
function handleSizeChange(val) {
    pageSet.value.per_page = val;
    getPageList();
}

// 处理页码变化
function handleCurrentChange(val) {
    pageSet.value.page = val;
    getPageList();
}

onMounted(() => {
    getPageList();
});
</script>
<style scoped>
.nopadding {
    padding: 20px;
}
.box-menu {
    position: absolute;
    z-index: 1000;
    background-color: #fff;
    box-shadow: 0px 0px 10px #ccc, 0px 0px 20px #ccc, 0px 0px 30px #ccc;
    padding: 10px;
}

.box-menu div {
    cursor: pointer;
    line-height: 30px;
}
</style>