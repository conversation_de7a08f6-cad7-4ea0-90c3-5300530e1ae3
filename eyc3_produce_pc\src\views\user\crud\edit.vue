<template>
    <yp_form :columns="columns" :backrouter="backrouter"></yp_form>
</template>

<script>
export default {
    name: 'BasicForm',
    mounted() {
        // console.log('打开了detail页面')
    },
    data() {
        return {
            backrouter: '/user',
            columns: [
                {
                    label: '姓名',
                    name: 'name',
                    component: 'input',
                    options: {
                        disabled: true
                    }
                },
                {
                    label: '部门名称',
                    name: 'department_name',
                    component: 'select',
                    options: {
                        remote: {
                            api: 'user/get_user_dep_list',
                            data: {
                                corpid: '$corpid',
                                userid: '$userid'
                            },
                            label: 'name',
                            value: 'name',
                            relatedata: 'dept_id'
                        }
                    }
                }
            ]
        }
    }
}
</script>

<style>
</style>