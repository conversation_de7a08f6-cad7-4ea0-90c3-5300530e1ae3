/**
 * @description 自动import导入所有 vuex 模块
 */

import { createStore } from 'vuex';

const files = import.meta.globEager('./modules/*.js')
const modules = {}
Object.keys(files).forEach(key => {
	modules[key.replace(/^\.\/modules\/(.*)\.js$/g, '$1')] = files[key].default
})

const store = createStore({
	state: {
	  	listObj: {
			isShow:false,
			ver:false,
		},
		release_st:false,
		material_number:'',
		curIndex:1,
		technology:false,
		border_bom:null,
		cost_number:null,
		release_stlist:{},
		Unpublishedlist:{},
		bomRelease_list:{},
		bomUnpublishedlist:{},
		menuVisibletop:null,
	},
});
export default createStore({
	modules,
	store,
});
