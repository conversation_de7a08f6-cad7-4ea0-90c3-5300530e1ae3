
<template>
    <yp_form
        :columns="columns"
        :labelWidth="labelWidth"
        :backrouter="backrouter"
    ></yp_form>
</template>

<script>
export default {
    name: 'BasicPowerAdd',
    data() {
        return {
            labelWidth: '110px',
            backrouter: '/information/class',
            columns: [
                {
                    label: '分类名称',
                    name: 'title',
                    component: 'input',
                    options: {
                        placeholder: '请输入'
                    },
                    rules: [
                        {
                            required: true,
                            message: '请输入分类名称',
                            trigger: 'change'
                        }
                    ]
                },
                {
                    label: '父级分类',
                    name: 'parent_obj',
                    component: 'scTableSelect',
                    options: {
                        placeholder: '请输入',
                        api: 'category/get_ls',
                        tablecolum: [
                            {
                                label: '分类',
                                prop: 'title'
                            }
                        ],
                        props: {
                            label: 'title',
                            value: 'id',
                            keyword: 'keyword'
                        },
                        posttype: 'splitobj',
                        splitItems: {
                            parent_id: '$id',
                            parent_title: '$title'
                        }
                    }
                }
            ]
        }
    }
}
</script>

<style>
</style>