<template>
    <el-drawer v-model="drawer" :size="800" :title="savetitle" direction="rtl" destroy-on-close>
        <el-main style="padding: 0 30px" class="el-main">
            <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="auto" class="demo-ruleForm"
                :size="formSize" status-icon>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="工艺代号:" prop="processCode">
                            <el-input v-model="ruleForm.processCode" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="设备名称:" prop="name">
                            <el-input v-model="ruleForm.name" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="设备类型:" prop="type">
                            <el-input v-model="ruleForm.type" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="设备说明:" prop="notes">
                            <el-input v-model="ruleForm.notes" :autosize="{ minRows: 2, maxRows: 3 }" type="textarea" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="综合小时费率:" prop="hourlyRate">
                            <el-input-number v-model="ruleForm.hourlyRate" :precision="2" :step="0.1" :min="0" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="成本中心:" prop="costCenter">
                            <el-input-number v-model="ruleForm.costCenter" :min="0" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="是否外协:" prop="outsourcingOrNot">
                            <el-select v-model="ruleForm.outsourcingOrNot" placeholder="Select">
                                <el-option label="是" :value="true"></el-option>
                                <el-option label="否" :value="false"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="人工分钟费率:" prop="rateOfMinutes">
                            <el-input-number v-model="ruleForm.rateOfMinutes" :precision="3" :step="0.01" :min="0" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div style="margin-top: 63vh;">
                <el-button type="primary" :loading="isSaveing" @click="submit()">提 交</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-main>

    </el-drawer>
</template>
<script setup>
import { ref } from 'vue';

let drawer = ref(false)
let savetitle = ref("")
let isSaveing = ref(false)
const ruleFormRef = ref()
let ruleForm = ref({
    processCode: "",
    name: "",
    type: "",
    notes: "",
    hourlyRate: 0,
    costCenter: 0,
    outsourcingOrNot: false,
    rateOfMinutes: 0
})
const rules = ref({
    processCode: [
        {
            required: true,
            message: '请输入工艺代号',
            trigger: 'blur',
        },
    ],
    name: [
        {
            required: true,
            message: '请输入设备名称',
            trigger: 'blur',
        }
    ],
    type: [
        {
            required: true,
            message: '请输入设备类型',
            trigger: 'blur',
        }
    ],
})

function open(titel) {
    savetitle.value = titel
    drawer.value = true
    ruleForm.value = {
        processCode: "",
        name: "",
        type: "",
        notes: "",
        hourlyRate: 0,
        costCenter: 0,
        outsourcingOrNot: false,
        rateOfMinutes: 0
    }
}
async function submit() {
    isSaveing.value = true
    await ruleFormRef.value.validate((valid, fields) => {
        if (valid) {
            setTimeout(() => {
                isSaveing.value = false
                drawer.value = false
            }, 1000);
        } else {
            isSaveing.value = false
        }
    })
}
function editItem(titel,item){
    savetitle.value = titel
    ruleForm.value=item
    drawer.value = true
}
function cancel() {
    drawer.value = false
}
defineExpose({
    open,
    editItem
})
</script>