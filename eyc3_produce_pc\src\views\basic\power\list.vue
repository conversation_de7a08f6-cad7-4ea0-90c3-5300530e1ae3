<!--
 * @author: 吉慧雯
 * @name: 计划任务
 * @desc: 介绍
 * @LastEditTime: 2022-12-30 15:55:07
 * @FilePath: \eyc3_guard_pc/src/views/work/basic/task
-->
<template>
    <yp_list
        ref="table"
        :url="url"
        :columns="columns"
        :formitems="formitems"
        :buttonList="buttonList"
    >
    </yp_list>
</template>

<script>
export default {
    name: 'Power',
    data() {
        return {
            url: 'permission_group/get_ls',
            columns: [
                {
                    label: '管理组名称',
                    prop: 'title'
                },
                {
                    label: '管理员',
                    prop: 'admin_names'
                },
                {
                    label: '权限节点',
                    prop: 'actions',
                    component: 'array',
                    options: [
                        {
                            prop: 'title'
                        }
                    ]
                },
                {
                    label: '操作',
                    prop: 'action',
                    component: 'button',
                    options: [
                        {
                            label: '详情',
                            component: 'detail',
                            options: {
                                title: '权限管理详情',
                                column: 1,
                                directionDesc: 'vertical',
                                remote: {
                                    api: 'permission_group/get_info',
                                    data: {
                                        id: '$id'
                                    }
                                },
                                items: [
                                    {
                                        label: '管理组名称',
                                        prop: 'title'
                                    },
                                    {
                                        label: '管理员',
                                        prop: 'admin_names'
                                    },
                                    {
                                        label: '管理范围',
                                        prop: 'manage_scope',
                                        component: 'tag',
                                        options: {
                                            items: [
                                                {
                                                    label: '全组织',
                                                    value: 0
                                                },
                                                {
                                                    label: '所在部门和下级部门',
                                                    value: 1
                                                },
                                                {
                                                    label: '所在部门',
                                                    value: 2
                                                }
                                            ]
                                        }
                                    },
                                    {
                                        label: '分配权限',
                                        prop: 'action_titles'
                                    }
                                ],
                                haveslot: true
                            }
                        },
                        {
                            label: '修改',
                            component: 'form',
                            options: {
                                name: 'BasicPowerEdit', // 跳转页面名
                                remote: {
                                    state: 'edit', // 状态,'add'|'edit'|'detail'
                                    label: '编辑权限管理', // 页头名
                                    api: 'permission_group/get_info', // 获取详情接口
                                    edit: 'permission_group/post_modify', // 修改详情接口
                                    data: {
                                        // 获取详情接口数据
                                        id: '$id'
                                    }
                                },
                            }
                        },
                        {
                            label: '删除',
                            type: 'danger',
                            component: 'confirm',
                            options: {
                                label: '确认删除',
                                message: '是否确认删除?',
                                remote: {
                                    api: `permission_group/post_del`,
                                    data: {
                                        id: '$id'
                                    }
                                },
                                relate: [
                                    {
                                        prop: 'lock',
                                        value: 1,
                                        message: '已锁定,请解锁后操作'
                                    }
                                ]
                            }
                        }
                    ]
                }
            ],
            formitems: [
                {
                    label: '管理组名称',
                    name: 'keyword',
                    component: 'input',
                    options: {
                        placeholder: '请输入'
                    }
                }
            ],
            buttonList: [
                {
                    label: '新建',
                    component: 'form',
                    options: {
                        name: 'BasicPowerAdd', // 跳转页面名
                        icon: 'el-icon-Plus', // 按钮图标
                        remote: {
                            state: 'add', // 状态
                            label: '新增权限管理', // 页头名
                            api: 'permission_group/post_add', // 新增地址接口
                            data: {}
                        }
                    }
                }
            ]
        }
    },
}
</script>

<style lang="scss" scoped>
</style>
