<template >
    <!-- <div style="text-align: right"> -->
        <!-- <yy_upload
            v-if="upload"
            :label="upload.label"
            :templateUrl="upload.templateUrl"
            :columnData="upload.columnData"
            :url="upload.url"
            :maxSize="upload.maxSize"
            :accept="upload.accept"
            :filename="upload.filename"
            @success="success"
        ></yy_upload> -->
    <!-- </div> -->
    <el-main v-loading="pdfdoing" class="common-layout" style="margin-top: -25px; height: 82vh;">
        <div style="display: flex; justify-content: space-between;align-items: center;margin-bottom: 10px;">
            <div class="info_title">
                <span>物料名称：<span>{{info_obj.title?info_obj.title:"暂无"}}</span></span>
                <span>物料号：<span>{{info_obj.number?info_obj.number:'暂无'}}</span></span>
                <span>规格：<span>{{info_obj.specs?info_obj.specs:'暂无'}}</span></span>
                <span>型号：<span>{{info_obj.type==0?'产品':info_obj.type==1?'部件':info_obj.type==2?'零件':info_obj.type==3?'原材料':'暂无'}}</span></span>
                <span>图号：<span>{{info_obj.drawing_number?info_obj.drawing_number:'暂无'}}</span></span>
            </div>
            <pdfupload @success="success" :postDataObj="postDataObj" :parentMessage="parentMessage"></pdfupload>
        </div>
        <div class="common-layout1" v-if="list.length>0">
            <!-- <div style="width: 100%;">[ { "id": 21, "corpid": "dingb9614df94342f570a1320dcb25e91351", "number": "2504010165", "url": "https://filestest.eykj.cn/2024-03-19/pPZVVE4cAcWkxu5IXZq394MIS6pcAUYS.pdf", "status": 0, "created_at": "2024-03-19 14:12:05",
</div> -->
<!-- 占位符 -->
            <!-- <div>

            </div> -->
            <!-- 渲染的数据 -->
            <div
                v-for="item in list"
                :key="item.id"
                class="elrow"
            >
                <!-- <iframe :src="'static/pdf/web/viewer.html?file=' + item.url" -->
                <iframe :src="'static/pdf/web/viewer.html?file=' + item.url"
                    style="width: 100%; height: 100%; border: none;"
                ></iframe>
            </div>
        </div>
        <div v-else>
            <el-empty
                description="暂无数据"
                :image-size="200"
            ></el-empty>
        </div>
    </el-main>
</template>

<script>
import { mapState } from 'vuex'
import * as dd from 'dingtalk-jsapi'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import VueOfficePdf from '@vue-office/pdf'
import { Base64 } from 'js-base64';
import pdfupload from './components/drawingupload.vue'



export default {
    components:{
    VueOfficePdf,
    pdfupload,
  },
    data() {
        return {
            postDataObj:{material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`)},
            pdfdoing:false,
            list: [],
            parentMessage:{drawing_number:''},
            // upload:{
            //     label: '导入',
            //     component: 'upload',
            //     url: 'https://fileapi.qixuw.com/file/post_upload',
            //     accept:'.pdf',
            //     maxSize:10000,
            //     multiple:true
            // },
            loading:true,
            option:'',
            info_obj:''
        }
    },
    computed: {
        ...mapState(['listObj'])
    },
    created() {
        this.option = sessionStorage.getItem(`material_id_${sessionStorage.getItem('windowId')}`)
        this.$store.watch(
            (state) => state.listObj,
            (newValue) => {
                this.postData('drawing/get_material_all')
            }
        )
    },
    mounted() {
        console.log(this.option,'this.option');
        this.indata('material/get_info', { id: this.option })
    },
    methods: {
        success(op){
            if (!op) {
                this.loading = false
                ElMessage.error(op.errmsg)
            } else {
                this.indata('material/get_info', { id: this.option })
            }
        },
        postData(url, page) {
            console.log( localStorage.getItem("parent_material_number"),'page');
            this.pdfdoing = true
            this.$HTTP.get(url, {material_number:this.info_obj.number}).then((res) => {
                if (res.errcode != 0) {
                    this.loading = false
                    ElMessage.error(res.errmsg)
                } else {
                    this.list = res.result
                    this.pdfdoing = false
                }
            })
        },
        indata(url, page) {
            this.$HTTP.get(url, page).then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    this.info_obj = res.result
                    this.parentMessage.drawing_number=this.info_obj.drawing_number
                    this.postData('drawing/get_material_all', { material_number: res.result.drawing_number})
                    console.log(res);
                }
            })
        },
        // Previewbutton(op) {
        //     console.log(op,'1231231231');
        //     if(op.url){
        //         dd.biz.util.openLink({
        //             url: op.url
        //             // 'http://files.qixuw.com/dingb9614df94342f570a1320dcb25e91351/canyin/2023-08-21/LCRjEUtgNgYwI8KaY2kAvoAuiCmxsgqh.pdf'
        //         })
        //     }else{
        //         ElMessage.error('没有图纸地址')
        //     }
        // },
        // clickadd(){
        //     var url = 'http://files.qixuw.com/dingb9614df94342f570a1320dcb25e91351/canyin/2023-08-21/LCRjEUtgNgYwI8KaY2kAvoAuiCmxsgqh.pdf'; //要预览文件的访问地址
        //     window.open('https://file.kkview.cn/onlinePreview?url='+encodeURIComponent(Base64.encode(url)));
        // }
    }
}
</script>

<style lang="scss" scoped>
.common-layout{
    height: 100px;
}
.common-layout1{
    height: 100%;
}
.elrow {
    height: 100%;
    width: 100%;
    // min-height: calc(100vh - 120px); /* Adjust based on header height */
    display: flex;
    flex-direction: column;
}
.info_title{
    font-size: 15px;
    margin-bottom: 10px;
    span{
        margin-right: 20px;
        span{
            color: #666666;
        }
    }
}
// .pdfimg {
//     vertical-align: middle;
// }
// .text {
//     display: flex;
//     height: 100%;
//     flex-direction: column;
//     justify-content: center;
//     align-items: center;
//     span {
//         font-size: 17px;
//         color: #007fff;
//         cursor: pointer;
//         margin-bottom: 5px;
//     }
// }
// .green {
//     color: #20ef5e;
// }
// .red {
//     color: #f56c6c;
// }
</style>
