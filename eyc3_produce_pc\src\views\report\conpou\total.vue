<!--
 * @author: 风源
 * @name: 类名
 * @desc: 介绍
 * @LastEditTime: 2023-01-03 23:10:19
 * @FilePath: /eyc3_canyin_pc/src/views/report/conpou/total.vue
-->
<template>
	<yp_list
		ref="table"
		:url="url"
		row-key="id"
		:columns="columns"
		stripe
		:add="add"
		:derive="derive"
		:formitems="formitems"
		:paging="false"
		:showsummary="true"
	>
	</yp_list>
</template>

<script>
export default {
	name: "ReportConpouTotal",
	data() {
		return {
			url: "report/get_coupon_sum",
			columns: [
				{
					label: "优惠券名称",
					prop: "coupon_title",
				},
				{
					label: "餐厅名称",
					prop: "dininghall_title",
				},
				{
					label: "订单数",
					prop: "count",
				},

				{
					label: "总订单金额",
					prop: "money",
				},
				{
					label: "总补贴金额",
					prop: "subsidy_money",
				},
				{
					label: "总优惠金额",
					prop: "coupon_money",
				},				
			],
			derive: {
				filename:"优惠券汇总"
			},
			formitems: [
				{
					label: "日期范围",
					name: "date_interval",
					value: [
						this.$TOOL.dateFormat(new Date(), "yyyy-MM-01"),
						this.$TOOL.dateFormat(new Date(), "yyyy-MM-dd"),
					],
					component: "date",
					options: {
						type: "daterange",
						rangeseparator: "至",
						startplaceholder: "开始日期",
						endplaceholder: "结束日期",
						valueFormat: "YYYY-MM-DD",
					},
					rules: [
						{
							required: true,
							message: "Please input Data",
							trigger: "change",
						},
					],
				},
				{
					label: "选择人员",
					name: "userlst",
					value: "",
					component: "selectUser"
				},
				{
					label: "优惠券",
					name: "coupon_id",
					value: "",
					component: "select",
					options: {
						remote: {
							api: `coupon/get_all`,
							label: "title",
							value: "id",
						},
						items: [
							{
								label: "全部",
								value: "",
							},
						],
					},
				},
				{
					label: "餐厅",
					name: "dininghall_id",
					value: "",
					component: "select",
					options: {
						remote: {
							api: `dininghall/get_all`,
							data: { name: "b" },
							label: "title",
							value: "id",
						},
						items: [
							{
								label: "全部",
								value: "",
							},
						],
					},
				},
				{
					label: "餐时",
					name: "repast_id",
					value: "",
					component: "select",
					options: {
						remote: {
							api: `repast/get_all`,
							data: { dininghall_id: "$dininghall_id" },
							label: "title",
							value: "id",
						},
						items: [
							{
								label: "全部",
								value: "",
							},
						],
					},
				},
				{
					label: "窗口",
					name: "window_id",
					value: "",
					component: "select",
					options: {
						remote: {
							api: `window/get_all`,
							data: { dininghall_id: "$dininghall_id" },
							label: "title",
							value: "id",
						},
						items: [
							{
								label: "全部",
								value: "",
							},
						],
					},
				},				
			],
		};
	},
};
</script>

<style></style>
