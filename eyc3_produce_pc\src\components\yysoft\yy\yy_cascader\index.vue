<template>
    <el-cascader
        v-model="value"
        :options="remoteoptions"
        :props="props"
        @visible-change="getData"
        :loading="loading"
        clearable
    ></el-cascader>
</template>

<script>
export default {
    name: 'yy_cascader',
    props: {
        placeholder: { type: String, default: '请选择' },
        props: { type: Object, default: () => {} },
        form: { type: Object, default: () => {} },
        remote: { type: Object, default: () => {} }
    },
    data() {
        return {
            loading: false,
            // options: [
            //     {
            //         value: 'zhinan',
            //         label: '指南',
            //         children: [
            //             {
            //                 value: 'shejiyuanze',
            //                 label: '设计原则',
            //                 children: [
            //                     {
            //                         value: 'yizhi',
            //                         label: '一致'
            //                     },
            //                     {
            //                         value: 'fankui',
            //                         label: '反馈'
            //                     },
            //                     {
            //                         value: 'xiaolv',
            //                         label: '效率'
            //                     },
            //                     {
            //                         value: 'kekong',
            //                         label: '可控'
            //                     }
            //                 ]
            //             },
            //             {
            //                 value: 'daohang',
            //                 label: '导航',
            //                 children: [
            //                     {
            //                         value: 'cexiangdaohang',
            //                         label: '侧向导航'
            //                     },
            //                     {
            //                         value: 'dingbudaohang',
            //                         label: '顶部导航'
            //                     }
            //                 ]
            //             }
            //         ]
            //     },
            //     {
            //         value: 'zujian',
            //         label: '组件',
            //         children: [
            //             {
            //                 value: 'basic',
            //                 label: 'Basic',
            //                 children: [
            //                     {
            //                         value: 'layout',
            //                         label: 'Layout 布局'
            //                     },
            //                     {
            //                         value: 'color',
            //                         label: 'Color 色彩'
            //                     },
            //                     {
            //                         value: 'typography',
            //                         label: 'Typography 字体'
            //                     },
            //                     {
            //                         value: 'icon',
            //                         label: 'Icon 图标'
            //                     },
            //                     {
            //                         value: 'button',
            //                         label: 'Button 按钮'
            //                     }
            //                 ]
            //             },
            //             {
            //                 value: 'form',
            //                 label: 'Form',
            //                 children: [
            //                     {
            //                         value: 'radio',
            //                         label: 'Radio 单选框'
            //                     },
            //                     {
            //                         value: 'checkbox',
            //                         label: 'Checkbox 多选框'
            //                     },
            //                     {
            //                         value: 'input',
            //                         label: 'Input 输入框'
            //                     },
            //                     {
            //                         value: 'input-number',
            //                         label: 'InputNumber 计数器'
            //                     },
            //                     {
            //                         value: 'select',
            //                         label: 'Select 选择器'
            //                     },
            //                     {
            //                         value: 'cascader',
            //                         label: 'Cascader 级联选择器'
            //                     },
            //                     {
            //                         value: 'switch',
            //                         label: 'Switch 开关'
            //                     },
            //                     {
            //                         value: 'slider',
            //                         label: 'Slider 滑块'
            //                     },
            //                     {
            //                         value: 'time-picker',
            //                         label: 'TimePicker 时间选择器'
            //                     },
            //                     {
            //                         value: 'date-picker',
            //                         label: 'DatePicker 日期选择器'
            //                     },
            //                     {
            //                         value: 'datetime-picker',
            //                         label: 'DateTimePicker 日期时间选择器'
            //                     },
            //                     {
            //                         value: 'upload',
            //                         label: 'Upload 上传'
            //                     },
            //                     {
            //                         value: 'rate',
            //                         label: 'Rate 评分'
            //                     },
            //                     {
            //                         value: 'form',
            //                         label: 'Form 表单'
            //                     }
            //                 ]
            //             },
            //             {
            //                 value: 'data',
            //                 label: 'Data',
            //                 children: [
            //                     {
            //                         value: 'table',
            //                         label: 'Table 表格'
            //                     },
            //                     {
            //                         value: 'tag',
            //                         label: 'Tag 标签'
            //                     },
            //                     {
            //                         value: 'progress',
            //                         label: 'Progress 进度条'
            //                     },
            //                     {
            //                         value: 'tree',
            //                         label: 'Tree 树形控件'
            //                     },
            //                     {
            //                         value: 'pagination',
            //                         label: 'Pagination 分页'
            //                     },
            //                     {
            //                         value: 'badge',
            //                         label: 'Badge 标记'
            //                     }
            //                 ]
            //             },
            //             {
            //                 value: 'notice',
            //                 label: 'Notice',
            //                 children: [
            //                     {
            //                         value: 'alert',
            //                         label: 'Alert 警告'
            //                     },
            //                     {
            //                         value: 'loading',
            //                         label: 'Loading 加载'
            //                     },
            //                     {
            //                         value: 'message',
            //                         label: 'Message 消息提示'
            //                     },
            //                     {
            //                         value: 'message-box',
            //                         label: 'MessageBox 弹框'
            //                     },
            //                     {
            //                         value: 'notification',
            //                         label: 'Notification 通知'
            //                     }
            //                 ]
            //             },
            //             {
            //                 value: 'navigation',
            //                 label: 'Navigation',
            //                 children: [
            //                     {
            //                         value: 'menu',
            //                         label: 'NavMenu 导航菜单'
            //                     },
            //                     {
            //                         value: 'tabs',
            //                         label: 'Tabs 标签页'
            //                     },
            //                     {
            //                         value: 'breadcrumb',
            //                         label: 'Breadcrumb 面包屑'
            //                     },
            //                     {
            //                         value: 'dropdown',
            //                         label: 'Dropdown 下拉菜单'
            //                     },
            //                     {
            //                         value: 'steps',
            //                         label: 'Steps 步骤条'
            //                     }
            //                 ]
            //             },
            //             {
            //                 value: 'others',
            //                 label: 'Others',
            //                 children: [
            //                     {
            //                         value: 'dialog',
            //                         label: 'Dialog 对话框'
            //                     },
            //                     {
            //                         value: 'tooltip',
            //                         label: 'Tooltip 文字提示'
            //                     },
            //                     {
            //                         value: 'popover',
            //                         label: 'Popover 弹出框'
            //                     },
            //                     {
            //                         value: 'card',
            //                         label: 'Card 卡片'
            //                     },
            //                     {
            //                         value: 'carousel',
            //                         label: 'Carousel 走马灯'
            //                     },
            //                     {
            //                         value: 'collapse',
            //                         label: 'Collapse 折叠面板'
            //                     }
            //                 ]
            //             }
            //         ]
            //     },
            //     {
            //         value: 'ziyuan',
            //         label: '资源',
            //         children: [
            //             {
            //                 value: 'axure',
            //                 label: 'Axure Components'
            //             },
            //             {
            //                 value: 'sketch',
            //                 label: 'Sketch Templates'
            //             },
            //             {
            //                 value: 'jiaohu',
            //                 label: '组件交互文档'
            //             }
            //         ]
            //     }
            // ],
            remoteoptions: [],
            value: ''
        }
    },
    watch: {
        value(val) {
            console.log(val)
            this.form[this.remote.name] = null
            if (this.form[this.remote.children.name]) {
                // console.log(this.remote.children.name)
                this.form[this.remote.children.name] = null
            }
            if (val) {
                if (val.length == 1) {
                    this.form[this.remote.name] = val[0]
                } else if (val.length == 2) {
                    this.form[this.remote.name] = val[0]
                    this.form[this.remote.children.name] = val[1]
                }
            }
        }
    },
    mounted() {},
    methods: {
        //处理远程选项数据
        getData(e) {
            if (e) {
                if (this.remote) {
                    this.remoteoptions = []
                    this.loading = true
                    var postData = {}
                    var rs = this.remote.data
                    console.log(rs)
                    for (const key in rs) {
                        if (typeof rs[key] == 'number') {
                            postData[key] = rs[key]
                        } else if (rs[key].substring(0, 1) == '$') {
                            postData[key] = this.form[rs[key].substring(1)]
                            // console.log(postData[key])
                            if (postData[key] == '') return
                        } else {
                            postData[key] = rs[key]
                        }
                    }
                    this.$HTTP.get(this.remote.api, postData).then((res) => {
                        if (res.errcode != 0) {
                            ElMessage.error(res.errmsg)
                        } else {
                            this.handleData(res.result)
                            this.loading = false
                        }
                    })
                }
            }
        },
        // 处理获取到的数据
        handleData(info) {
            // console.log(info)
            info.forEach((item) => {
                item.value.forEach((itm) => {
                    // console.log('----------------313-----------------------')
                    // console.log(itm)
                    var handleData = {}
                    handleData.value = itm[this.remote.value]
                    handleData.label = itm[this.remote.label]
                    if (this.remote.children) {
                        var childrendata = []
                        itm[this.remote.children.value].forEach((im) => {
                            var handlechildrendata = {}
                            handlechildrendata.value = im[this.remote.children.items.value]
                            handlechildrendata.label = im[this.remote.children.items.label]
                            childrendata.push(handlechildrendata)
                        })
                        handleData.children = childrendata
                    }
                    this.remoteoptions.push(handleData)
                })
            })
        }
    }
}
</script>

<style>
</style>