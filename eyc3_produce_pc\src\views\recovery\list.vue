<template>
    <el-container>
        <el-header>
            <div class="right-panel">
                <div class="right-panel-search">
                    <el-input
                        v-model="query.keyword"
                        placeholder="名称查询"
                        clearable
                    ></el-input>
                    <el-button
                        type="primary"
                        icon="el-icon-search"
                        @click="Query_button"
                    ></el-button>
                </div>
            </div>
        </el-header>
        <el-main class="nopadding" v-loading="loading">
			<scTable ref="table" :data="post_datalist" :lthority="limauthority" row-key="id" hidePagination  hideDo>
				<el-table-column
                    type="index"
                    label="序号"
                    align="center"
                    width="50">
                </el-table-column>
                <el-table-column
                    width="50"
                    label="图标"
                    #default="scope"
                >
                    <el-icon size="16px" color="color" v-if="scope.row.type==3"> <!-- 原材料 -->
                        <sc-icon-raw-material/>
                    </el-icon>
                    <el-icon size="16px" color="color" v-if="scope.row.type==0"> <!-- 产品 -->
                        <sc-icon-product/>
                    </el-icon>
                    <el-icon size="16px" color="color" v-if="scope.row.type==2"> <!-- 零件 -->
                        <sc-icon-part/>
                    </el-icon>
                    <el-icon size="16px" color="red" v-if="scope.row.type==1"> <!-- 部件 -->
                        <sc-icon-mponent/>
                    </el-icon>
                </el-table-column>
                <el-table-column
                    label="ID"
                    min-width="80"
                    align="center"
                    sortable
                >
                    <template #default="scope">
                        <span @click.stop="titleClick(scope.row)">{{ scope.row.id }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="number"
                    label="代号"
                    min-width="80"
                    align="center"
                    sortable
                >
                    <template #default="scope">
                        <span @click.stop="titleClick(scope.row)">{{ scope.row.number }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="title"
                    label="名称"
                    min-width="80"
                    align="center"
                    sortable
                >
                    <template #default="scope">
                        <span @click.stop="titleClick(scope.row)">{{ scope.row.title }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="drawing_number"
                    label="图号"
                    min-width="80"
                    align="center"
                    sortable
                >
                    <template #default="scope">
                        <span @click.stop="titleClick(scope.row)">{{ scope.row.drawing_number }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="specs"
                    label="规格"
                    min-width="80"
                    align="center"
                    sortable
                >
                    <template #default="scope">
                        <span @click.stop="titleClick(scope.row)">{{ scope.row.specs }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="texture_title"
                    label="材质"
                    min-width="80"
                    align="center"
                    sortable
                >
                    <template #default="scope">
                        <span @click.stop="titleClick(scope.row)">{{ scope.row.texture_title }}</span>
                    </template>
                </el-table-column>
				<el-table-column label="操作" align="center" min-width="150">
					<template #default="scope">
						<!-- <el-button type="primary" plain size="small" @click="table_show(scope.row)">详情</el-button> -->
						<!-- <el-button type="primary" plain size="small" @click="table_edit(scope.row)">编辑</el-button> -->
						<el-popconfirm title="确定恢复吗？" @confirm="table_edit(scope.row, scope.$index)">
							<template #reference>
								<el-button plain type="danger" size="small" v-show="showRecovery">恢复</el-button>
							</template>
						</el-popconfirm>
                        <!-- <el-popconfirm title="确定删除吗？" @confirm="table_del(scope.row, scope.$index)">
							<template #reference>
								<el-button plain type="danger" size="small">删除</el-button>
							</template>
						</el-popconfirm> -->
					</template>
				</el-table-column>
			</scTable>
		</el-main>
        <el-footer>
            <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-sizes="[10, 20, 30, 40]"
                :page-size="query.per_page"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                small
            >
            </el-pagination>
        </el-footer>
    </el-container>
    <el-drawer v-model="dialog.info" :size="500" title="详细" direction="rtl" destroy-on-close>
		<info ref="infoDialog"></info>
	</el-drawer>
    <el-drawer
        v-model="dialog.save"
        :size="800"
        :title="savetitle"
        direction="rtl"
        destroy-on-close
    >
        <save-dialog
            ref="saveDialog"
            @saveclosure="saveclosure"
            @success="handleSaveSuccess"
            @transfer="transfer"
        ></save-dialog>
    </el-drawer>

</template>

<script>
import info from './crud/info'
import saveDialog from './crud/add'
export default {
    name: 'dept',
    components: {
        saveDialog,
        info
    },
    data() {
        return {
            limauthority:false,
            showRecovery:null,
            dialog:{
			    save: false,
				info: false
			},
            query: {
                page: 1,
                per_page: 10,
                status:1
            },
            loading:false,
            post_datalist: [],
            search: {
                keyword: null
            },
            total: 0,
            savetitle: '',
        }
    },
    computed: {
    },
    created(){
        try {
            const array = localStorage.getItem('PERMISSION')
            // console.log(JSON.parse(array).content.actions, '打印的权限节点')
            //判断是否可以下载
            JSON.parse(array).content.actions.map((item) => {
                console.log(item,'item>>>>>>');
                if (item.id == 11) {
                    item.acts.map((items) => {
                        if (items.title == '恢复') {
                            // console.log('你来了吗??')
                            this.showRecovery = true
                        }
                    })
                }
                // } else if (item.id == 1) {
                //     item.acts.map((items) => {
                //         // console.log('走了吗?111111111111111111111111111', items)
                //         if (items.title == '删除') {
                //             // console.log('你来了吗??')
                //             this.deactivate = true
                //         }
                //     })
                // }
            })
            // 处理jsonData
        } catch (error) {
            console.error('解析JSON时出错:', error)
            // 错误处理，比如显示用户友好的消息或使用默认数据
        }
    },
    mounted(){
        this.list_post()
        // 添加键盘事件监听器，按回车键触发查询
        document.addEventListener('keydown', this.handleKeyDown)
    },
    beforeDestroy() {
        // 移除键盘事件监听器
        document.removeEventListener('keydown', this.handleKeyDown)
    },
    watch: {
        // 监听左侧树输入筛选
        'query.keyword'(val) {
            if (val) {
                // this.$HTTP.post('material/get_search', { keyword: val }).then((res) => {
                // 	if (res.errcode != 0) {
                // 		this.loading = false
                // 		ElMessage.error(res.errmsg)
                // 	} else {
                // 		let resItem = res.result
                // 		console.log(resItem,'====================================');
                // 		resItem.map((item) => {
                // 			if (item.son == 1) {
                // 				item.leaf = false
                // 			} else {
                // 				item.leaf = true
                // 			}
                // 		})
                // 		this.groupData = resItem
                // 	}
                // })
            } else {
                this.list_post()
            }
        }
    },
    methods: {
        // 处理键盘事件，按回车键触发查询
        handleKeyDown(event) {
            // 如果按下的是回车键（keyCode 13）
            if (event.keyCode === 13 || event.key === 'Enter') {
                // 检查当前焦点是否在输入框上
                const activeElement = document.activeElement;
                const isInputFocused = activeElement.tagName === 'INPUT' &&
                                      activeElement.classList.contains('el-input__inner');

                // 如果焦点在搜索输入框上，或者没有特定元素获得焦点
                if (isInputFocused || activeElement === document.body) {
                    // 触发查询按钮点击事件
                    this.Query_button();
                }
            }
        },

        // 数据请求
        list_post(url,op){
            this.loading = true
            this.$HTTP.post('material/get_ls',this.query).then((res) => {
                if (res.errcode != 0) {
                    this.loading = false
                    if(res.errcode!=510){
                        ElMessage.error(res.errmsg)
                    }else{
                        this.limauthority = true
                    }
                } else {
                    let resData = res.result.data
                    this.post_datalist = resData
                    this.total = res.result.total
                    this.loading = false
                }
            })
        },
        // // 添加
        // add(){
        //     this.dialog.save = true
        //     this.savetitle = '新增'
        //     this.$nextTick(() => {
        //         this.$refs.saveDialog.addsaveData({ add: 'tool/post_add' })
        //     })
        // },
        //窗口编辑
        table_edit(row) {
            this.$HTTP.get('material/post_recover', { id: row.id}).then((res) => {
                if (res.errcode == 0) {
                    this.list_post()
                    this.$message({
                        type: 'success',
                        message: '恢复成功!'
                    })
                }
            })
        },
        titleClick(){

        },
        // // 点击添加提交成功重新调用列表接口
        // transfer(op) {
        //     if (op == '成功') {
        //         this.list_post()
        //     }
        // },
        // // 删除
        // table_del(row){
        //     this.$HTTP.get('tool/post_del', { id: row.id }).then((res) => {
        //         if (res.errcode == 0) {
        //             this.list_post()
        //             this.$message({
        //                 type: 'success',
        //                 message: '删除成功!'
        //             })
        //         }
        //     })
        // },
        // // 关闭弹窗
        // saveclosure(op){
        //     this.dialog.save = op
        // },
        // //查看
		// table_show(row){
		// 	this.dialog.info = true
		// 		this.$nextTick(() => {
		// 		this.$refs.infoDialog.setData(row)
		// 	})
		// },
        // 分页事件
        handleSizeChange(val) {
            // console.log(`每页 ${val} 条`)
            this.query.per_page = val
            this.list_post()
        },
        handleCurrentChange(val) {
            // console.log(`当前页: ${val}`)
            this.query.page = val
            this.list_post()
        },
        // 搜索
        Query_button() {
            // console.log(this.query, '12321321')
            this.list_post()
        },
        // 重置查询表单
        resetQuery() {
            this.query = {
                per_page: 10,
                page: 1,
                status:1
            }
            this.list_post()
        }
    }
}
</script>

<style scoped>
.scTable:deep(.el-table__footer) .cell {
    font-weight: bold;
}
.scTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-horizontal {
    height: 12px;
    border-radius: 12px;
}
.scTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-vertical {
    width: 12px;
    border-radius: 12px;
}
</style>
