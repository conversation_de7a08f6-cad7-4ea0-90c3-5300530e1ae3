<template>
    <!-- form -->
    <template v-if="component == 'form'">
        <yy_form
            :modelValue="modelValue"
            :filed="dialogformitems"
            @submit="submit"
            @goBack="handleClose"
        ></yy_form>
    </template>
</template>

<script>
export default {
    name: 'contentslot',
    props: {
        // 内容类型
        components: { type: String, default: '' },
        // 配置内容
        options:{type:Object,default:() =>{}}
    },
    data() {
        return {}
    }
}
</script>

<style>
</style>