<template>
    <el-container>
        <el-header>
            <div class="left-panel">
                <el-button type="primary" icon="el-icon-plus" @click="add">添加</el-button>
            </div>
            <div class="right-panel">
                <div class="right-panel-search">
                    <el-input v-model="query.keyword" placeholder="模糊查询" clearable></el-input>
                    <el-button type="primary" icon="el-icon-search" @click="Query_button"></el-button>
                </div>
            </div>
        </el-header>
        <el-main class="nopadding" v-loading="loading">
            <div class="table-container">
                <el-table :data="tableData" border style="width: 100%" max-height="760" table-layout="fixed">
                    <!-- <template #empty>
                    <el-empty
                        description="暂无数据"
                        :image-size="100"
                    ></el-empty>
                </template> -->
                    <!-- <el-table-column prop="name" label="实例名称"  /> -->
                    <el-table-column prop="title" label="车间"  />
                    <el-table-column prop="code" label="代码"  />
                    <el-table-column prop="hour_rate" label="车间费率" />
                    <el-table-column fixed="right" label="操作" min-width="120">
                        <template #default="scope">
                            <el-button link type="primary" size="small" @click="handleClick(scope.row)">
                                修改
                            </el-button>
                            <el-button link type="primary" size="small" @click="delItem(scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div style="margin-top: 10px;">
                <el-pagination v-model:current-page="query.page" v-model:page-size="query.pageSize"
                    :page-sizes="[20, 50, 100]" :size="size" :disabled="disabled" :background="background"
                    layout="sizes, prev, pager, next" :total="query.total" @size-change="handleSizeChange"
                    @current-change="handleCurrentChange" />
            </div>
        </el-main>
        <addForm ref="addFromRef" :getDataList="getDataList"></addForm>
    </el-container>
</template>
<script setup name="laborRate">
import http from '@/utils/request';
import addForm from './components/addForm.vue';
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { ElMessage } from 'element-plus';
let addFromRef = ref()
let loading = ref(false)
let tableData = ref([
    // {
    //     name: "车间费率",
    //     title: "组装车间",
    //     code: "20025",
    //     hour_rate: 2.79,
    // },
])
let query = ref({
    page: 1,
    pageSize: 20,
    total: 100,
    keyword: null
})
function add() {
    addFromRef.value.open("新建车间费率管理")
}
function handleClick(e) {
    addFromRef.value.editItem("修改车间费率管理", e)
}
function getDataList() {
    loading.value = true
    http.get("center/get_ls",query.value).then((res)=>{
        loading.value = false
        if(res.errcode === 0){
            query.value.total = res.result.total
            tableData.value = res.result.data
        }
        console.log(res,"res")
    }).catch(err=>{
        loading.value = false
    })
}
getDataList()

// 处理键盘事件，按回车键触发查询
function handleKeyDown(event) {
    // 如果按下的是回车键（keyCode 13）
    if (event.keyCode === 13 || event.key === 'Enter') {
        // 检查当前焦点是否在输入框上
        const activeElement = document.activeElement;
        const isInputFocused = activeElement.tagName === 'INPUT' &&
                              activeElement.classList.contains('el-input__inner');

        // 如果焦点在搜索输入框上，或者没有特定元素获得焦点
        if (isInputFocused || activeElement === document.body) {
            // 触发查询按钮点击事件
            Query_button();
        }
    }
}

// 在组件挂载时添加键盘事件监听器
onMounted(() => {
    document.addEventListener('keydown', handleKeyDown);
});

// 在组件卸载前移除键盘事件监听器
onBeforeUnmount(() => {
    document.removeEventListener('keydown', handleKeyDown);
});

function delItem(e) {
    http.post("center/post_del",{id:e.id}).then((res)=>{
        if(res.errcode === 0){
            ElMessage.success("删除成功")
            getDataList()
        }else{
            ElMessage.success("删除失败")
        }
    })
}
function Query_button() {
    getDataList()
}
const handleSizeChange = (val) => {
    query.value.pageSize = val
    getDataList()
}
const handleCurrentChange = (val) => {
    query.value.page = val
    getDataList()
}
</script>
<style lang="scss" scoped>
.el-main.nopadding {
    padding: 0;
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
}

// .table-container {
// flex: 1;
// max-height: calc(100vh - 200px);
/* 根据实际布局调整 */
// overflow: auto;
// }</style>