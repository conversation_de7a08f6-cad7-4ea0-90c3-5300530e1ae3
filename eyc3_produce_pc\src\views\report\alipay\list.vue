<!--
 * @author: 风源
 * @name: 类名
 * @desc: 介绍
 * @LastEditTime: 2022-12-15 20:58:22
 * @FilePath: \eyc3_canyin_pc\src\views\report\alipay\list.vue
-->
<template>
    <yp_list
        ref="table"
        :url="url"
        row-key="id"
        :columns="columns"
        stripe
        :add="add"
        :derive="derive"
        :formitems="formitems"
    >
    </yp_list>
</template>

<script>
export default {
    name: 'ReportAlipayist',
    data() {
        return {
            url: 'report/get_alipay_log_ls',
            columns: [
                {
                    label: '订单标题',
                    prop: 'subject'
                },
                {
                    label: '订单金额',
                    prop: 'total_amount'
                },

                {
                    label: '实收金额',
                    prop: '实收金额'
                },
                {
                    label: '总退款金额',
                    prop: 'refund_fee'
                },
                {
                    label: '实际退款金额',
                    prop: 'send_back_fee'
                },

                {
                    label: '交易状态',
                    prop: 'trade_status'
                },
                {
                    label: '支付宝交易号',
                    prop: 'trade_no'
                },
                {
                    label: '买家支付宝账号',
                    prop: 'buyer_logon_id'
                },
                {
                    label: '商户订单号',
                    prop: 'out_trade_no'
                },                
            ],
            derive: {
                filename:"支付宝流水"
            },
            formitems: [
                {
                    label: '日期范围',
                    name: 'date_interval',
                    value: [
                        this.$TOOL.dateFormat(new Date(), 'yyyy-MM-01'),
                        this.$TOOL.dateFormat(new Date(), 'yyyy-MM-dd')
                    ],
                    component: 'date',
                    options: {
                        type: 'daterange',
                        rangeseparator: '至',
                        startplaceholder: '开始日期',
                        endplaceholder: '结束日期',
                        valueFormat: 'YYYY-MM-DD'
                    },
                    rules: [
                        {
                            required: true,
                            message: 'Please input Data',
                            trigger: 'change'
                        }
                    ]
                },	
            ]
        }
    }
}
</script>

<style></style>
