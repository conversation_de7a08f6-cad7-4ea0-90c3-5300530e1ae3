<!--
 * @author: 风源
 * @name: 类名
 * @desc: 介绍
 * @LastEditTime: 2022-12-30 15:55:07
 * @FilePath: \eyc3_canyin_pc\src\views\user\list.vue
-->
<template>
    <yp_list
        ref="table"
        :url="url"
        :columns="columns"
        :formitems="formitems"
        :derive="derive"
        :buttonList="buttonList"
    >
    </yp_list>
</template>

<script>
export default {
    name: 'UserList',
    data() {
        return {
            url: 'user/get_ls',
            columns: [
                {
                    label: '姓名',
                    prop: 'name'
                },
                {
                    label: 'userid',
                    prop: 'userid'
                },
                {
                    label: '部门名称',
                    prop: 'department_name'
                },
                {
                    label: '部门路径',
                    prop: 'dept_title'
                },
                {
                    label: '地址',
                    prop: 'area'
                },
                {
                    label: '操作',
                    prop: 'action',
                    component: 'button',
                    options: [
                        {
                            label: '编辑',
                            component: 'dialog',
                            options: {
                                // name: 'UserEdit',
                                component: 'form',
                                size: 'small',
                                remote: {
                                    state: 'edit',
                                    label: '修改用户信息',
                                    api: 'user/get_info',
                                    edit: 'user/post_modify',
                                    data: {
                                        userid: '$userid'
                                    },
                                    submitData: ['dept_id', 'department_name']
                                },
                                columns: [
                                    { label: '编辑', component: 'title' },
                                    {
                                        label: '姓名',
                                        name: 'name',
                                        component: 'input',
                                        options: {
                                            disabled: true
                                        }
                                    },
                                    {
                                        label: '部门名称',
                                        name: 'department_name',
                                        component: 'select',
                                        options: {
                                            remote: {
                                                api: 'user/get_user_dep_list',
                                                data: {
                                                    corpid: '$corpid',
                                                    userid: '$userid'
                                                },
                                                label: 'name',
                                                value: 'name',
                                                relatedata: {
                                                    department_name: '$name',
                                                    dept_id: '$dept_id'
                                                },
                                                notpreload: true 
                                            }
                                        }
                                    }
                                ]
                            }
                        }
                    ]
                }
            ],
            derive: {
                url: 'user/get_user_template',
                filename: '用户列表'
            },
            // buttonList: [
            //     {
            //         component: 'upload',
            //         url: 'user/post_upload_charge',
            //         label: '批量导入'
            //     }
            // ],
            formitems: [
                {
                    label: '姓名',
                    name: 'keyword',
                    component: 'input',
                    options: {
                        placeholder: '请输入'
                    }
                }
            ]
        }
    }
}
</script>

<style lang="scss" scoped>
</style>
