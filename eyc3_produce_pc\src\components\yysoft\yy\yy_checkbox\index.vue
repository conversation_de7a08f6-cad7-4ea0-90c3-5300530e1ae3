<template>
    <div class="deliverySetting">
        <div class="deliverySetting-table">
            <div class="table-head">
                <div class="selection">
                    <el-checkbox
                        :indeterminate="indeterminate"
                        v-model="ischeckAll"
                        @change="handleCheckAllChange"
                    ></el-checkbox>
                </div>
                <div class="width185">全部</div>
                <div class="width265">权限</div>
            </div>
            <div
                class="table-body"
                v-for="(partition, partitionIndex) in distributorsInfo"
                :key="partitionIndex"
            >
                <div class="selection">
                    <p>
                        <el-checkbox
                            :indeterminate="partition.indeterminate"
                            v-model="partition.selected"
                            @change="handleCheckedCountryAllChange(partitionIndex, $event, partition)"
                            :key="partitionIndex"
                        ></el-checkbox>
                    </p>
                </div>
                <div class="width185">
                    <p>{{ partition.title }}</p>
                </div>
                <div class="width265">
                    <el-checkbox
                        v-for="country in JSON.parse(partition.acts)"
                        v-model="country.selected"
                        @change="handleCheckedCountryChange(partitionIndex, country.title, $event, country, partition)"
                        :label="country"
                        :key="country"
                        >{{ country.title }}</el-checkbox
                    >
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { ElMessage } from 'element-plus'
export default {
    name: 'yy_checkbox',
    components: {},
    props: {
        model: { type: String, default: '' },
        name: { type: String, default: '' },
        form: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data() {
        return {
            distributorsInfo: [],
            ischeckAll: false, //一级全选状态
            indeterminate: false,
            arraylist: [], //选中状态的数据
            ischeckboxs: [], //提交选中数据
            arrAy: []
        }
    },
    computed: {},
    created() {},
    mounted() {
        this.getData()
    },
    watch: {
        arraylist() {
            let array = this.deepClone(this.arraylist)
            let item = []
            array.forEach((i) => {
                let arr = JSON.parse(i.acts)
                arr.forEach((obj) => {
                    if (obj.selected) {
                        item.push(obj)
                    }
                })
                i.acts = JSON.stringify(item)
                item = []
            })
            let arrays = []
            array.forEach((res) => {
                if (res.acts != '[]') {
                    arrays.push(res)
                }
            })
            array = arrays
            // this.form[this.model.name] = JSON.stringify(array)
            // console.log('------------------')
            let postdata = JSON.parse(JSON.stringify(array))
            var poatdatacopys = []
            postdata.forEach((item) => {
                // console.log(item.acts)
                // console.log(JSON.parse(item.acts))
                // item.acts = JSON.parse(item.acts)
                var postobj = {}
                for (let i in item) {
                    if (i == 'acts') {
                        postobj[i] = JSON.parse(item.acts)
                    } else {
                        postobj[i] = item[i]
                    }
                }
                poatdatacopys.push(postobj)
            })
            // console.log(JSON.stringify(poatdatacopys))
            this.form[this.model.name] = JSON.stringify(poatdatacopys)
            this.form.action_titles = ''
        }
    },
    methods: {
        // 深拷贝
        deepClone(obj) {
            //判断拷贝的要进行深拷贝的是数组还是对象，是数组的话进行数组拷贝，对象的话进行对象拷贝
            var objClone = Array.isArray(obj) ? [] : {}
            //进行深拷贝的不能为空，并且是对象或者是
            if (obj && typeof obj === 'object') {
                for (let key in obj) {
                    if (obj.hasOwnProperty(key)) {
                        if (obj[key] && typeof obj[key] === 'object') {
                            objClone[key] = this.deepClone(obj[key])
                        } else {
                            objClone[key] = obj[key]
                        }
                    }
                }
            }
            return objClone
        },
        //处理远程选项数据
        getData() {
            this.$HTTP.get(this.model.options.remote.api, this.model.options.remote.postData).then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    res.result.forEach((e) => {
                        e.selected = false
                        var actArray = JSON.parse(e.acts)
                        actArray.forEach((res) => {
                            res.selected = false
                        })
                        e.acts = JSON.stringify(actArray)
                        // e.acts = actArray
                    })
                    if (this.model.type == 'add') {
                        this.distributorsInfo = res.result
                    } else if (this.model.type == 'info' || 'edit') {
                        let arr = JSON.parse(this.form.actions)
                        arr.forEach((i) => {
                            let atc = null
                            if (typeof i.acts == 'object') {
                                atc = i.acts
                            } else if (typeof i.acts == 'string') {
                                atc = JSON.parse(i.acts)
                            }
                            res.result.forEach((obj) => {
                                let act = JSON.parse(obj.acts)
                                if (obj.id == i.id) {
                                    obj.selected = true
                                    atc.forEach((res) => {
                                        act.forEach((item) => {
                                            if (res.act == item.act) {
                                                item.selected = true
                                            }
                                        })
                                    })
                                }
                                obj.acts = JSON.stringify(act)
                            })
                        })
                        this.arraylist = res.result
                        this.distributorsInfo = res.result
                    }
                }
            })
        },
        handleCheckAllChange(e) {
            this.arrAy = this.distributorsInfo
            //一级change事件
            this.ischeckAll = e
            if (e == true) {
                this.indeterminate = false
                //二级全选反选不确定
                this.distributorsInfo.forEach((i) => {
                    i.selected = e
                    var obj = JSON.parse(i.acts)
                    obj.forEach((j) => {
                        j.selected = e
                        i.acts = JSON.stringify(obj)
                    })
                })
                this.arrAy.forEach((i) => {
                    this.arraylist.push(i)
                })
                var newArr = [...new Set(this.arraylist)]
                this.arraylist = newArr
            } else {
                this.indeterminate = false
                //三级全选反选不确定
                this.distributorsInfo.forEach((i) => {
                    i.selected = e
                    var obj = JSON.parse(i.acts)
                    obj.forEach((j) => {
                        j.selected = e
                        i.acts = JSON.stringify(obj)
                    })
                })
                this.arraylist = []
            }
        },
        handleCheckedCountryAllChange(index, e, partition) {
            //二级change事件
            let Array = []
            this.arraylist.forEach((obj) => {
                if (obj.selected != false) {
                    Array.push(obj)
                    this.arraylist = Array
                } else {
                    this.arraylist.push(partition)
                }
            })
            var newArr = [...new Set(this.distributorsInfo)]
            this.distributorsInfo = newArr
            if (partition.selected == true) {
                Array.push(partition)
                var newArr = [...new Set(Array)]
                this.arraylist = newArr
            }
            this.distributorsInfo[index].selected = e //二级勾选后，子级全部勾选或者取消
            if (e == false) this.distributorsInfo[index].indeterminate = false //去掉二级不确定状态
            var obj = JSON.parse(this.distributorsInfo[index].acts)
            if (obj)
                obj.forEach((i) => {
                    i.selected = e
                })
            this.distributorsInfo[index].acts = JSON.stringify(obj)
            this.getIsCheckAll()
        },
        handleCheckedCountryChange(topIndex, sonId, e, country, partition) {
            var obj = JSON.parse(this.distributorsInfo[topIndex].acts)
            var tickCount = 0,
                unTickCount = 0,
                len = obj.length
            for (var i = 0; i < len; i++) {
                if (sonId == obj[i].title) {
                    obj[i].selected = e
                    this.distributorsInfo[topIndex].acts = JSON.stringify(obj)
                }
                if (obj[i].selected == true) tickCount++
                if (obj[i].selected == false) unTickCount++
            }
            if (tickCount == len) {
                //三级级全勾选
                this.distributorsInfo[topIndex].selected = true
                this.distributorsInfo[topIndex].indeterminate = false
            } else if (unTickCount == len) {
                //三级级全不勾选
                this.distributorsInfo[topIndex].selected = false
                this.distributorsInfo[topIndex].indeterminate = false
            } else {
                this.distributorsInfo[topIndex].selected = false
                this.distributorsInfo[topIndex].indeterminate = true //添加二级不确定状态
            }

            this.getIsCheckAll()
            //三级change事件
            if (this.arraylist.length == 0) {
                this.arraylist.push(partition)
            } else {
                this.arraylist.forEach((item) => {
                    if (item.id == partition.id) {
                        item = partition
                    } else {
                        this.arraylist.push(partition)
                        var newArr = [...new Set(this.arraylist)]
                        this.arraylist = newArr
                    }
                })
            }
            var newArr = [...new Set(this.distributorsInfo)]
            this.distributorsInfo = newArr
        },
        getIsCheckAll() {
            var tickCount = 0,
                unTickCount = 0,
                ArrLength = this.distributorsInfo.length
            for (var j = 0; j < ArrLength; j++) {
                //全选checkbox状态
                if (this.distributorsInfo[j].selected == true) tickCount++
                if (this.distributorsInfo[j].selected == false) unTickCount++
            }
            if (tickCount == ArrLength) {
                //二级全勾选
                // console.log('二级全勾选')
                this.ischeckAll = true
                this.indeterminate = false
            } else if (unTickCount == ArrLength) {
                //二级全不勾选
                // console.log('二级全不勾选')
                this.ischeckAll = false
                this.indeterminate = false
            } else {
                // console.log('添加一级不确定状态')
                this.ischeckAll = false
                this.indeterminate = true //添加一级不确定状态
            }
        }
    }
}
</script>
<style lang="scss">
.deliverySetting {
    padding: 20px 0;
    position: relative;
    .el-table {
        thead {
            tr {
                th {
                    font-size: 14px;
                }
            }
        }
        tbody {
            tr {
                td {
                    vertical-align: baseline;
                    p {
                        line-height: 30px;
                    }
                    .el-checkbox-group {
                        display: flex;
                        flex-direction: column;
                        label {
                            line-height: 30px;
                            margin-left: 0;
                        }
                    }
                }
            }
        }
    }
    .deliverySetting-table {
        font-size: 14px;
        color: #333;
        .table-head,
        .table-body {
            display: flex;
            padding: 10px 0;
            .selection {
                width: 45px;
                text-align: center;
                line-height: 36px;
            }
            .width185 {
                width: 185px;
            }
            .width265 {
                width: 265px;
            }
        }
        .table-head {
            height: 36px;
            align-items: center;
            background-color: #e7f2ff;
        }
        .table-body {
            border-bottom: 1px solid #e4e4e4;
            color: #666;
            &:hover {
                background-color: #f5f7fa;
            }
            .width265 {
                display: flex;
                flex-direction: column;
                label {
                    line-height: 30px;
                    margin-left: 0;
                    color: #666;
                }
            }
            p {
                line-height: 30px;
            }
        }
    }
}
</style>
