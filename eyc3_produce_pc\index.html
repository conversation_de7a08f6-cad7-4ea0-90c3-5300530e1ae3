<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width,initial-scale=1.0">
		<link rel="icon" href="/favicon.ico">
		<title>云三智造</title>
		<script type="text/javascript">
			document.write("<script src='config.js?"+new Date().getTime()+"'><\/script>");
		</script>
		<!-- <link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/luckysheet@2.1.12/dist/plugins/css/pluginsCss.css' /> -->
		<!-- <link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/luckysheet@2.1.12/dist/plugins/plugins.css' /> -->
		<!-- <link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/luckysheet@2.1.12/dist/css/luckysheet.css' /> -->
		<!-- <link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/luckysheet@2.1.12/dist/assets/iconfont/iconfont.css' /> -->
		<!-- <script src="https://cdn.jsdelivr.net/npm/luckysheet@2.1.12/dist/plugins/js/plugin.js"></script> -->
		<!-- <script src="https://cdn.jsdelivr.net/npm/luckysheet@2.1.12/dist/luckysheet.umd.js"></script> -->
		<link rel='stylesheet' href='/static/luckysheet/pluginsCss.css' />
		<link rel='stylesheet' href='/static/luckysheet/plugins.css' />
		<link rel='stylesheet' href='/static/luckysheet/luckysheet.css' />
		<link rel='stylesheet' href='/static/luckysheet/iconfont.css' />
		<script src="/static/luckysheet/plugin.js"></script>
		<script src="/static/luckysheet/luckysheet.umd.js"></script>
	</head>
	<body>
		<noscript>
			<strong>We're sorry but soft doesn't work properly without JavaScript
				enabled. Please enable it to continue.</strong>
		</noscript>
		<script type="text/javascript">
			var dark = window.localStorage.getItem('APP_DARK');
			if(dark){
				document.documentElement.classList.add("dark")
			}
		</script>
<script src="https://unpkg.com/vconsole@latest/dist/vconsole.min.js"></script>
<!-- <script src="https://cdn.jsdelivr.net/npm/vconsole@latest/dist/vconsole.min.js"></script> -->
<!-- <script type="text/javascript" src="http://js.eykj.cn/vconsole.min.js"></script> -->

<script>
  // VConsole 默认会挂载到 `window.VConsole` 上
  var vConsole = new window.VConsole();
</script>

		<div id="app" class="aminui">
			<div class="app-loading">
				<div class="app-loading__logo">
					<img src="/img/logo.png"/>
				</div>
				<div class="app-loading__loader"></div>
				<div class="app-loading__title">云三智造</div>
			</div>
			<style>
				.app-loading {position: absolute;top:0px;left:0px;right:0px;bottom:0px;display: flex;justify-content: center;align-items: center;flex-direction: column;background: #fff;}
				.app-loading__logo {margin-bottom: 30px;}
				.app-loading__logo img {width: 90px;vertical-align: bottom;}
				.app-loading__loader {box-sizing: border-box;width: 35px;height: 35px;border: 5px solid transparent;border-top-color: #000;border-radius: 50%;animation: .5s loader linear infinite;position: relative;}
				.app-loading__loader:before {box-sizing: border-box;content: '';display: block;width: inherit;height: inherit;position: absolute;top: -5px;left: -5px;border: 5px solid #ccc;border-radius: 50%;opacity: .5;}
				.app-loading__title {font-size: 24px;color: #333;margin-top: 30px;}
				.dark .app-loading {background: #222225;}
				.dark .app-loading__loader {border-top-color: #fff;}
				.dark .app-loading__title {color: #d0d0d0;}
				@keyframes loader {
				    0% {
				        transform: rotate(0deg);
				    }
				    100% {
				        transform: rotate(360deg);
				    }
				}
			</style>
		</div>
		<!-- built files will be auto injected -->
	</body>
	<div id="versionCheck" style="display: none;position: absolute;z-index: 99;top:0;left:0;right:0;bottom:0;padding:40px;background:rgba(255,255,255,0.9);color: #333;">
		<h2 style="line-height: 1;margin: 0;font-size: 24px;">当前使用的浏览器内核版本过低 :(</h2>
		<p style="line-height: 1;margin: 0;font-size: 14px;margin-top: 20px;opacity: 0.8;">当前版本：<span id="versionCheck-type">--</span> <span id="versionCheck-version">--</span></p>
		<p style="line-height: 1;margin: 0;font-size: 14px;margin-top: 10px;opacity: 0.8;">最低版本要求：Chrome 71+、Firefox 65+、Safari 12+、Edge 97+。</p>
		<p style="line-height: 1;margin: 0;font-size: 14px;margin-top: 10px;opacity: 0.8;">请升级浏览器版本，或更换现代浏览器，如果你使用的是双核浏览器,请切换到极速/高速模式。</p>
	</div>
	<script type="text/javascript">
	function getBrowerInfo(){
		var userAgent = window.navigator.userAgent;
		var browerInfo = {
			type: 'unknown',
			version: 'unknown',
			userAgent: userAgent
		};
		if(document.documentMode){
			browerInfo.type = "IE"
			browerInfo.version = document.documentMode + ''
		}else if(indexOf(userAgent, "Firefox")){
			browerInfo.type = "Firefox"
			browerInfo.version = userAgent.match(/Firefox\/([\d.]+)/)[1]
		}else if(indexOf(userAgent, "Opera")){
			browerInfo.type = "Opera"
			browerInfo.version = userAgent.match(/Opera\/([\d.]+)/)[1]
		}else if(indexOf(userAgent, "Edg")){
			browerInfo.type = "Edg"
			browerInfo.version = userAgent.match(/Edg\/([\d.]+)/)[1]
		}else if(indexOf(userAgent, "Chrome")){
			browerInfo.type = "Chrome"
			browerInfo.version = userAgent.match(/Chrome\/([\d.]+)/)[1]
		}else if(indexOf(userAgent, "Safari")){
			browerInfo.type = "Safari"
			browerInfo.version = userAgent.match(/Safari\/([\d.]+)/)[1]
		}
		return browerInfo
	}
    function indexOf(userAgent, brower) {
        return userAgent.indexOf(brower) > -1
    }
	function isSatisfyBrower(){
		var minVer = {
			"Chrome": 71,
			"Firefox": 65,
			"Safari": 12,
			"Edg": 97,
			"IE": 999
		}
		var browerInfo = getBrowerInfo()
		var materVer = browerInfo.version.split('.')[0]
        return materVer >= minVer[browerInfo.type]
	}
	if(!isSatisfyBrower()){
		document.getElementById('versionCheck').style.display = 'block';
		document.getElementById('versionCheck-type').innerHTML = getBrowerInfo().type;
		document.getElementById('versionCheck-version').innerHTML = getBrowerInfo().version;
	}
	</script>
	<script type="module" src="/src/main.js"></script>
</html>
