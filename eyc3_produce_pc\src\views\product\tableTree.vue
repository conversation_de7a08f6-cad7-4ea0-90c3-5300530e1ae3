<template>
    <el-breadcrumb
        :separator-icon="ArrowRight"
        style="padding-bottom: 10px"
    >
        <el-breadcrumb-item
            v-for="item in arraylist"
            :key="item"
            ><a
                href="javascript:void(0);"
                @click="breadClick(item)"
                >{{ item.title }}</a
            ></el-breadcrumb-item
        >
    </el-breadcrumb>
    <el-container class="teble-content">
        <el-main
            class="nopadding"
            v-loading="pictLoading"
        >
            <el-table
                ref="multipleTableRef"
                :data="list.tableData"
                style="width: 100%; height: 100%"
                :fit="true"
                :highlight-current-row="true"
                :row-class-name="tableRowClassName"
                @row-contextmenu="rowContextmenu"
                @selection-change="handleSelectionChange"
            >
                <template #empty>
                    <el-empty
                        description="暂无数据"
                        :image-size="200"
                    ></el-empty>
                </template>
                <el-table-column
                    type="selection"
                    width="55"
                >
                </el-table-column>
                <el-table-column
                    type="index"
                    label="序号"
                    align="center"
                    width="50"
                >
                </el-table-column>
                <el-table-column
                    width="50"
                    label="图标"
                    #default="scope"
                >
                    <el-icon
                        size="16px"
                        color="color"
                        v-if="scope.row.type == 3"
                    >
                        <!-- 原材料 -->
                        <sc-icon-raw-material />
                    </el-icon>
                    <el-icon
                        size="16px"
                        color="color"
                        v-if="scope.row.type == 0"
                    >
                        <!-- 产品 -->
                        <sc-icon-product />
                    </el-icon>
                    <el-icon
                        size="16px"
                        color="color"
                        v-if="scope.row.type == 2"
                    >
                        <!-- 零件 -->
                        <sc-icon-part />
                    </el-icon>
                    <el-icon
                        size="16px"
                        color="red"
                        v-if="scope.row.type == 1"
                    >
                        <!-- 部件 -->
                        <sc-icon-mponent />
                    </el-icon>
                </el-table-column>
                <!-- <el-table-column
                    label="ID"
                    min-width="80"
                    align="center"
                    sortable
                >
                    <template #default="scope">
                        <span @click.stop="titleClick(scope.row)">{{ scope.row.id }}</span>
                    </template>
                </el-table-column> -->
                <el-table-column
                    prop="number"
                    label="代号"
                    min-width="80"
                    align="center"
                    sortable
                >
                    <template #default="scope">
                        <span @click.stop="titleClick(scope.row)">{{ scope.row.number }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="title"
                    label="名称"
                    min-width="80"
                    align="center"
                    sortable
                >
                    <template #default="scope">
                        <span @click.stop="titleClick(scope.row)">{{ scope.row.title }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="drawing_number"
                    label="图号"
                    min-width="80"
                    align="center"
                    sortable
                >
                    <template #default="scope">
                        <span @click.stop="titleClick(scope.row)">{{ scope.row.drawing_number }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="specs"
                    label="规格"
                    min-width="80"
                    align="center"
                    sortable
                >
                    <template #default="scope">
                        <span @click.stop="titleClick(scope.row)">{{ scope.row.specs }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="texture_title"
                    label="材质"
                    min-width="80"
                    align="center"
                    sortable
                >
                    <template #default="scope">
                        <span @click.stop="titleClick(scope.row)">{{ scope.row.texture_title }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="dosage"
                    label="用量"
                    min-width="80"
                    align="center"
                    sortable
                >
                    <template #default="scope">
                        <el-input
                            v-if="scope.row.edit"
                            v-model="scope.row.dosage"
                            @input="handleInput(scope.row.dosage)"
                            placeholder="用量"
                        ></el-input>
                        <span v-else>{{ scope.row.dosage }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    label="操作"
                    align="center"
                    min-width="100"
                    fixed="right"
                >
                    <template #default="scope">
                        <el-popconfirm
                            title="确定删除吗？"
                            @confirm="handleDelete(scope)"
                        >
                            <template #reference>
                                <el-button
                                    type="danger"
                                    size="medium"
                                    plain
                                     :disabled="!isShowBatch"
                                >
                                    <span
                                        class="el-icon-delete"
                                        aria-hidden="true"
                                        >删除</span
                                    >
                                </el-button>
                            </template>
                        </el-popconfirm>
                        <!-- <el-button
                            @click.stop="confirmData(scope.row)"
                            v-if="scope.row.edit"
                            type="success"
                            size="medium"
                        >
                            <span
                                class="el-icon-check"
                                aria-hidden="true"
                                >保存</span
                            >
                        </el-button> -->
                        <!-- <el-button
                            @click.stop="cancellation(scope.row)"
                            v-if="scope.row.edit"
                            type="info"
                            size="medium"
                        >
                            <sapn
                                class="el-icon-check"
                                aria-hidden="false"
                                >取消</sapn
                            >
                        </el-button> -->
                        <!-- <div v-else>
                            <el-button
                                type="primary"
                                size="medium"
                                @click.stop="editData(scope.row)"
                                plain
                            >
                                <span
                                    class="el-icon-edit"
                                    aria-hidden="true"
                                    >修改</span
                                >
                            </el-button>

                        </div> -->
                    </template>
                </el-table-column>
                <!-- <template #button>

                </template> -->
            </el-table>
            <div
                style="margin-top: 20px; position: fixed; z-index: 99999"
            >
                <!-- 批量修改按钮组 -->
                <el-button
                    v-if="isShowBatch"
                    @click.stop="confirmDataBatch()"
                    type="success"
                    size="medium"
                >
                    <span
                        class="el-icon-check"
                        aria-hidden="true"
                        >批量保存</span
                    >
                </el-button>
                <el-button
                    type="primary"
                    v-else
                    @click="toggleAllForEdit()"
                    >批量修改</el-button
                >
                <el-button
                    v-if="isShowBatch"
                    @click.stop="cancellation()"
                    type="info"
                    size="medium"
                >
                    <span
                        class="el-icon-check"
                        aria-hidden="false"
                        >取消</span
                    >
                </el-button>
            </div>
        </el-main>
    </el-container>
    <div
        class="box-menu"
        v-if="menuVisible"
        :style="{ left: menu_left + 'px', top: menu_top + 'px' }"
    >
        <div @click.stop="tree_add()">
            <el-button
                type="link"
                icon="el-icon-plus"
                >BOM添加</el-button
            >
        </div>
        <!-- <div @click.stop="pegging_click()">
            <el-button
                type="link"
                icon="el-icon-edit"
                >BOM反查</el-button
            >
        </div> -->
        <div @click.stop="cost_click()">
            <el-button
                type="link"
                icon="el-icon-document"
                >BOM成本</el-button
            >
        </div>
    </div>
    <el-drawer
        ref="drawerRef"
        v-model="dialog"
        title="添加子集"
        :before-close="handleClose"
        direction="rtl"
        class="demo-drawer"
        size="70%"
    >
        <div class="demo-drawer__content">
            <div class="formType">
                <el-form
                    :model="form"
                    label-position="top"
                    :rules="rules"
                    ref="form"
                    :inline="true"
                    class="demo-form-inline"
                >
                    <div
                        class="title"
                        style="width: 100%"
                    >
                        基本属性
                    </div>
                    <el-form-item
                        label="父级物料编号"
                        :label-width="formLabelWidth"
                    >
                        <el-input
                            v-model="form.parent_material_number"
                            placeholder="请输入内容"
                            style="min-width: 300px"
                            :disabled="true"
                        />
                    </el-form-item>
                    <el-form-item
                        label="标签"
                        :label-width="formLabelWidth"
                    >
                        <el-select
                            v-model="form.tags"
                            multiple
                            filterable
                            allow-create
                            default-first-option
                            collapse-tags
                            collapse-tags-tooltip
                            @visible-change="tagsgetData"
                            :loading="tagsloading"
                            placeholder="请选择标签"
                            style="min-width: 300px"
                        >
                            <el-option
                                v-for="item in tagsArray"
                                :key="item.id"
                                :label="item.title"
                                :value="item.title"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        label="编号"
                        :label-width="formLabelWidth"
                        prop="material_number"
                    >
                        <el-autocomplete
                            class="inline-input"
                            v-model="form.material_number"
                            :fetch-suggestions="querySearch"
                            placeholder="请输入内容"
                            :trigger-on-focus="false"
                            style="min-width: 300px"
                            @select="
                                (item) => {
                                    handleSelect(item)
                                }
                            "
                            clearable
                        >
                            <template #default="{ item }">
                                <div class="name">{{ item.title }}</div>
                            </template>
                        </el-autocomplete>
                    </el-form-item>
                    <el-form-item
                        label="名称"
                        :label-width="formLabelWidth"
                        prop="title"
                    >
                        <el-autocomplete
                            class="inline-input"
                            v-model="form.title"
                            :fetch-suggestions="querySearch"
                            placeholder="请输入内容"
                            :trigger-on-focus="false"
                            style="min-width: 300px"
                            @select="
                                (item) => {
                                    handleSelect(item)
                                }
                            "
                            clearable
                        >
                            <template #default="{ item }">
                                <div class="name">{{ item.title }}</div>
                            </template>
                        </el-autocomplete>
                        <!-- <el-input v-model="form.title" placeholder="请输入内容" style="min-width: 300px;"/> -->
                    </el-form-item>
                    <el-form-item
                        label="类型"
                        :label-width="formLabelWidth"
                        prop="type"
                    >
                        <el-select
                            v-model="form.type"
                            placeholder="请选择"
                            style="min-width: 300px"
                        >
                            <el-option
                                v-for="item in listType"
                                :key="item.id"
                                :label="item.title"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        label="图号"
                        :label-width="formLabelWidth"
                        prop="drawing_number"
                    >
                        <el-autocomplete
                            class="inline-input"
                            v-model="form.drawing_number"
                            :fetch-suggestions="querySearch"
                            placeholder="请输入内容"
                            :trigger-on-focus="false"
                            style="min-width: 300px"
                            @select="
                                (item) => {
                                    handleSelect(item)
                                }
                            "
                            clearable
                        >
                            <template #default="{ item }">
                                <div class="name">{{ item.title }}</div>
                            </template>
                        </el-autocomplete>
                        <!-- <el-input v-model="form.drawing_number" placeholder="请输入内容" style="min-width: 300px;"/> -->
                    </el-form-item>
                    <el-form-item
                        label="规格"
                        :label-width="formLabelWidth"
                        prop="specs"
                    >
                        <el-input
                            v-model="form.specs"
                            placeholder="请输入内容"
                            style="min-width: 300px"
                        />
                    </el-form-item>
                    <el-form-item
                        label="单重"
                        :label-width="formLabelWidth"
                        prop="weight"
                    >
                        <el-input
                            v-model="form.weight"
                            placeholder="请输入内容"
                            style="min-width: 300px"
                        />
                    </el-form-item>
                    <el-form-item
                        label="单位"
                        :label-width="formLabelWidth"
                        prop="unit_title"
                    >
                        <el-select
                            v-model="form.unit_title"
                            placeholder="请选择"
                            style="min-width: 300px"
                            value-key="id"
                            filterable
                            @visible-change="unit_getData"
                            @change="selectUnit($event)"
                        >
                            <el-option
                                v-for="item in unitTitle"
                                :key="item.id"
                                :label="item.title"
                                :value="item.title"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        label="材质"
                        :label-width="formLabelWidth"
                        prop="texture_title"
                    >
                        <el-select
                            v-model="form.texture_title"
                            placeholder="请选择"
                            style="min-width: 300px"
                            value-key="id"
                            filterable
                            @visible-change="getData"
                            @change="selectget($event)"
                        >
                            <el-option
                                v-for="item in texture"
                                :key="item.id"
                                :label="item.title"
                                :value="item.title"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        label="分类"
                        :label-width="formLabelWidth"
                        v-if="categoryShow"
                        prop="category_title"
                    >
                        <el-select
                            v-model="form.category_title"
                            placeholder="请选择"
                            style="min-width: 300px"
                            value-key="id"
                            filterable
                            @visible-change="categorydata"
                            @change="selectCategory($event)"
                        >
                            <el-option
                                v-for="item in categoryArray"
                                :key="item.id"
                                :label="item.title"
                                :value="item.title"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        label="零件类型"
                        :label-width="formLabelWidth"
                        prop="part_type_title"
                    >
                        <el-select
                            v-model="form.part_type_title"
                            placeholder="请选择"
                            style="min-width: 300px"
                            value-key="id"
                            filterable
                            @visible-change="partType_title"
                            @change="partType($event)"
                        >
                            <el-option
                                v-for="item in partTypeArray"
                                :key="item.id"
                                :label="item.title"
                                :value="item.title"
                            />
                        </el-select>
                    </el-form-item>
                    <div
                        class="title"
                        style="width: 100%"
                    >
                        财务信息
                    </div>
                    <el-form-item
                        label="计划价"
                        :label-width="formLabelWidth"
                        prop="price"
                    >
                        <el-input
                            v-model="form.price"
                            placeholder="请输入内容"
                            style="min-width: 300px"
                        />
                    </el-form-item>
                    <div
                        class="title"
                        style="width: 100%"
                    >
                        工艺信息
                    </div>
                    <el-form-item
                        label="材料定额"
                        :label-width="formLabelWidth"
                        prop="quota"
                    >
                        <el-input
                            v-model="form.quota"
                            placeholder="请输入内容"
                            style="min-width: 300px"
                        />
                    </el-form-item>
                    <el-form-item
                        label="BOM量"
                        :label-width="formLabelWidth"
                        prop="dosage"
                    >
                        <el-input
                            v-model="form.dosage"
                            placeholder="请输入内容"
                            style="min-width: 300px"
                        />
                    </el-form-item>

                    <div class="demo-drawer__footer">
                        <el-button @click="cancelForm">关 闭</el-button>
                        <el-button
                            type="primary"
                            @click="onclickAdd('form')"
                            :loading="loading"
                            >{{ loading ? '提交中 ...' : '确 定' }}</el-button
                        >
                    </div>
                </el-form>
            </div>
        </div>
    </el-drawer>
    <div></div>
</template>

<script>
import { mapState } from 'vuex'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import jsPDF from 'jspdf'
export default {
    name: 'tableBase',
    data() {
        return {
            post_page: {
                keyword: ''
            },
            editTableData: [],
            // 接收反查的数据
            checkBom: null,
            pictLoading: false,
            menuVisible: false,
            menu_left: 0,
            menu_top: 0,
            currentPage: 1, //初始页
            pagesize: 10, //    每页的数据
            // 是否出现批量保存
            isShowBatch: false,
            // 是个显示批量修改
            isShowBatchModify: false,
            list: {
                tableData: [
                    {
                        date: '2016-05-03',
                        name: 'Tom',
                        state: 'California',
                        city: 'Los Angeles',
                        address: 'No. 189, Grove St, Los Angeles',
                        zip: 'CA 90036',
                        dis: 2
                    }
                ]
            },
            form: {
                parent_material_number: '',
                material_number: '',
                title: '',
                type: '',
                drawing_number: '',
                specs: '',
                weight: '',
                unit_title: '',
                unit_id: '',
                texture_id: '',
                texture_title: '',
                price: '',
                quota: '',
                part_type_id: '',
                part_type_title: '',
                dosage: '',
                category_title: '',
                category_id: '',
                tags: ''
            },
            rules: {
                title: [{ required: true, message: '请输入名称', trigger: 'blur' }],
                type: [{ required: true, message: '请选择类型', trigger: 'change' }],
                material_number: [{ required: true, message: '请输入编号', trigger: 'blur' }],
                unit_title: [{ required: true, message: '请选择单位', trigger: 'change' }],
                texture_title: [{ required: true, message: '请选择材质', trigger: 'change' }],
                category_title: [{ required: true, message: '请选择分类', trigger: 'change' }],
                drawing_number: [{ required: true, message: '请输入图号', trigger: 'blur' }],
                specs: [{ required: true, message: '请输入规格', trigger: 'blur' }],
                // weight: [
                //     { required: true, message: '请输入单重', trigger: 'blur' }
                // ],
                // price: [
                //     { required: true, message: '请输入计划价', trigger: 'blur' }
                // ],
                // quota: [
                //     { required: true, message: '请输入材料定额', trigger: 'blur' }
                // ],
                // dosage: [
                //     { required: true, message: '请输入BOM量', trigger: 'blur' }
                // ],
                part_type_title: [{ required: true, message: '请选择零件类型', trigger: 'change' }]
            },
            // 弹出框状态
            dialog: false,
            loading: false,
            arraylist: [],
            // 单位
            unitTitle: [],

            // 材质
            texture: [],
            // 零件类型
            partTypeArray: [],
            // BOM量显示
            dosaShow: false,
            // 分类数据
            categoryArray: [],
            // 分类显示
            categoryShow: false,
            // 弹出框title
            doing_title: '添加产品',
            //类型
            listType: [
                {
                    title: '部件',
                    value: 1
                },
                {
                    title: '零件',
                    value: 2
                },
                {
                    title: '原材料',
                    value: 3
                }
            ],
            // 标签
            tagsloading: false,
            tagsArray: [],
            tagstitle: [],
            dosageNum: ''
        }
    },
    computed: {
        ...mapState(['listObj'])
    },
    props: {
        // 添加一个页面标识符属性，用于区分不同页面
        pageType: {
            type: String,
            default: 'default'
        }
    },
    created() {
        // 使用页面标识符来获取正确的数据
        const arraylistKey = `arraylist_${this.pageType}_${sessionStorage.getItem('windowId')}`;
        if (sessionStorage.getItem(arraylistKey)) {
            this.arraylist = JSON.parse(sessionStorage.getItem(arraylistKey))
        }
        this.$store.watch(
            (state) => state.listObjTree,
            (newValue) => {
                // 使用页面标识符来获取正确的数据
                const materialNumberKey = `parent_material_number_${this.pageType}_${sessionStorage.getItem('windowId')}`;
                this.indata('bom/get_material', { material_number: sessionStorage.getItem(materialNumberKey) })
                this.arraylist = [this.$store.state.listObjTree.items]
            }
        )
        this.$store.watch(
            (state) => state.listObjTree.table,
            (newValue) => {
                // console.log(newValue,this.$store.state.listObj,'this.$store.state.listObj1231231');
                // 使用页面标识符来获取正确的数据
                const materialNumberKey = `parent_material_number_${this.pageType}_${sessionStorage.getItem('windowId')}`;
                this.indata('bom/get_material', { material_number: sessionStorage.getItem(materialNumberKey) })
            }
        )
        this.$store.watch(
            (state) => state.listObjTree.upload,
            (newValue) => {
                // 使用页面标识符来获取正确的数据
                const materialNumberKey = `parent_material_number_${this.pageType}_${sessionStorage.getItem('windowId')}`;
                this.indata('bom/get_material', { material_number: sessionStorage.getItem(materialNumberKey) })
            }
        )
        this.$store.watch(
            (state) => state.technology,
            (newValue, oldValue) => {
                // 使用页面标识符来获取正确的数据
                const materialNumberKey = `parent_material_number_${this.pageType}_${sessionStorage.getItem('windowId')}`;
                this.indata('bom/get_material', { material_number: sessionStorage.getItem(materialNumberKey) })
            }
        )
        this.$store.watch(
            (state) => state.bomRelease_list,
            (newValue, oldValue) => {
                this.list.tableData = this.$store.state.bomRelease_list
            }
        )
        this.$store.watch(
            (state) => state.bomUnpublishedlist,
            (newValue, oldValue) => {
                this.list.tableData = this.$store.state.bomUnpublishedlist
            }
        )
    },
    mounted() {
        // 使用页面标识符来获取正确的数据
        const materialNumberKey = `parent_material_number_${this.pageType}_${sessionStorage.getItem('windowId')}`;
        console.log(sessionStorage.getItem(materialNumberKey),8899966555)
        this.indata('bom/get_material', { material_number: sessionStorage.getItem(materialNumberKey) })
    },
    methods: {
        // 全选复选框
        handleSelectionChange(rows) {
            this.editTableData = rows
            console.log(rows, '选中的数据')

            // 如果没有选中行，重置批量修改状态
            if (this.editTableData.length == 0) {
                this.isShowBatch = false
                // 清除所有行的编辑状态
                if (this.list.tableData && this.list.tableData.length > 0) {
                    this.list.tableData.forEach(item => {
                        item.edit = false;
                    });
                }
            }
        },
        // 批量修改所有数据（不需要选中）
        toggleAllForEdit() {
            if (!this.list.tableData || this.list.tableData.length === 0) {
                return;
            }

            // 将所有行设置为编辑状态
            this.list.tableData.forEach((item) => {
                item.edit = true;
            });

            // 更新编辑数据数组
            this.editTableData = [...this.list.tableData];

            // 显示批量保存和取消按钮
            this.isShowBatch = true;
        },

        // 批量修改选中数据（保留但不使用）
        toggleSelection(params) {
            if (params.length > 0) {
                // 将选中的行设置为编辑状态
                params.forEach((item) => {
                    item.edit = true;
                });

                // 显示批量保存和取消按钮
                this.isShowBatch = true;
            } else {
                ElMessage.warning('请先选择需要批量修改的数据');
            }
        },
        handleInput(row) {
            console.log(parseFloat(row).toFixed(2), 'row')
            this.dosageNum = parseFloat(row).toFixed(2)
        },
        tableRowClassName({ row, rowIndex }) {
            if (row.part_type == 1) {
                return 'greenTable'
            } else {
                return ''
            }
        },
        // input下拉框数据选择回显
        handleSelect(item) {
            console.log(item)
            this.form.material_number = item.number
            // this.form.number = item.number,
            ;(this.form.title = item.title),
                (this.form.type = item.type),
                (this.form.drawing_number = item.drawing_number),
                (this.form.specs = item.specs),
                (this.form.weight = item.weight),
                (this.form.unit_title = item.unit_title),
                (this.form.unit_id = item.unit_id),
                (this.form.texture_id = item.texture_id),
                (this.form.texture_title = item.texture_title),
                (this.form.price = item.price),
                (this.form.quota = item.quota),
                (this.form.part_type_id = item.part_type_id),
                (this.form.part_type_title = item.part_type_title),
                (this.form.dosage = item.dosage),
                (this.form.category_title = item.category_title),
                (this.form.category_id = item.category_id)
        },
        // 编号input下拉输入选择
        querySearch(queryString, cb) {
            this.post_page.keyword = queryString
            // var restaurants = []
            this.$HTTP.post('material/get_all', this.post_page).then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    // console.log(res.result);
                    // restaurants = res.result
                    // var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants
                    // 调用 callback 返回建议列表的数据
                    cb(res.result)
                }
            })
        },
        // 表格添加
        tree_add() {
            this.dialog = true
            this.menuVisible = false
            this.posturl = 'bom/post_save'
            this.form.opt_type = 0
        },
        // 标签数据
        tagsgetData(e) {
            if (e) {
                this.tagsloading = true
                this.$HTTP.post('tag/get_all').then((res) => {
                    if (res.errcode != 0) {
                        this.tagsloading = false
                        ElMessage.error(res.errmsg)
                    } else {
                        this.tagsloading = false
                        this.tagsArray = res.result
                    }
                })
            }
        },
        // 鼠标右击事件
        rowContextmenu(row, column, event) {
            this.$emit('rownotification', false)
            this.$store.state.menuVisibletop = true
            console.log(this.$store.state, this.$store.state.menuVisibletop, 'this.$store.menuVisibletop')
            this.BomObj = row
            this.checkBom = {
                ctype: 'product',
                drawing_number: row.drawing_number,
                id: row.id,
                title: row.title,
                son: row.son,
                type: row.type,
                number: row.number,
                dosage: row.dosage,
                part_type: row.part_type,
                status: row.status,
                specs: row.specs
            }
            this.$store.state.border_bom = [
                {
                    id: row.id,
                    title: row.title,
                    number: row.number
                }
            ]
            this.$store.state.cost_number = row.number
            this.menuVisible = true
            // 节点数据
            this.form = {
                parent_material_number: row.number,
                material_number: '',
                title: '',
                type: '',
                drawing_number: '',
                specs: '',
                weight: '',
                unit_title: '',
                unit_id: '',
                texture_id: '',
                texture_title: '',
                price: '',
                quota: '',
                part_type_id: '',
                part_type_title: '',
                dosage: '',
                category_title: '',
                category_id: ''
            }
            // 将菜单显示在鼠标点击旁边定位
            this.menu_left = event.clientX + 50
            this.menu_top = event.clientY - 0
            document.addEventListener('click', this.foo)
        },
        foo() {
            this.menuVisible = false
            document.removeEventListener('click', this.foo)
        },
        // 反查BOM
        // pegging_click() {
        //     // // this.$router.push('/search')
        //     console.log(this.checkBom, 'this.BomObj')
        //     this.form.parent_number = this.checkBom.number
        //     this.$store.state.listObj = {
        //         isShow: false,
        //         isShowTree: true,
        //         items: this.checkBom,
        //         table: false,
        //         upload: false
        //     }
        //     this.menuVisible = false
        // },
        // 成本
        cost_click() {
            this.$router.push('/cost')
        },
        // 面包屑
        breadClick(op) {
            // console.log(op,'aweqeqweqweqwe');
            this.arraylist.splice(this.arraylist.indexOf(op) + 1)
            this.indata('bom/get_material', { material_number: op.number })
            localStorage.setItem('arraylist', JSON.stringify(this.arraylist))
            localStorage.setItem('parent_material_number', op.number)
            localStorage.setItem('material_id', op.id)
            this.$store.state.listObjTree.ver = !this.$store.state.listObjTree.ver
        },
        titleClick(item, column, event) {
            // localStorage.setItem('parent_material_number', item.number)
            // localStorage.setItem('material_id', item.id)
            // this.$store.state.listObjTree.ver = !this.$store.state.listObjTree.ver
            // this.arraylist.push({
            //     id: item.id,
            //     title: item.title,
            //     number: item.number
            // })
            // // this.$router.push({query:{arrayList:JSON.stringify(this.arraylist)}})

            // localStorage.setItem('arraylist', JSON.stringify(this.arraylist))
            // this.indata('bom/get_material', { material_number: item.number })
        },
        // 修改
        editData(row) {
            row.edit = true
        },
        // 保存
        confirmData(row) {
            console.log(row, 'row>>>>')
            row.edit = false
            // 使用页面标识符来获取正确的数据
            const materialNumberKey = `parent_material_number_${this.pageType}_${sessionStorage.getItem('windowId')}`;
            const postData = {
                parent_material_number: sessionStorage.getItem(materialNumberKey),
                material_number: row.number,
                opt_type: 1
            }
            Object.assign(postData, row)
            postData.dosage = this.dosageNum
            console.log(postData, 'postData')
            // this.$HTTP.post('bom/post_save', postData).then((res) => {
            //     if (res.errcode != 0) {
            //         ElMessage.error(res.errmsg)
            //     } else {
            //         this.indata('bom/get_material', { material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`) })
            //         this.$store.state.listObj.ver = !this.$store.state.listObj.ver
            //         this.$notify({
            //             title: '修改',
            //             message: '编辑成功',
            //             type: 'success',
            //             duration: 2000
            //         })
            //         // console.log(this.remote)
            //     }
            // })
        },
        //批量保存
        confirmDataBatch() {
            // 显示加载状态
            this.pictLoading = true;

            // 使用页面标识符来获取正确的数据
            const materialNumberKey = `parent_material_number_${this.pageType}_${sessionStorage.getItem('windowId')}`;
            const materialNumber = sessionStorage.getItem(materialNumberKey);

            if (!materialNumber) {
                ElMessage.error('无法获取物料编号，请刷新页面后重试');
                this.pictLoading = false;
                return;
            }

            // 处理用量数据，保留两位小数
            this.list.tableData.forEach(item => {
                if (item.dosage) {
                    try {
                        item.dosage = parseFloat(item.dosage).toFixed(2);
                    } catch (e) {
                        console.error('处理用量数据出错:', e);
                    }
                }
            });

            // 构建请求数据
            const requestData = {
                boms: this.list.tableData,
                material_number: materialNumber
            };

            console.log('批量保存数据:', requestData);

            // 发送请求
            this.$HTTP.post('bom/post_save_all', requestData)
                .then((res) => {
                    this.pictLoading = false;

                    if (res.errcode === 0) {
                        // 重置编辑状态
                        this.list.tableData.forEach(item => {
                            item.edit = false;
                        });

                        // 重置批量修改状态
                        this.isShowBatch = false;

                        // 清除选中状态
                        if (this.$refs.multipleTableRef) {
                            this.$refs.multipleTableRef.clearSelection();
                        }

                        // 刷新数据
                        this.indata('bom/get_material', { material_number: materialNumber });

                        // 更新状态
                        this.$store.state.listObjTree.ver = !this.$store.state.listObjTree.ver;
                    } else if (res.errcode === 14025) {
                        ElMessage.warning(res.errmsg);
                    } else {
                        ElMessage.error(res.errmsg || '保存失败，请重试');
                    }
                })
                .catch(error => {
                    this.pictLoading = false;
                    console.error('批量保存出错:', error);
                    ElMessage.error('保存失败，请检查网络连接后重试');
                });
        },

        // 删除
        handleDelete(row, index) {
            // 使用页面标识符来获取正确的数据
            const materialNumberKey = `parent_material_number_${this.pageType}_${sessionStorage.getItem('windowId')}`;
            const postData = {
                parent_material_number: sessionStorage.getItem(materialNumberKey),
                material_number: row.row.number,
                opt_type: 2
            }
            this.$HTTP.post('bom/post_save', postData).then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    this.indata('bom/get_material', { material_number: sessionStorage.getItem(materialNumberKey) })
                    this.$store.state.listObjTree.ver = !this.$store.state.listObjTree.ver
                }
            })
            // this.list.tableData.splice(index, 1);
        },

        indata(url, page) {
            console.log(url, 'url')
            console.log(page, 'page')
            this.pictLoading = true
            const postData = {
                page: this.currentPage,
                per_page: this.pagesize
            }

            // 确保page中的material_number参数有值
            if (!page || !page.material_number) {
                console.error('Missing material_number in page parameter');

                // 尝试从sessionStorage获取
                const materialNumberKey = this.getMaterialNumberKey();
                if (sessionStorage.getItem(materialNumberKey)) {
                    if (!page) {
                        page = {};
                    }
                    page.material_number = sessionStorage.getItem(materialNumberKey);
                } else if (this.$store.state.listObjTree && this.$store.state.listObjTree.items && this.$store.state.listObjTree.items.number) {
                    // 如果sessionStorage中没有，尝试从store中获取
                    if (!page) {
                        page = {};
                    }
                    page.material_number = this.$store.state.listObjTree.items.number;

                    // 设置到sessionStorage中
                    sessionStorage.setItem(materialNumberKey, this.$store.state.listObjTree.items.number);
                    if (this.$store.state.listObjTree.items.id) {
                        sessionStorage.setItem(`material_id_${this.pageType}_${sessionStorage.getItem('windowId')}`, this.$store.state.listObjTree.items.id);
                    }
                } else {
                    ElMessage.error('缺少物料编号，无法获取数据');
                    this.pictLoading = false;
                    return;
                }
            }

            Object.assign(postData, page)

            // 使用页面标识符来获取正确的数据
            const pageData = {
                page: this.currentPage,
                per_page: this.pagesize,
                material_number: postData.material_number
            }

            // 确保material_number参数有值
            if (!pageData.material_number) {
                console.error('Missing material_number in bom_log/get_ls call');
                ElMessage.error('缺少物料编号，无法获取BOM版本信息');
                this.pictLoading = false;
                return;
            }

            this.$HTTP.post('bom_log/get_ls', pageData).then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    console.log(res.result, 'result')
                    if (res.result) {
                        let arrayStr = res.result.data
                        console.log(arrayStr, 'arrayStr')
                        let strArray = res.result.data[0]
                        let array = JSON.parse(localStorage.getItem('USER_INFO'))
                        if (strArray != undefined) {
                            if (strArray.userid == array.content.userid) {
                                // 使用 find 方法查找 published = 0 的对象
                                const targetObject = arrayStr.find((item) => item.published === 0)

                                if (targetObject) {
                                    console.log('找到 published = 0 的对象：', targetObject)
                                    this.$HTTP.get('bom_log/get_detail', { id: targetObject.id }).then((res) => {
                                        //未发布
                                        if (res.errcode != 0) {
                                            ElMessage.error(res.errmsg)
                                        } else {
                                            this.list.tableData = res.result
                                            this.list.tableData.map((item) => {
                                                item.edit = false
                                            })
                                            this.pictLoading = false
                                            // this.list.tableData = this.$store.state.Unpublishedlist
                                            console.log(res, '222222222222222')
                                        }
                                    })
                                } else {
                                    this.$HTTP.get(url, postData).then((res) => {
                                        if (res.errcode != 0) {
                                            ElMessage.error(res.errmsg)
                                        } else {
                                            // this.$store.state.listObj.table = false
                                            // this.$store.state.listObj.upload = false
                                            this.list.tableData = res.result
                                            this.list.tableData.map((item) => {
                                                item.edit = false
                                            })
                                            this.pictLoading = false
                                            // if (res.result) {

                                            // }
                                        }
                                    })
                                    console.log('未找到 published = 0 的对象')
                                }
                            } else {
                                this.$HTTP.get(url, postData).then((res) => {
                                    if (res.errcode != 0) {
                                        ElMessage.error(res.errmsg)
                                    } else {
                                        // this.$store.state.listObj.table = false
                                        // this.$store.state.listObj.upload = false
                                        this.list.tableData = res.result
                                        this.list.tableData.map((item) => {
                                            item.edit = false
                                        })
                                        this.pictLoading = false
                                        // if (res.result) {

                                        // }
                                    }
                                })
                            }
                        } else {
                            this.$HTTP.get(url, postData).then((res) => {
                                if (res.errcode != 0) {
                                    ElMessage.error(res.errmsg)
                                } else {
                                    // this.$store.state.listObj.table = false
                                    // this.$store.state.listObj.upload = false
                                    this.list.tableData = res.result
                                    this.pictLoading = false
                                    // if (res.result) {

                                    // }
                                }
                            })
                        }
                    }
                    // this.$store.state.bomUnpublishedlist=res.result
                    // this.list.tableData = res.result.data
                }
            })
        },
        // 获取当前页面的materialNumberKey
        getMaterialNumberKey() {
            return `parent_material_number_${this.pageType}_${sessionStorage.getItem('windowId')}`;
        },

        //取消批量修改
        cancellation() {
            // 重置批量修改状态
            this.isShowBatch = false;

            // 清除选中状态
            if (this.$refs.multipleTableRef) {
                this.$refs.multipleTableRef.clearSelection();
            }

            // 使用页面标识符来获取正确的数据
            const materialNumberKey = this.getMaterialNumberKey();
            const materialNumber = sessionStorage.getItem(materialNumberKey);

            if (materialNumber) {
                // 重新加载数据，恢复原始状态
                this.indata('bom/get_material', { material_number: materialNumber });
                this.$store.state.listObjTree.ver = !this.$store.state.listObjTree.ver;
            }
        },
        //弹出面板
        handleClose(done) {
            this.dialog = false
            this.loading = false
            // if (this.loading) {
            //     return;
            // }
            // this.$confirm('确定要提交表单吗？')
            //     .then(_ => {
            //     this.loading = true;
            //     this.$HTTP.post(this.posturl, this.form).then((res) => {
            //         if (res.errcode != 0) {
            //             this.loading = false
            //             ElMessage.error(res.errmsg)
            //         } else {
            //             done();
            //             this.dialog = false
            //             this.loading = false
            //             this.$notify({
            //                 title: '添加',
            //                 message: '操作成功 ',
            //                 type: 'success',
            //                 duration: 2000
            //             })
            //         }
            //     })
            //     })
        },
        cancelForm() {
            this.loading = false
            this.dialog = false
            clearTimeout(this.timer)
        },
        // 抽屉提交
        onclickAdd(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.tagstitle = this.form.tags
                    if (this.form.tags) {
                        var postarr = []
                        this.$HTTP
                            .post('tag/get_all')
                            .then((res) => {
                                if (res.errcode != 0) {
                                    ElMessage.error(res.errmsg)
                                } else {
                                    postarr = res.result
                                    const mergedArray = this.form.tags.map((tag) => {
                                        const existingPost = postarr.find((post) => post.title === tag)
                                        if (existingPost) {
                                            return { title: existingPost.title, id: existingPost.id }
                                        } else {
                                            return { title: tag, id: '' }
                                        }
                                    })
                                    this.form.tags = JSON.stringify(mergedArray)
                                }
                                this.loading = true
                                // 调取接口
                                this.$HTTP.post(this.posturl, this.form).then((res) => {
                                    if (res.errcode != 0) {
                                        this.loading = false
                                        this.form.tags = this.tagstitle
                                        ElMessage.error(res.errmsg)
                                    } else {
                                        this.dialog = false
                                        this.loading = false
                                        this.$store.state.listObjTree.ver = !this.$store.state.listObjTree.ver
                                        this.$notify({
                                            title: '添加',
                                            message: '操作成功 ',
                                            type: 'success',
                                            duration: 2000
                                        })
                                    }
                                })
                            })
                            .catch(() => {
                                this.loading = false
                                this.form.tags = this.tagstitle
                            })
                    } else {
                        this.loading = true
                        // 调取接口
                        this.$HTTP
                            .post(this.posturl, this.form)
                            .then((res) => {
                                if (res.errcode != 0) {
                                    this.loading = false
                                    this.form.tags = this.tagstitle
                                    ElMessage.error(res.errmsg)
                                } else {
                                    this.dialog = false
                                    this.loading = false
                                    this.$store.state.listObjTree.ver = !this.$store.state.listObjTree.ver
                                    this.$notify({
                                        title: '添加',
                                        message: '操作成功 ',
                                        type: 'success',
                                        duration: 2000
                                    })
                                }
                            })
                            .catch(() => {
                                this.loading = false
                                this.form.tags = this.tagstitle
                            })
                    }
                } else {
                    // this.dialog = false
                    // this.form.tags = ''
                    this.loading = false
                    ElMessage.error('带*为必填')
                    return false
                }
            })
        },
        // onclickAdd(formName){
        //     this.tagstitle = this.form.tags
        //     if(this.form.tags){
        //         var postarr = []
        //         this.$HTTP
        //             .post('tag/get_all').then((res)=>{
        //                 if (res.errcode != 0) {
        //                     ElMessage.error(res.errmsg)
        //                 } else {
        //                     postarr = res.result
        //                     const mergedArray = this.form.tags.map(tag => {
        //                     const existingPost = postarr.find(post => post.title === tag);
        //                     if (existingPost) {
        //                         return {title:existingPost.title,id:existingPost.id};
        //                     } else {
        //                         return { title: tag, id: '' };
        //                     }
        //                     });
        //                     this.form.tags = JSON.stringify(mergedArray)
        //                 }
        //                 this.matpost_data(formName)
        //         })
        //     }else{
        //         this.matpost_data(formName)
        //     }
        // },

        // 材质数据
        getData() {
            this.$HTTP.post('texture/get_all').then((res) => {
                this.texture = res.result
            })
        },
        selectget(e) {
            let dir = this.texture.find((item) => item.title === e)
            this.form.texture_id = dir.id
        },
        // 单位数据
        unit_getData() {
            this.$HTTP.post('unit/get_all').then((res) => {
                this.unitTitle = res.result
            })
        },
        selectUnit(e) {
            let dir = this.unitTitle.find((item) => item.title === e)
            this.form.unit_id = dir.id
        },
        // 零件类型
        partType_title() {
            this.$HTTP.post('part_type/get_all').then((res) => {
                this.partTypeArray = res.result
            })
        },
        partType(e) {
            let dir = this.partTypeArray.find((item) => item.title === e)
            this.form.part_type_id = dir.id
        },
        // 分类数据
        categorydata() {
            this.$HTTP.post('category/get_all').then((res) => {
                this.categoryArray = res.result
            })
        },
        selectCategory(e) {
            let dir = this.categoryArray.find((item) => item.title === e)
            this.form.category_id = dir.id
        }
    }
}
</script>

<style scoped lang="scss">
.table-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    /* 其他样式 */
}
.teble-content {
    width: 100%;
    height: 95%;
}
.pagin {
    position: absolute;
    bottom: 20px;
    min-width: 200px;
}
.box-menu {
    // width: 100px;
    position: absolute;
    z-index: 1000;
    background-color: #fff;
    box-shadow: 0px 0px 10px #ccc, 0px 0px 20px #ccc, 0px 0px 30px #ccc;
    padding: 10px;

    div {
        cursor: pointer;
        line-height: 30px;
    }
}

.demo-drawer__footer {
    position: fixed;
    bottom: 10px;
    right: 10px;
}
.demo-form-inline {
    padding: 0 20px;
}
.formType {
    margin-bottom: 50px;
    .title {
        font-size: 17px;
        font-weight: 550;
        padding: 20px;
        border-bottom: 2px solid #e9e9e9;
        margin: 0 0 10px 0;
    }
}
::v-deep .el-table .greenTable {
    color: #67c23a;
    // background-color:red
}
</style>
