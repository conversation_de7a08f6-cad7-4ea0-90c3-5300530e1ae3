<!--
 * @Descripttion: scDialog 弹窗扩展演示文件
 * @version: 1.0
 * @Author: sakuya
 * @Date: 2021年8月27日08:56:30
 * @LastEditors: qq <EMAIL>
 * @LastEditTime: 2025-02-25 17:02:31
-->

<template>
    <el-container>
        <el-menu default-active="2" class="el-menu-vertical-demo" :collapse="isCollapse" @open="handleOpen"
            @close="handleClose" :style="{ width: widthmeu + 'px' }">
            <div v-if="isCollapse">
                <el-main v-loading="pictLoading">
                    <div class="card-box">
                        <div class="left-panel">
                            <span @click="versionR()" :class="versionRi ? 'lanse' : ''">版本记录</span>
                        </div>
                        <!-- v-if="urlAress=='bom_log/get_ls'" -->
                        <div v-if="urlAress == 'bom_log/get_ls'">当前版本Version &nbsp;{{ verValue }}</div>
                        <div v-else>当前版本Version &nbsp;{{ craftValue }}</div>
                        <div v-if="versionlist.length > 0">
                            <div v-for="item in versionlist" :key="item">
                                <!-- style="display: flex; justify-content: space-between" -->
                                <div style="margin: 10px 0; display: flex; justify-content: space-between">
                                    Version {{ item.ver }} <span v-if="item.published == 2">{{ item.updated_at
                                        }}</span><span v-else>{{ item.created_at }}</span>
                                </div>
                                <div style="cursor: pointer">
                                    <el-tag class="ml-2" type="success" v-if="item.published == 2">已发布</el-tag>
                                    <el-tag class="ml-2" type="warning" v-if="item.published == 1">审核中</el-tag>
                                    <el-tag class="ml-2" type="danger" v-if="item.published == 0"
                                        @click="releaseAdd(item)">未发布</el-tag>

                                    <el-tag @click="open2(item)" style="margin-right: 10px">历史对比</el-tag>
                                    <span>{{ item.user_name }}</span>

                                    <el-dropdown trigger="click" v-if="item.published == 0" style="padding-top: 10px">
                                        <el-tag type="info" effect="plain">
                                            切换
                                            <el-icon>
                                                <!-- 零件 -->
                                                <el-icon-arrow-down />
                                            </el-icon>
                                        </el-tag>
                                        <template #dropdown>
                                            <el-dropdown-item>
                                                <el-button type="primary" @click="addClick(item)">发布数据</el-button>
                                            </el-dropdown-item>
                                            <el-dropdown-item>
                                                <el-button type="primary" @click="Unpublished(item)">未发布数据</el-button>
                                            </el-dropdown-item>
                                        </template>
                                    </el-dropdown>
                                    <el-tag class="ml-2" type="danger" style="margin-top: 10px;margin-left: 10px;"
                                        v-if="item.published == 0" @click="delVersion(item)">删除</el-tag>
                                </div>
                            </div>
                        </div>
                        <div v-else>
                            <el-empty :description="descriptionTitle" :image-size="100"></el-empty>
                        </div>
                    </div>
                </el-main>
                <sc-dialog v-model="dialog2" :title="switchisShow ? 'BOM版本对比' : '工艺卡版本对比'" :loading="dialog2Loading">
                    <el-empty description="暂无版本对比信息" :image-size="80" v-if="versiondata"></el-empty>
                    <el-descriptions direction="vertical" :column="4" :size="size" v-if="switchisShow" border>
                        <el-descriptions-item label="对比BOM信息">
                            <div v-for="item in BomObj.contrast_bom" :key="item.id" style="margin-bottom: 10px">
                                <div v-if="item.contrast_bom">
                                    <div>标题：{{ item.title }}</div>
                                    <div>编号：{{ item.number }}</div>
                                    <div :class="`${item.contrast_bom.dosage != parseFloat(item.dosage).toFixed(2)
                                        ? 'newfontcolor'
                                        : ''
                                        }`">
                                        用量：{{ parseFloat(item.dosage).toFixed(2) }}
                                    </div>
                                    <div>时间：{{ item.created_at }}</div>
                                </div>
                                <div v-else class="addfontcolor">
                                    <div>标题：{{ item.title }}</div>
                                    <div>编号：{{ item.number }}</div>
                                    <div>用量：{{ parseFloat(item.dosage).toFixed(2) }}</div>
                                    <div>时间：{{ item.created_at }}</div>
                                </div>
                            </div>
                        </el-descriptions-item>
                        <el-descriptions-item label="备注信息">
                            {{ BomObj.contrast_desc }}
                        </el-descriptions-item>
                        <el-descriptions-item label="当前BOM信息">
                            <div v-for="item in BomObj.bom" :key="item.id" style="margin-bottom: 10px">
                                <div v-if="item.bom">
                                    <div>标题：{{ item.title }}</div>
                                    <div>编号：{{ item.number }}</div>
                                    <div :class="`${item.bom.dosage != parseFloat(item.dosage).toFixed(2) ? 'newfontcolor' : ''
                                        }`">
                                        用量：{{ parseFloat(item.dosage).toFixed(2) }}
                                    </div>
                                    <div>时间：{{ item.created_at }}</div>
                                </div>
                                <div v-else class="addfontcolor">
                                    <div>标题：{{ item.title }}</div>
                                    <div>编号：{{ item.number }}</div>
                                    <div>用量：{{ parseFloat(item.dosage).toFixed(2) }}</div>
                                    <div>时间：{{ item.created_at }}</div>
                                </div>
                            </div>
                        </el-descriptions-item>
                        <el-descriptions-item label="备注信息">
                            {{ BomObj.current_desc }}
                        </el-descriptions-item>
                    </el-descriptions>
                    <el-descriptions direction="vertical" :column="4" :size="size" v-else border>
                        <el-descriptions-item label="对比工序信息">
                            <div v-for="item in BomObj.contrast_crafts" :key="item.id" style="margin-bottom: 10px">

                                <div v-if="item.new_crafts">
                                    <div :class="`${item.new_crafts.process_id != item.process_id ? 'newfontcolor' : ''
                                        }`">
                                        工序ID：{{ item.process_id }}
                                    </div>
                                    <div :class="`${item.new_crafts.process_title != item.process_title ? 'newfontcolor' : ''
                                        }`">
                                        工序：{{ item.process_title }}
                                    </div>
                                    <div :class="`${item.new_crafts.process_content_id != item.process_content_id
                                        ? 'newfontcolor'
                                        : ''
                                        }`">
                                        工序内容id：{{ item.process_content_id }}
                                    </div>
                                    <div :class="`${item.new_crafts.process_content != item.process_content
                                        ? 'newfontcolor'
                                        : ''
                                        }`">
                                        工序内容：{{ item.process_content }}
                                    </div>
                                    <div :class="`${item.new_crafts.tool_id != item.tool_id ? 'newfontcolor' : ''}`">
                                        机床号id：{{ item.tool_id }}
                                    </div>
                                    <div
                                        :class="`${item.new_crafts.tool_code != item.tool_code ? 'newfontcolor' : ''}`">
                                        机床号：{{ item.tool_code }}
                                    </div>
                                    <div :class="`${item.new_crafts.unit_hour != parseFloat(item.unit_hour).toFixed(2)
                                        ? 'newfontcolor'
                                        : ''
                                        }`">
                                        单位工时：{{ parseFloat(item.unit_hour).toFixed(2) }}
                                    </div>
                                    <div
                                        :class="`${item.new_crafts.desc && typeof item.new_crafts.desc != undefined && item.new_crafts.desc != item.desc ? 'newfontcolor' : ''}`">
                                        备注：{{ item.desc }}
                                    </div>
                                    <div
                                        :class="`${item.new_crafts.sort && typeof item.new_crafts.sort != undefined && item.new_crafts.sort != item.sort ? 'newfontcolor' : ''}`">
                                        序号：{{ item.sort }}
                                    </div>
                                </div>
                                <div v-else class="addfontcolor">
                                    <div>工序ID：{{ item.process_id }}</div>
                                    <div>工序：{{ item.process_title }}</div>
                                    <div>工序内容id：{{ item.process_content_id }}</div>
                                    <div>工序内容：{{ item.process_content }}</div>
                                    <div>机床号id：{{ item.tool_id }}</div>
                                    <div>机床号：{{ item.tool_code }}</div>
                                    <div>单位工时：{{ parseFloat(item.unit_hour).toFixed(2) }}</div>
                                    <div>备注：{{ item.desc }}</div>
                                    <div>序号：{{ item.sort }}</div>
                                </div>
                            </div>
                        </el-descriptions-item>
                        <el-descriptions-item label="备注信息">
                            {{ BomObj.contrast_desc }}
                        </el-descriptions-item>
                        <el-descriptions-item label="当前工序信息">
                            <div v-for="item in BomObj.crafts" :key="item.id" style="margin-bottom: 10px">
                                <!-- {{ item.contrast_crafts }} -->
                                <div v-if="item.contrast_crafts">
                                    <div :class="`${item.contrast_crafts.process_id != item.process_id ? 'editfontcolor' : ''
                                        }`">
                                        工序ID：{{ item.process_id }}
                                    </div>
                                    <div :class="`${item.contrast_crafts.process_title != item.process_title
                                        ? 'editfontcolor'
                                        : ''
                                        }`">
                                        工序：{{ item.process_title }}
                                    </div>
                                    <div :class="`${item.contrast_crafts.process_content_id != item.process_content_id
                                        ? 'editfontcolor'
                                        : ''
                                        }`">
                                        工序内容id：{{ item.process_content_id }}
                                    </div>
                                    <div :class="`${item.contrast_crafts.process_content != item.process_content
                                        ? 'editfontcolor'
                                        : ''
                                        }`">
                                        工序内容：{{ item.process_content }}
                                    </div>
                                    <div :class="`${item.contrast_crafts.tool_id != item.tool_id ? 'editfontcolor' : ''
                                        }`">
                                        机床号id：{{ item.tool_id }}
                                    </div>
                                    <div :class="`${item.contrast_crafts.tool_code != item.tool_code ? 'editfontcolor' : ''
                                        }`">
                                        机床号：{{ item.tool_code }}
                                    </div>
                                    <div :class="`${item.contrast_crafts.unit_hour != parseFloat(item.unit_hour).toFixed(2)
                                        ? 'editfontcolor'
                                        : ''
                                        }`">
                                        单位工时：{{ parseFloat(item.unit_hour).toFixed(2) }}
                                    </div>
                                    <!-- {{ typeof item.contrast_crafts.desc }} {{ typeof item.desc }} -->
                                    <div
                                        :class="`${item.contrast_crafts.desc && typeof item.contrast_crafts.desc != undefined && item.contrast_crafts.desc != item.desc ? 'editfontcolor' : ''}`">
                                        备注：{{ item.desc }}
                                    </div>
                                    <div
                                        :class="`${item.contrast_crafts.sort && typeof item.contrast_crafts.sort != undefined && item.contrast_crafts.sort != item.sort ? 'newfontcolor' : ''}`">
                                        序号：{{ item.sort }}
                                    </div>
                                </div>
                                <div v-else>
                                    <div>工序ID：{{ item.process_id }}</div>
                                    <div>工序：{{ item.process_title }}</div>
                                    <div>工序内容id：{{ item.process_content_id }}</div>
                                    <div>工序内容：{{ item.process_content }}</div>
                                    <div>机床号id：{{ item.tool_id }}</div>
                                    <div>机床号：{{ item.tool_code }}</div>
                                    <div>单位工时：{{ parseFloat(item.unit_hour).toFixed(2) }}</div>
                                    <div>备注：{{ item.desc }}</div>
                                    <div>序号：{{ item.sort }}</div>
                                </div>
                            </div>
                        </el-descriptions-item>
                        <el-descriptions-item label="备注信息">
                            {{ BomObj.current_desc }}
                        </el-descriptions-item>
                    </el-descriptions>
                    <div style="float: right">
                        <el-button type="primary" class="huibutton" @click="RollBACK()">回滚</el-button>
                    </div>
                </sc-dialog>
                <el-pagination class="pagin" @size-change="handleSizeChange" @current-change="handleCurrentChange"
                    :current-page="currentPage" :page-size="pagesize" small layout="prev, pager, next" :total="total">
                </el-pagination>
            </div>
        </el-menu>
        <el-footer>
            <el-radio-group v-model="isCollapse">
                <!-- <el-radio-button @click="edsss()">expand</el-radio-button> -->
                <el-icon :size="25" @click="edsss()">
                    <el-icon-Expand />
                </el-icon>
                <!-- <el-button @click="edsss()">collapse</el-button> -->
            </el-radio-group>
        </el-footer>
    </el-container>
    <el-dialog v-model="dialogVisible" title="发布原因" width="30%" :before-close="handleClose">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
            <el-form-item label="备注：" prop="publishDesc">
                <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" placeholder="请输入发布原因"
                    v-model="ruleForm.publishDesc"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" :loading="submitLoading" @click="submitForm('ruleForm')">
                    <template #icon>
                        <el-icon v-if="!submitLoading">
                            <Check />
                        </el-icon>
                    </template>
                    {{ submitLoading ? '提交中...' : '提交' }}
                </el-button>
                <el-button @click="dialogVisible = false">关闭</el-button>
            </el-form-item>
        </el-form>
    </el-dialog>
</template>

<script>
import { mapState } from 'vuex'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { Check } from '@element-plus/icons-vue'
export default {
    data() {
        return {
            verValue: '',
            craftValue: '',
            urlAress: '',
            isCollapse: true,
            pictLoading: false,
            descriptionTitle: '暂无数据',
            currentPage: 1, //初始页
            pagesize: 10, //    每页的数据
            operateRi: false, //操作记录
            versiondata: false,
            dialog2: false,
            dialog2Loading: false,
            versionRi: true, //版本记录
            switchisShow: true, //对比切换
            total: 12,
            versionlist: [],
            editlist: [],
            BomObj: [],
            widthmeu: '220',
            RollBACK_post: {}, //回滚
            url: 'bom_log/get_ls',
            dialogVisible: false,
            option: {},
            ruleForm: {
                publishDesc: ''
            },
            rules: {
                publishDesc: [{ required: true, message: '请填写发布原因', trigger: 'blur' }]
            },
            submitLoading: false,  // 添加提交加载状态
        }
    },
    mounted() { },
    computed: {
        ...mapState(['listObj'])
    },
    created() {
        if (sessionStorage.getItem('ver') == 'null') {
            this.verValue = '无'
        } else {
            this.verValue = sessionStorage.getItem('ver')
        }
        if (sessionStorage.getItem('craft_small_ver') == 'null') {
            this.craftValue = '无'
        } else {
            this.craftValue = sessionStorage.getItem('craft_small_ver')
        }
        const postData = {
            page: this.currentPage,
            per_page: this.pagesize
        }
        Object.assign(postData, { material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`) })
        this.$store.watch(
            (state) => state.listObj.varApi,
            (newValue) => {
                if (sessionStorage.getItem('ver') == 'null') {
                    this.verValue = '无'
                } else {
                    this.verValue = sessionStorage.getItem('ver')
                }
                if (sessionStorage.getItem('craft_small_ver') == 'null') {
                    this.craftValue = '无'
                } else {
                    this.craftValue = sessionStorage.getItem('craft_small_ver')
                }
                Object.assign(postData, { material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`) })
                if (this.$store.state.listObj.varApi) {
                    if (this.$store.state.listObj.varApi.get_ls) {
                        let op = { material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`) }
                        this.versionNumber(op)

                        this.url = this.$store.state.listObj.varApi.get_ls
                        // this.post_Data('bom_log/get_ls', postData)//操作记录
                        this.Vspost(this.$store.state.listObj.varApi.get_ls, postData) //版本记录
                    }
                }

                // console.log(this.$store.state.listObj.varApi.get_ls,'asdasdasdasdasdq1qsaghdefrsjhdhasfdasjdhasfasdhaksd');
            }
        )
        this.$store.watch(
            (state) => state.listObj.ver,
            (newValue) => {
                if (sessionStorage.getItem('ver') == 'null') {
                    this.verValue = '无'
                } else {
                    this.verValue = sessionStorage.getItem('ver')
                }
                if (sessionStorage.getItem('craft_small_ver') == 'null') {
                    this.craftValue = '无'
                } else {
                    this.craftValue = sessionStorage.getItem('craft_small_ver')
                }
                Object.assign(postData, { material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`) })
                if (this.$store.state.listObj.varApi) {
                    if (this.$store.state.listObj.varApi.get_ls) {
                        let op = { material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`) }
                        this.versionNumber(op)

                        this.url = this.$store.state.listObj.varApi.get_ls
                        // this.post_Data('bom_log/get_ls', postData)//操作记录
                        this.Vspost(this.$store.state.listObj.varApi.get_ls, postData) //版本记录
                    }
                }
            }
        )
        // this.post_Data('bom_log/get_ls', postData)//操作记录
        // console.log();
        // alert(this.$store.state.curIndex)
        // console.log(this.$store.state.curIndex);
        // alert(this.$store.state.curIndex)
        if (this.$store.state.curIndex == 1 || this.$store.state.curIndex == 0) {
            let op = { material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`) }
            this.versionNumber(op)
            this.Vspost('bom_log/get_ls', postData) //版本记录
        } else {
            let op = { material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`) }
            this.versionNumber(op)
            this.Vspost('craft_log/get_ls', postData) //版本记录
        }
    },
    methods: {
        // 切换发布数据
        addClick(op) {
            if (this.url == 'bom_log/get_ls') {
                this.$HTTP.get('bom/get_material', { material_number: op.material_number }).then((res) => {
                    if (res.errcode != 0) {
                        ElMessage.error(res.errmsg)
                    } else {
                        // bom发布数据
                        this.$store.state.bomRelease_list = res.result
                        this.versionNumber(op)
                        // this.verValue= op.ver
                    }
                })
            } else {
                this.$HTTP.get('craft/get_detail', { material_number: op.material_number }).then((res) => {
                    if (res.errcode != 0) {
                        ElMessage.error(res.errmsg)
                    } else {
                        // 工艺卡发布
                        this.$store.state.release_stlist = res.result
                        this.versionNumber(op)
                        // this.craftValue=op.craft_small_ver
                    }
                })
            }
        },
        // 切换未发布数据
        Unpublished(op) {
            if (this.url == 'bom_log/get_ls') {
                this.$HTTP.get('bom_log/get_detail', { id: op.id }).then((res) => {
                    if (res.errcode != 0) {
                        ElMessage.error(res.errmsg)
                    } else {
                        this.$store.state.bomUnpublishedlist = res.result
                        this.$store.state.bomUnpublishedlist.map((item) => {
                            item.edit = false
                        })
                        this.verValue = op.ver
                        // this.craftValue=op.craft_small_ver
                    }
                })
            } else {
                console.log(op, '>>>>>>>>>>>>>>>>>>>>>>>>>>');

                this.craftValue = op.ver
                this.$HTTP.get('craft_log/get_detail', { id: op.id }).then((res) => {
                    if (res.errcode != 0) {
                        ElMessage.error(res.errmsg)
                    } else {
                        this.$store.state.Unpublishedlist = res.result
                        this.$store.state.Unpublishedlist.map((item) => {
                            item.edit = false
                        })
                    }
                })
            }
        },
        edsss() {
            if (this.isCollapse) {
                this.isCollapse = false
                this.widthmeu = 0
            } else {
                this.isCollapse = true
                this.widthmeu = 300
            }
        },
        // 发布按钮
        releaseAdd(op) {
            this.ruleForm.publishDesc = ''
            this.dialogVisible = true
            this.option = op
        },
        submitForm(formName) {
            if (this.submitLoading) return;  // 如果正在提交，直接返回

            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.submitLoading = true;  // 开始提交，显示加载状态

                    var appurl = ''
                    var appData
                    if (this.url == 'bom_log/get_ls') {
                        appurl = 'bom/post_publish'
                        appData = {
                            number: this.option.material_number,
                            desc: this.ruleForm.publishDesc
                        }
                    } else {
                        appurl = 'craft/post_publish'
                        appData = {
                            material_number: this.option.material_number,
                            desc: this.ruleForm.publishDesc
                        }
                    }
                    var postData = {
                        page: this.currentPage,
                        per_page: this.pagesize,
                        material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`)
                    }
                    this.$HTTP.post(appurl, appData).then((res) => {
                        if (res.errcode != 0) {
                            this.submitLoading = false
                            ElMessage.error(res.errmsg)
                        } else {
                            this.dialogVisible = false
                            this.$notify({
                                title: '发布',
                                message: '操作成功 ',
                                type: 'success',
                                duration: 2000
                            })
                            // 随后补充页面刷新
                            this.$emit('versinQuery', false)
                            this.$store.state.technology = !this.$store.state.technology
                            this.Vspost(this.url, postData) //版本记录
                            this.versionNumber(postData)
                            this.submitLoading = false
                        }
                    }).catch(() => {
                        this.submitLoading = false
                    })
                } else {
                    ElMessage.error('带*为必填')
                    return false
                }
            })
        },
        post_Data(url, postData) {
            this.$HTTP.post(url, postData).then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    this.editlist = res.result.data
                    this.total = res.result.total
                }
            })
        },
        Vspost(url, postData) {
            console.log(url, 'urlurlurl')
            this.urlAress = url
            this.pictLoading = true
            this.$HTTP.post(url, postData).then((res) => {
                this.pictLoading = false
                if (res.errcode != 0 && res.errcode != 510) {
                    ElMessage.error(res.errmsg)
                } else if (res.errcode == 510) {
                    this.versionlist = []
                    this.descriptionTitle = '暂无权限'
                    this.craftValue = '无'
                } else {
                    this.versionlist = res.result.data
                    console.log(this.versionlist, 'dajsdjasjd')
                    this.total = res.result.total
                }
            })
        },
        versionNumber(op) {
            this.$HTTP.post('material/get_info', { number: op.material_number }).then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    if (this.urlAress == 'bom_log/get_ls') {
                        this.verValue = res.result.ver
                    } else {
                        this.craftValue = res.result.craft_small_ver
                    }
                    console.log(res, 'res?>>>>>>')
                }
            })
        },
        open2(op) {
            // console.log(op.id);
            this.RollBACK_post = op
            this.switchisShow = true
            this.dialog2 = true
            this.dialog2Loading = true
            const postDate = {
                material_number: op.material_number,
                contrast_id: op.id
            }

            if (this.$store.state.listObj.varApi) {
                if (this.$store.state.listObj.varApi.get_contrast == 'bom_log/get_contrast') {
                    this.$HTTP.post('bom_log/get_contrast', postDate).then((res) => {
                        let array = res.result
                        if (array) {
                            if (array.contrast_bom && array.bom) {
                                array.contrast_bom.forEach((item) => {
                                    console.log(item, 'item1111111111111111')
                                    array.bom.forEach((k) => {
                                        if (item.number == k.number) {
                                            item.contrast_bom = k
                                            let { contrast_bom, ...newUser } = item
                                            k.bom = newUser
                                            // console.log(k,item,'res.result1');
                                        }
                                    })
                                })
                            }
                        }
                        this.BomObj = array
                        // this.BomObj = res.result
                        this.dialog2Loading = false
                    })
                } else {
                    this.switchisShow = false
                    this.$HTTP.post('craft_log/get_contrast', postDate).then((res) => {
                        let array = res.result
                        if (array) {
                            if (array.crafts && array.contrast_crafts) {
                                array.crafts.forEach((item) => {
                                    array.contrast_crafts.forEach((k) => {
                                        if (item.process_id == k.process_id) {
                                            item.contrast_crafts = k
                                            let { contrast_crafts, ...newUser } = item
                                            k.new_crafts = newUser
                                            // console.log(k,item,'res.result1');
                                        }
                                    })
                                })
                            }
                        }
                        this.BomObj = array
                        this.dialog2Loading = false
                    })
                }
            } else {
                this.$HTTP.post('bom_log/get_contrast', postDate).then((res) => {
                    // console.log(res.result,'res.result2');
                    this.BomObj = res.result
                    this.dialog2Loading = false
                })
            }
        },
        // 回滚
        RollBACK() {
            // this.dialog2Loading = false
            //                         this.dialog2 = false
            //                         // this.addClick(this.RollBACK_post)

            //                         this.$store.state.technology = !this.$store.state.technology
            // this.Vspost(this.$store.state.listObj.varApi.get_ls, postData) //版本记录
            this.$confirm('此操作将回滚BOM, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    if (this.$store.state.curIndex == 2) {
                        this.dialog2Loading = false
                        this.dialog2 = false
                        // alert('2222222222222222222222222222222222222222222222')
                        this.$HTTP
                            .post('craft_log/post_rollback', {
                                material_number: this.RollBACK_post.material_number,
                                id: this.RollBACK_post.id
                            })
                            .then((res) => {
                                if (res.errcode != 0) {
                                    ElMessage.error(res.errmsg)

                                    // this.$store.state.technology = !this.$store.state.technology
                                    // this.$store.state.curIndex=1
                                } else {
                                    this.dialog2Loading = false
                                    this.dialog2 = false
                                    var postData = {
                                        page: this.currentPage,
                                        per_page: this.pagesize,
                                        material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`)
                                    }
                                    console.log(postData, 'postData')
                                    this.Vspost(this.$store.state.listObj.varApi.get_ls, postData)
                                    // this.Vspost(this.$store.state.listObj.varApi.get_ls, postData) //版本记录
                                    // this.addClick(this.RollBACK_post)
                                    // this.$HTTP
                                    //     .get('craft/get_detail', {
                                    //         material_number: this.RollBACK_post.material_number
                                    //     })
                                    //     .then((res) => {
                                    //         if (res.errcode != 0) {
                                    //             ElMessage.error(res.errmsg)
                                    //         } else {
                                    //             this.$store.state.release_stlist = res.result
                                    //         }
                                    //     })
                                    this.$notify({
                                        title: '回滚',
                                        message: '操作成功 ',
                                        type: 'success',
                                        duration: 2000
                                    })
                                }
                            })
                    } else {
                        // alert('1111111111111111111')
                        this.$HTTP
                            .post('bom_log/post_rollback', {
                                material_number: this.RollBACK_post.material_number,
                                id: this.RollBACK_post.id
                            })
                            .then((res) => {
                                if (res.errcode != 0) {
                                    ElMessage.error(res.errmsg)
                                } else {
                                    this.$store.state.technology = !this.$store.state.technology
                                    this.dialog2Loading = false
                                    this.dialog2 = false
                                    // this.$HTTP
                                    //     .get('craft/get_detail', {
                                    //         material_number: this.RollBACK_post.material_number
                                    //     })
                                    //     .then((res) => {
                                    //         if (res.errcode != 0) {
                                    //             ElMessage.error(res.errmsg)
                                    //         } else {
                                    //             this.$store.state.release_stlist = res.result
                                    //         }
                                    //     })
                                    var postData = {
                                        page: this.currentPage,
                                        per_page: this.pagesize,
                                        material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`)
                                    }
                                    console.log(postData, 'postData')
                                    this.Vspost(this.$store.state.listObj.varApi.get_ls, postData)
                                    this.$notify({
                                        title: '回滚',
                                        message: '操作成功 ',
                                        type: 'success',
                                        duration: 2000
                                    })
                                }
                            })
                    }
                })
                .catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消回滚'
                    })
                })
        },
        // 操作记录
        operate() {
            this.versionRi = false
            this.operateRi = true
        },
        // 版本记录
        versionR() {
            this.versionRi = true
            this.operateRi = false
        },
        // 分页
        handleSizeChange(size) {
            this.pagesize = size //每页下拉显示数据
        },
        handleCurrentChange(currentPage) {
            // console.log(currentPage,'currentPage');
            this.currentPage = currentPage //点击第几页
            const postData = {
                page: this.currentPage,
                per_page: 10
            }
            Object.assign(postData, { material_number: this.$store.state.listObj.items.number })
            this.Vspost(this.$store.state.listObj.varApi.get_ls, postData)
            // if(this.$store.state.curIndex = 2){

            // }else if(this.$store.state.curIndex = 1){
            //     this.Vspost(this.$store.state.listObj.varApi.get_ls, postData)
            // }
        },
        //删除未发布的版本
        delVersion(item) {
            if (item.son_materials) {
                console.log("bom")
            } else {
                console.log("工艺卡")
            }
            console.log(item, 'item')
            ElMessageBox.confirm(
                '确定删除当前未发布版本吗?',
                '删除',
                {
                    confirmButtonText: '确定删除',
                    cancelButtonText: '取消',
                    type: 'warning',
                    closeOnClickModal: false, // 禁止点击遮罩层关闭
                    closeOnPressEscape: false, // 禁止ESC键关闭
                    showClose: false,
                    beforeClose: (action, instance, done) => {
                        if (action === 'confirm') {
                            instance.confirmButtonLoading = true;
                            instance.cancelButtonClass = 'is-disabled';
                            if (item.son_materials) {
                                this.$HTTP.post("bom_log/post_del", { id: item.id }).then((res) => {
                                    console.log(res, 'res')
                                    if (res.errcode != 0) {
                                        done()
                                        ElMessage.error(res.errmsg)
                                        instance.confirmButtonLoading = false;
                                        instance.cancelButtonClass = '';
                                    } else {
                                        this.$emit('versinQuery', false)
                                        this.$emit('reload')
                                        ElMessage.success('删除成功');
                                        instance.confirmButtonLoading = false;
                                        instance.cancelButtonClass = '';
                                        const postData = {
                                            page: this.currentPage,
                                            per_page: 10
                                        }
                                        Object.assign(postData, { material_number: this.$store.state.listObj.items.number })
                                        this.Vspost(this.$store.state.listObj.varApi.get_ls, postData)
                                        done()
                                    }
                                })
                            } else {
                                this.$HTTP.post("craft_log/post_del", { id: item.id }).then((res) => {
                                    console.log(res, 'res')
                                    if (res.errcode != 0) {
                                        done()
                                        ElMessage.error(res.errmsg)
                                        instance.confirmButtonLoading = false;
                                        instance.cancelButtonClass = '';
                                    } else {
                                        this.$emit('versinQuery', false)
                                        this.$emit('reload')
                                        ElMessage.success('删除成功');
                                        instance.confirmButtonLoading = false;
                                        instance.cancelButtonClass = '';
                                        const postData = {
                                            page: this.currentPage,
                                            per_page: 10
                                        }
                                        Object.assign(postData, { material_number: this.$store.state.listObj.items.number })
                                        this.Vspost(this.$store.state.listObj.varApi.get_ls, postData)
                                        done()
                                    }
                                })
                            }

                        } else {
                            done()
                        }
                    }
                }
            )
            // .then(() => {
            //     ElMessage({
            //         type: 'success',
            //         message: '删除成功',
            //     })
            // })
            // .catch(() => {
            //     ElMessage({
            //         type: 'info',
            //         message: '已取消',
            //     })
            // })
        }
    }
}
</script>

<style scoped lang="scss">
.el-menu-vertical-demo {
    display: block;
    height: 100%;
}

.el-footer {
    text-align: center;
}

// .el-button {
//     height: 0;
//     font-size: 6px;
//     padding: 8px 5px;
// }
.huibutton {
    font-size: 15px;
    margin: 10px 0;
    padding: 13px 15px;
}

.left-panel {
    padding: 20px;
    display: flex;
    justify-content: space-around;
    font-size: 14px;
    cursor: pointer;
    /* font-weight: 600; */
}

.el-card__body {
    padding: 0 20px 20px 20px;
}

.lanse {
    font-weight: 600;
    color: #409eff;
    cursor: pointer;
}

.username {
    font-size: 14px;
}

.opname {
    color: #409eff;
}

.opcontent span {
    color: #000;
}

.el-pagination {
    position: absolute;
    bottom: 15px;
}

.editfontcolor {
    color: red;
}

.addfontcolor {
    color: #3ab596;
}

.newfontcolor {
    color: red;
}
</style>
