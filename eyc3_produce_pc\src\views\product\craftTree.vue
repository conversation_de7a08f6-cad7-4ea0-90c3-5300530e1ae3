<template>
    <el-main v-loading="pictLoading">
        <h2 class="H2">工艺卡</h2>
        <div class="card-header">
            <div
                v-for="(item, i) in headerList"
                :key="i"
            >
                {{ item.text }}：
                <span
                    style="color: rgba(0, 0, 0, 0.6)"
                    v-if="item.text !== '类型'"
                >
                    {{ objlist[item.key] ? objlist[item.key] : '暂无' }}
                </span>
                <span
                    v-else
                    style="color: rgba(0, 0, 0, 0.6)"
                >
                    {{
                        objlist[item.key] == 0
                            ? '产品'
                            : objlist[item.key] == 1
                            ? '部件'
                            : objlist[item.key] == 2
                            ? '零件'
                            : objlist[item.key] == 3
                            ? '原材料'
                            : '暂无'
                    }}
                </span>
            </div>
        </div>
    </el-main>
    <el-main v-loading="Tabledoing">
        <h2>工序过程</h2>
        <fromTable
            @showDrawer="showDrawer"
            @process_type="process_type"
            :crafts="craftsForm"
            :current-material-number="getCurrentMaterialNumber()"
            v-if="objlist.crafts"
        ></fromTable>
    </el-main>
    <div
        v-if="drawer"
        class="modal-container"
    >
        <div class="header_ta">
            <h3>{{ drawerName }}</h3>
            <el-icon
                :size="25"
                @click="close_button()"
            >
                <el-icon-Close />
            </el-icon>
        </div>
        <div class="modal-content">
            <div
                id="luckysheet"
                style="margin: 0 0 10px 0"
            ></div>
            <!-- <div v-show="isMaskShow" id="tip">Downloading</div> -->
        </div>
    </div>
</template>
<script>
import fromTable from './components/fromtableTree.vue'
import { exportExcel } from './components/export'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import LuckyExcel from 'luckyexcel'

export default {
    name: 'craft',
    components: {
        fromTable
    },
    data() {
        return {
            pictLoading: false,
            Tabledoing: false,
            drawer: false,
            headerList: [
                {
                    text: '编码',
                    key: 'number'
                },
                {
                    text: '名称',
                    key: 'title'
                },
                {
                    text: '图号',
                    key: 'drawing_number'
                },
                {
                    text: '类型',
                    key: 'type'
                },
                {
                    text: '原材料规格',
                    key: 'specs'
                },
                {
                    text: '单位',
                    key: 'unit_title'
                },
                {
                    text: '材质',
                    key: 'texture_title'
                },
                {
                    text: '原材料用量',
                    key: 'weight'
                },
                // {
                //     text: '编制',
                //     key: ''
                // },
                {
                    text: '校对',
                    key: 'craft_modify_user_name'
                },
                {
                    text: '修改时间',
                    key: 'updated_at'
                }
            ],
            objlist: {},
            craftsForm: [],
            isMaskShow: false,
            drawerName: '',
            lastToolCode: null,
            material_numberAll:'',
        }
    },
    props: {
        // 添加一个页面标识符属性，用于区分不同页面
        pageType: {
            type: String,
            default: 'default'
        }
    },
    created() {
        // 使用页面标识符来获取正确的数据
        this.getMaterialNumberKey();
        this.material_numberAll = sessionStorage.getItem(this.getMaterialNumberKey())

        this.$store.watch(
            (state) => state.listObjTree,
            (newValue) => {
                this.indata('craft/get_detail', { material_number: sessionStorage.getItem(this.getMaterialNumberKey()) })
            }
        )
        this.$store.watch(
            (state) => state.listObjTree.table,
            (newValue) => {
                this.indata('craft/get_detail', { material_number: sessionStorage.getItem(this.getMaterialNumberKey()) })
            }
        )
        // this.indata('craft/get_detail',{material_number:localStorage.getItem("parent_material_number")})
        this.post_data()
    },
    methods: {
        close_button() {
            this.drawer = false
            // 销毁原来表格
            window.luckysheet.destroy()
        },
        process_type() {
            this.drawer = false
            this.lastToolCode = null
            // 销毁原来表格
            window.luckysheet.destroy()
            // console.log('111111111111111111');
        },
        showDrawer(op) {
            if (this.lastToolCode !== op.tool_code) {
                // 检查 tool_code 是否发生改变
                this.lastToolCode = op.tool_code // 更新为新的 tool_code
                this.$HTTP.get('tool/get_info', { code: op.tool_code }).then((res) => {
                    if (res.errcode != 0) {
                        ElMessage.error(res.errmsg)
                    } else {
                        if (res.result) {
                            if (res.result.url) {
                                this.$HTTP.get(res.result.url).then((item) => {
                                    if (item) {
                                        this.isMaskShow = true
                                        this.drawerName = res.result.process_title
                                        // if(this.drawer) return
                                        this.drawer = true
                                        this.doingapi(res.result.url)
                                    }
                                })
                            } else {
                                this.$notify({
                                    title: '警告',
                                    message: '暂无匹配的xlsx文件',
                                    type: 'warning'
                                })
                            }
                        } else {
                            this.$notify({
                                    title: '警告',
                                    message: '暂无匹配的xlsx文件',
                                    type: 'warning'
                                })
                        }
                    }
                })
            }
        },
        doingapi(value, name) {
            // console.log('asgdjasgdjasgdhjasgdajsdga',value,name);
            LuckyExcel.transformExcelToLuckyByUrl(value, name, (exportJson, luckysheetfile) => {
                if (exportJson.sheets == null || exportJson.sheets.length == 0) {
                    alert('Failed to read the content of the excel file, currently does not support xls files!')
                    return
                }
                // console.log('exportJson', exportJson)
                // jsonData.value = exportJson

                this.isMaskShow = false
                // 销毁原来表格
                window.luckysheet.destroy()
                //重新创建新表格
                window.luckysheet.create({
                    container: 'luckysheet', //设定DOM容器id
                    showinfobar: false, //是否显示顶部信息栏
                    data: exportJson.sheets, //表格内容
                    title: exportJson.info.name, //表格标题
                    userInfo: exportJson.info.creator //
                })
            })
        },
        handleChange() {
            console.log(val)
        },
        // 获取当前页面的materialNumberKey
        getMaterialNumberKey() {
            return `parent_material_number_${this.pageType}_${sessionStorage.getItem('windowId')}`;
        },

        // 获取当前物料编号
        getCurrentMaterialNumber() {
            // 获取当前页面类型和窗口ID
            const windowId = sessionStorage.getItem('windowId') || '';
            const pageType = this.pageType || 'default';

            // 构建正确的material_number键
            const materialNumberKey = `parent_material_number_${pageType}_${windowId}`;

            // 尝试获取material_number
            let materialNumber = sessionStorage.getItem(materialNumberKey);

            // 如果没有找到特定页面的material_number，尝试从其他页面获取
            if (!materialNumber) {
                // 尝试从openBom页面获取
                const openBomKey = `parent_material_number_openBom_${windowId}`;
                materialNumber = sessionStorage.getItem(openBomKey);

                // 如果仍然没有，尝试从pegging页面获取
                if (!materialNumber) {
                    const peggingKey = `parent_material_number_pegging_${windowId}`;
                    materialNumber = sessionStorage.getItem(peggingKey);
                }

                // 如果仍然没有，尝试从openBomIndex页面获取
                if (!materialNumber) {
                    const openBomIndexKey = `parent_material_number_openBomIndex_${windowId}`;
                    materialNumber = sessionStorage.getItem(openBomIndexKey);
                }
            }

            // 如果仍然没有找到，尝试使用默认键
            if (!materialNumber) {
                materialNumber = sessionStorage.getItem(`parent_material_number_${windowId}`);
            }

            // 如果仍然没有找到，尝试使用this.material_numberAll
            if (!materialNumber && this.material_numberAll) {
                materialNumber = this.material_numberAll;
            }

            console.log('getCurrentMaterialNumber 返回物料编号:', materialNumber);
            return materialNumber;
        },

        post_data() {
            // 使用页面标识符来获取正确的数据
            let Array = JSON.parse(localStorage.getItem(`craftStaging_${this.material_numberAll}`))
            let num = sessionStorage.getItem(this.getMaterialNumberKey())
            let arrShow = false
            if (Array) {
                Array.forEach((item) => {
                    if (item.material_number === num) {
                        arrShow = true
                        this.$confirm('此工艺卡有缓存，是否展示缓存数据', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        })
                            .then(() => {
                                this.$message({
                                    type: 'success',
                                    message: '已展示暂存数据!'
                                })
                                this.indata(
                                    'craft/get_detail',
                                    { material_number: sessionStorage.getItem(this.getMaterialNumberKey()) },
                                    item.Array
                                )
                                // console.log('1');
                            })
                            .catch(() => {
                                this.indata('craft/get_detail', {
                                    material_number: sessionStorage.getItem(this.getMaterialNumberKey())
                                })
                                // console.log('2');
                                this.$message({
                                    type: 'info',
                                    message: '已取消'
                                })
                            })
                    }
                })
                if (!arrShow) {
                    this.indata('craft/get_detail', { material_number: sessionStorage.getItem(this.getMaterialNumberKey()) })
                }
            } else {
                // console.log('4');
                this.indata('craft/get_detail', { material_number: sessionStorage.getItem(this.getMaterialNumberKey()) })
            }
        },
        indata(url, page, op) {
            this.pictLoading = true
            this.Tabledoing = true

            // 如果page中没有material_number，尝试从sessionStorage获取
            if (!page) {
                page = {};
            }

            // 获取当前页面类型和窗口ID
            const windowId = sessionStorage.getItem('windowId') || '';
            const pageType = this.pageType || 'default';

            console.log('当前页面类型:', pageType);

            // 构建正确的material_number键
            const materialNumberKey = `parent_material_number_${pageType}_${windowId}`;

            // 尝试获取material_number
            let materialNumber = page.material_number;

            // 如果page中没有material_number，尝试从sessionStorage获取
            if (!materialNumber) {
                materialNumber = sessionStorage.getItem(materialNumberKey);
                console.log(`尝试从 ${materialNumberKey} 获取物料编号:`, materialNumber);
            }

            // 如果没有找到特定页面的material_number，尝试从其他页面获取
            if (!materialNumber) {
                // 尝试从openBom页面获取
                const openBomKey = `parent_material_number_openBom_${windowId}`;
                materialNumber = sessionStorage.getItem(openBomKey);
                console.log(`尝试从 ${openBomKey} 获取物料编号:`, materialNumber);

                // 如果仍然没有，尝试从pegging页面获取
                if (!materialNumber) {
                    const peggingKey = `parent_material_number_pegging_${windowId}`;
                    materialNumber = sessionStorage.getItem(peggingKey);
                    console.log(`尝试从 ${peggingKey} 获取物料编号:`, materialNumber);
                }

                // 如果仍然没有，尝试从openBomIndex页面获取
                if (!materialNumber) {
                    const openBomIndexKey = `parent_material_number_openBomIndex_${windowId}`;
                    materialNumber = sessionStorage.getItem(openBomIndexKey);
                    console.log(`尝试从 ${openBomIndexKey} 获取物料编号:`, materialNumber);
                }

                // 如果找到了物料编号，将其保存到当前页面的键中
                if (materialNumber) {
                    sessionStorage.setItem(materialNumberKey, materialNumber);
                    console.log(`将物料编号 ${materialNumber} 保存到 ${materialNumberKey}`);
                }
            }

            // 如果仍然没有找到，尝试使用默认键
            if (!materialNumber) {
                materialNumber = sessionStorage.getItem(`parent_material_number_${windowId}`);
                console.log(`尝试从默认键获取物料编号:`, materialNumber);
            }

            // 如果仍然没有找到，尝试使用this.material_numberAll
            if (!materialNumber && this.material_numberAll) {
                materialNumber = this.material_numberAll;
                console.log(`使用this.material_numberAll:`, materialNumber);
            }

            // 更新page对象
            page.material_number = materialNumber;

            // 确保material_number参数有值
            if (!page.material_number) {
                console.error(`Missing material_number in ${url} call`);
                ElMessage.error('缺少物料编号，无法获取工艺卡信息');
                this.pictLoading = false;
                this.Tabledoing = false;
                return;
            }

            console.log('使用的物料编号:', materialNumber, '页面类型:', pageType, '调用URL:', url);

            this.$HTTP.get(url, page).then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    // this.$store.state.listObj.table = false
                    // this.$store.state.listObj.upload = false
                    this.objlist = res.result
                    if (op) {
                        res.result.crafts=op
                        this.craftsForm = res.result
                    } else {
                        console.log(res.result,'op11111111111111111111111111111111111111111111111111');

                        this.craftsForm = res.result
                    }
                    this.pictLoading = false
                    this.Tabledoing = false
                }
            }).catch(error => {
                console.error(`Error in ${url} call:`, error);
                ElMessage.error('获取工艺卡信息失败');
                this.pictLoading = false;
                this.Tabledoing = false;
            })
        }
    }
}
</script>
<style lang="scss" scoped>
.modal-container {
    position: fixed;
    top: 0;
    right: 0;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    width: 30%;
    height: 100%;
    max-width: 340px;
    z-index: 10;
}

.header_ta {
    margin: 20px 0 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.H2 {
    /* margin: 30px 106px 10px; */
    margin: 30px 0px 10px;
}
.nopadding h2 {
    /* margin: 30px 120px 10px; */
    margin: 30px 14px 10px;
}
.card-header {
    display: flex;
    margin: 30px 14px;
    flex-wrap: wrap;
    align-items: center;
    font-size: 14px;
}
.card-header div {
    width: 33.33%;
    height: 45px;
}
#luckysheet {
    margin: 0px;
    padding: 0px;
    position: absolute;
    width: 100%;
    left: 0px;
    top: 60px;
    bottom: 0px;
    z-index: 9999;
}

#uploadBtn {
    font-size: 16px;
}
</style>
