<template>
    <el-main>
        <yy_tab
            v-if="tabitems"
            :tabitems="tabitems"
            @queryformdata="queryformdata"
            :derive="derive"
        ></yy_tab>
        <el-alert
            v-if="headertitle"
            :title="headertitle"
            type="success"
            style="margin: 20px 0"
        ></el-alert>
        <el-row v-if="headerpiece">
            <div class="content">
                <div
                    v-for="(item, index) in headerlist"
                    :key="index"
                    :class="listbackcolor === index ? 'itemcolor' : 'item'"
                    @click="listbutton(index, item)"
                >
                    <div class="itemtitle">{{ item.title }}</div>
                    <div
                        v-for="title in headerpiece.columns"
                        :key="title"
                        class="titlecontent"
                    >
                        <div class="contentkey">
                            <span class="stoptitle">{{ title.name }}</span
                            ><span>{{ item[title.prop] }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </el-row>
        <el-main>
            <el-row :gutter="15">
            <el-col
                :lg="8"
                v-for="item in columns"
                :key="item"
            >
                <yy_scEcharts
                    :title="item.title"
                    :grid="item.grid"
                    :typeEcharts="item.type"
                    :anchor="item.anchor"
                    :xAxis="item.xAxis"
                    :yAxis="item.yAxis"
                    :tooltip="item.tooltip"
                    :radar="item.radar"
                    :progress="item.progress"
                    :series="item.series"
                >
                </yy_scEcharts>
            </el-col>
            <wordCloud
                v-if="wordCloud"
                :wordCloud="wordCloud"
            ></wordCloud>
        </el-row>
        </el-main>
        <div
            class=""
            v-if="tableslot"
        >
            <div class="itemtitle">{{ tableslot.title }}</div>
            <tableslot :tableslot="tableslot"></tableslot>
        </div>
    </el-main>
</template>

<script>
import { ElMessage, ElNotification } from 'element-plus'
import wordCloud from './wordCloud'
import tableslot from './tableslot'

export default {
    name: 'yp_scEcharts',
    components: {
        wordCloud,
        tableslot
    },
    props: {
        url: { type: String, default: '' },
        columns: { type: Object, default: () => {} },
        headertitle: { type: String, default: '' },
        post_data: { type: Object, default: () => {} },
        tabitems: { type: Object, default: () => {} },
        headerpiece: { type: Object, default: () => {} },
        wordCloud: { type: Object, default: () => {} },
        tableslot: { type: Object, default: () => {} }
    },
    data() {
        return {
            headerlist: [],
            listbackcolor: null,
            list: [
                {
                    name: '测试1'
                },
                {
                    name: '测试2'
                },
                {
                    name: '测试3'
                },
                {
                    name: '测试4'
                },
                {
                    name: '测试5'
                },
                {
                    name: '测试6'
                },
                {
                    name: '测试7'
                }
            ],
            series: [
                // {
                //     radius: this.radius,
                //     center: this.center,
                //     label: this.label,
                //     data: this.itemdata,
                //     type: this.typeEcharts,
                //     barWidth: this.barWidth
                // },
                // {
                //     radius: this.radius,
                //     center: this.center,
                //     label: this.label,
                //     data: [110, 180, 120, 120, 60, 90, 110],
                //     type: this.typeEcharts,
                //     barWidth: this.barWidth,
                //     anchor:this.anchor,
                //     progress: this.progress,
                // },
            ]
        }
    },
    mounted() {
        this.getData()
        if (this.headerpiece) {
            this.gettitleData()
        }
    },

    methods: {
        // 处理远程
        //获取数据
        getData(option) {
            var url
            this.columns.forEach((op) => {
                url = op.url
                this.$HTTP
                    .post(url, { ...this.post_data, ...op.post_data, ...this.queryform, ...option })
                    .then((res) => {
                        if (res.errcode != 0) {
                        } else {
                            this.columns.forEach((obj) => {
                                obj.series.forEach((item) => {
                                    res.result.series.forEach((objitem) => {
                                        if (obj.title.text == objitem.name) {
                                            console.log((item.data = objitem.data), '12111')
                                            obj.xAxis.data = res.result[obj.prop] //X轴数据展示
                                        }
                                    })
                                })
                            })
                        }
                    })
            })
        },
        gettitleData(post_data) {
            this.$HTTP.post(this.headerpiece.url, post_data).then((res) => {
               this.headerlist = res.result.data
            })
        },
        listbutton(op, item) {
            this.listbackcolor = op
            this.getData({ area_id: item.id })
        },
        // 处理tab数据
        queryformdata(value) {
            console.log(value, '日期')
            this.queryform = value
            setTimeout(() => {
                this.getData()
            }, 100)
        }
    }
}
</script>

<style scoped lang="scss">
.content {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
}

.item {
    flex: 1;
    background: #fff;
    border-radius: 4px;
    margin: 0 15px 15px 0;
    width: calc((100% - 50px) / 4);
    min-width: calc((100% - 50px) / 4);
    max-width: calc((100% - 50px) / 4);
    padding: 20px;
}
.item:hover {
    cursor: pointer;
    -webkit-box-shadow: 0px 12px 32px 4px rgba(0, 0, 0, 0.04), 0px 8px 20px rgba(0, 0, 0, 0.08);
    -moz-box-shadow: 0px 12px 32px 4px rgba(0, 0, 0, 0.04), 0px 8px 20px rgba(0, 0, 0, 0.08);
    box-shadow: 0px 12px 32px 4px rgba(0, 0, 0, 0.04), 0px 8px 20px rgba(0, 0, 0, 0.08);
}
.item:nth-child(4n) {
    margin-right: 0;
}
.itemcolor {
    flex: 1;
    background: #007fff;
    color: #fff;
    margin: 0 15px 14px 0;
    border-radius: 4px;
    width: calc((100% - 50px) / 4);
    min-width: calc((100% - 50px) / 4);
    max-width: calc((100% - 50px) / 4);
    padding: 20px;
}
.itemcolor:hover {
    cursor: pointer;
    -webkit-box-shadow: 0px 12px 32px 4px rgba(0, 0, 0, 0.04), 0px 8px 20px rgba(0, 0, 0, 0.08);
    -moz-box-shadow: 0px 12px 32px 4px rgba(0, 0, 0, 0.04), 0px 8px 20px rgba(0, 0, 0, 0.08);
    box-shadow: 0px 12px 32px 4px rgba(0, 0, 0, 0.04), 0px 8px 20px rgba(0, 0, 0, 0.08);
}
.itemcolor:nth-child(4n) {
    margin-right: 0;
}
.titlecontent {
    display: inline-block;
    margin: 10px 20px;
    width: calc((100% - 10px) / 4);
    min-width: calc((100% - 10px) / 4);
    max-width: calc((100% - 10px) / 4);
}
.itemtitle {
    font-size: 17px;
    font-weight: 600;
}
</style>
