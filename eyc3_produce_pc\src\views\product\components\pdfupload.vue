<!--
 * @Descripttion: 文件导入
 * @version: 1.0
 * @Author: sakuya
 * @Date: 2022年5月24日11:30:03
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-01-06 20:56:45
-->

<template>
    <div>
        <el-button type="primary" icon="sc-icon-upload" @click="open">图纸上传</el-button>
    </div>

    <el-dialog v-model="dialog" title="导入" :width="550" :close-on-click-modal="false" append-to-body destroy-on-close>
        <el-radio-group v-model="radio" @change="handleChange">
            <el-radio :label="1">非下料图纸</el-radio>
            <el-radio :label="2">下料图纸</el-radio>
            <el-radio :label="3">外购件</el-radio>
        </el-radio-group>
        <el-progress v-if="loading" v-loading="loading" :text-inside="true" :stroke-width="20" :percentage="percentage"
            style="margin-bottom: 15px" />
        <div>
            <div class="upload-demo">
                <!-- @dragover.prevent @drop="handleFolderDrop" -->
                <div class="el-upload" >
                    <input class="el-upload-dragger" style="border: 1px solid red; width: 100%; height: 100%"
                        type="file" multiple @change="handleFileUpload" directory webkitdirectory />
                </div>
                <div class="add">
                    <el-icon class="el-icon--upload" size="60px"><el-icon-upload-filled /></el-icon>
                    <div class="el-upload__text"><span>点击上传</span></div>
                </div>
            </div>
            <div v-for="(item, index) in folder_files" :key="index" class="files_css">
                <div class="file_name">
                    <el-icon size="15px"><el-icon-document /></el-icon>
                    <span>{{ item.name }}</span>
                </div>
                <el-icon class="iconX" size="15px" @click.stop="edit_fle(index)"><el-icon-close /></el-icon>
            </div>
            <div>总数：{{ folder_files.length }}件</div>
            <div style="text-align: right">
                <el-button type="primary" @click="add_files">确认上传</el-button>
            </div>
        </div>

        <sc-dialog v-model="dialog2" :title="switchisShow ? '图纸是否可以上传' : '工艺卡版本对比'" :loading="dialog2Loading">
            <el-descriptions direction="vertical" :column="2" :size="size" v-if="switchisShow" border>
                <el-descriptions-item :label="'总共' + `${folder_files.length}` + '个可以上传的图纸'">
                    <div v-for="item in folder_files" :key="item" style="margin-bottom: 10px">
                        <div class="addfontcolor">
                            <div>图纸名：{{ item.name }}</div>
                        </div>
                    </div>
                </el-descriptions-item>
                <el-descriptions-item :label="'总共' + `${differenceSet.length}` + '个不可以上传的图纸'">
                    <div v-for="item in differenceSet" :key="item" style="margin-bottom: 10px">
                        <div class="addfontcolor">
                            <div>图纸名：{{ item.name }}</div>
                        </div>
                    </div>
                </el-descriptions-item>
            </el-descriptions>
            <el-descriptions direction="vertical" :column="2" :size="size" v-else border>
                <el-descriptions-item label="对比工序信息"> </el-descriptions-item>
                <el-descriptions-item label="当前工序信息"> </el-descriptions-item>
            </el-descriptions>
        </sc-dialog>
        <!-- 图纸名称是否符合 -->
        <sc-dialog v-model="sheetName">
            <el-descriptions direction="vertical" :column="2" :size="size" border>
                <el-descriptions-item :label="'总共' + `${processedArray.length}` + '个符合图纸命名规则'">
                    <div v-for="item in processedArray" :key="item" style="margin-bottom: 10px">
                        <div class="addfontcolor">
                            <div :style="{ color: item.isMarked ? 'red' : 'black' }">图纸名：{{ item.name }}</div>
                        </div>
                    </div>
                </el-descriptions-item>
                <el-descriptions-item :label="'总共' + `${sheetNameOnMeet.length}` + '个不符合图纸命名规则'">
                    <div v-for="item in sheetNameOnMeet" :key="item" style="margin-bottom: 10px">
                        <div class="addfontcolor">
                            <div>图纸名：{{ item.name }}</div>
                        </div>
                    </div>
                </el-descriptions-item>
            </el-descriptions>
            <template v-slot:footer>
                <el-button type="primary" @click="confirmUploadInto" ref="myButton"
                    v-if="clickInto == '拉入'">确认上传</el-button>
                <el-button type="primary" @click="confirmUpload" ref="myButton" v-else>确认上传</el-button>
            </template>
        </sc-dialog>
    </el-dialog>
    <el-dialog title="上传成功" v-model="dialogTableVisible">
        <el-table :data="gridData">
            <el-table-column property="file_name" label="图纸名称" width="150"></el-table-column>
            <el-table-column property="name" label="" width="200"></el-table-column>
            <el-table-column property="address" label=""></el-table-column>
        </el-table>
    </el-dialog>
</template>

<script>
import { ElMessage } from 'element-plus'
export default {
    name: '',
    emits: ['success'],
    props: {
        postDataObj: { type: Object, default: () => { } },
        files_url: { type: Array, default: () => null },
        FolderDrop: { type: Array, default: () => null },
        folder_files: { type: Array, default: () => null }
    },
    data() {
        return {
            dialog2: false,
            sheetName: false,
            dialog2Loading: false,
            // 点击上传的数据
            fileListAdd: [],
            // 符合图纸命名
            sheetNameMeet: [],
            // 不符合图纸命名
            sheetNameOnMeet: [],
            dialogTableVisible: false,
            dialog: false,
            loading: false,
            folder_files: [],
            gridData: [],
            differenceSet: [],
            files_url: [],
            FolderDrop: [],
            switchisShow: true, //对比切换
            FolderDrop_event: null,
            radio: 1,
            type: 0,
            fileType:1,
            // 区分是点击还是拉入
            clickInto: '',
            signLoading: false
        }
    },
    computed: {
        processedArray() {
            const prefixMap = {};
            const result = [];

            this.sheetNameMeet.forEach(item => {
                const parts = item.name.split(' ');
                const prefix = parts[0];

                if (!prefixMap[prefix]) {
                    prefixMap[prefix] = [];
                }

                prefixMap[prefix].push(item);

                // 标记如果前缀不止一个对象  
                item.isMarked = prefixMap[prefix].length > 1;

                result.push(item);
            });

            return result;
        }
    },
    mounted() {
        console.log(this.postDataObj, '>>>>>>')
    },
    methods: {
        handleChange(newRadioValue) {
            console.log('当前选择的值为：', newRadioValue)
            this.fileType=newRadioValue
            if (newRadioValue == 3) {
                this.type = 1
            }
            ; (this.folder_files = []), (this.files_url = []), (this.FolderDrop = []), (this.FolderDrop_event = null)
        },
        open() {
            this.dialog = true
                ; (this.folder_files = []), (this.files_url = []), (this.FolderDrop = [])
            // this.formData = {}
        },
        edit_fle(index) {
            this.folder_files.splice(index, 1)
            this.files_url.splice(index, 1)
        },
        async handleFolderDrop(event) {
            this.clickInto = '拉入'
            event.preventDefault()
            this.FolderDrop_event = event.dataTransfer.items
            for (let i = 0; i < this.FolderDrop_event.length; i++) {
                if (this.FolderDrop_event[i].webkitGetAsEntry) {
                    const entry = this.FolderDrop_event[i].webkitGetAsEntry()
                    if (entry.isDirectory) {
                        this.readDirectory(entry)
                    } else if (entry.isFile) {
                        entry.file((file) => {
                            this.FolderDrop.push(file)
                        })
                    }
                }
            }
            console.log(this.FolderDrop, 'this.FolderDrop');
            const loading = this.$loading({
                lock: true,
                text: '上传中',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            })
            loading.close();
            let type = ''
            if (this.radio == 3) {
                type = 1
            } else {
                type = ''
            }
            await this.$HTTP.get('drawing/get_bom_all', { type: type }).then((res) => {
                // this.list = res.result.data
                if (res.errcode != 0) {
                    this.loading = false
                    ElMessage.error(res.errmsg)
                } else {
                    loading.close();
                }
            })
            console.log(this.FolderDrop, 'this.FolderDrop');
            const newData = this.FolderDrop.map((item) => ({ name: item.name }))
            console.log(newData, ' 全部图纸11111111111')
            if (this.radio == 1) {
                const valid = [];
                const invalid = [];
                // const regex = /^TMZ/;
                const regex = /^(?!\s*$).+?\s/
                newData.forEach(item => {
                    if (regex.test(item.name) && item.name.endsWith('.pdf')) {
                        valid.push(item);
                    } else {
                        invalid.push(item);
                    }
                });
                console.log('符合', valid);
                this.sheetNameMeet = valid
                this.sheetNameOnMeet = invalid
                console.log('不符合', invalid);
            } else if (this.radio == 2) {
                const valid = [];
                const invalid = [];
                const regex = /^TXL-/;
                // const regex = /^(?!\s*$).+?\s/
                newData.forEach(item => {
                    if (regex.test(item.name)) {
                        valid.push(item);
                    } else {
                        invalid.push(item);
                    }
                });
                console.log('符合', valid);
                this.sheetNameMeet = valid
                this.sheetNameOnMeet = invalid
                console.log('不符合', invalid);
            } else if (this.radio == 3) {
                const valid = [];
                const invalid = [];
                const regex = /^\d{10}\s+/;
                newData.forEach(item => {
                    if (regex.test(item.name) && item.name.endsWith('.pdf')) {
                        valid.push(item);
                    } else {
                        invalid.push(item);
                    }
                });
                console.log('符合', valid);
                this.sheetNameMeet = valid
                this.sheetNameOnMeet = invalid
            }
            this.sheetName = true
        },
        readDirectory(directoryEntry) {
            const reader = directoryEntry.createReader()
            reader.readEntries((entries) => {
                entries.forEach((entry) => {
                    if (entry.isDirectory) {
                        this.readDirectory(entry)
                    } else if (entry.isFile) {
                        entry.file((file) => {
                            this.FolderDrop.push(file)
                        })
                    }
                })
            })
        },
        // 点击上传图纸
        async handleFileUpload(event) {
            const loading = this.$loading({
                lock: true,
                text: '上传中',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            })
            this.folder_files = []
            // 获取用户选择的所有文件
            const fileList = event.target.files
            this.fileListAdd = fileList
            const array = [];
            // for (let key in fileList) {  
            //     // 由于对象的键是字符串，但数组索引是整数，我们需要将键转换为整数（如果需要的话）  
            //     // 但在这个特定的例子中，由于我们只是想要将空对象添加到数组中，所以不需要转换键  
            //     // 直接将值（即空对象）添加到数组中  
            // arr.push(fileList[key]);  
            // }
            // 使用Object.keys()遍历对象的键  
            loading.close();
            Object.keys(fileList).forEach(key => {
                // 将每个键对应的值添加到数组中  
                array.push(fileList[key]);
            });
            const newData = array.map((item) => ({ name: item.name }))
            console.log(newData, ' 全部图纸呀呀咿呀咿呀哟一一')
            if (this.radio == 1) {
                const valid = [];
                const invalid = [];
                // const regex = /^TMZ/;
                const regex = /^(?!\s*$).+?\s/
                newData.forEach(item => {
                    if (regex.test(item.name) && item.name.endsWith('.pdf')) {
                        valid.push(item);
                    } else {
                        invalid.push(item);
                    }
                });
                console.log('符合', valid);
                this.sheetNameMeet = valid
                this.sheetNameOnMeet = invalid
                console.log('不符合', invalid);
            } else if (this.radio == 2) {
                const valid = [];
                const invalid = [];
                const regex = /^TXL-/;
                // const regex = /^(?!\s*$).+?\s/
                newData.forEach(item => {
                    if (regex.test(item.name)) {
                        valid.push(item);
                    } else {
                        invalid.push(item);
                    }
                });
                console.log('符合', valid);
                this.sheetNameMeet = valid
                this.sheetNameOnMeet = invalid
                console.log('不符合', invalid);
            } else if (this.radio == 3) {
                const valid = [];
                const invalid = [];
                const regex = /^\d{10}\s+/;
                newData.forEach(item => {
                    if (regex.test(item.name) && item.name.endsWith('.pdf')) {
                        valid.push(item);
                    } else {
                        invalid.push(item);
                    }
                });
                console.log('符合', valid);
                this.sheetNameMeet = valid
                this.sheetNameOnMeet = invalid
            }
            this.sheetName = true

        },
        async confirmUpload() {
            this.sheetName = false
            const loading = this.$loading({
                lock: true,
                text: '上传中',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            })
            let type = ''
            if (this.radio == 3) {
                type = 1
            } else {
                type = ''
            }
            // 调用递归函数处理文件夹中的文件
            await this.$HTTP.get('drawing/get_bom_all', { type: type }).then((res) => {
                console.log(JSON.stringify(res.result),888)
                // this.list = res.result.data
                if (res.errcode != 0) {
                    this.loading = false
                    ElMessage.error(res.errmsg)
                } else {
                    const numbers = res.result
                    this.dialog2 = true
                    loading.close();
                    this.switchisShow = true
                    if (numbers.length > 0) {
                        for (let i = 0; i < this.fileListAdd.length; i++) {
                            numbers.forEach((item) => {
                                const file = this.fileListAdd[i]
                                const str = file.name
                                const parts = file.name.split(' ')
                                var result = null
                                if (this.radio == 1) {
                                    result = str.match(/^(\S*)/)[0]
                                } else if (this.radio == 2) {
                                    result = str.slice(0, -4)
                                } else if (this.radio == 3) {
                                    result = parts[0]
                                }
                                let numberValue=item.number.toUpperCase()
                                if (
                                    result.toUpperCase() == numberValue 
                                    // || result.toUpperCase() == 'TXL-' + numberValue ||
                                    // result.toUpperCase() == 'TMZ-' + numberValue
                                ) {
                                    console.log(result,'result');
                                    console.log(numberValue,'====================================');
                                    const fileInfo = {
                                        name: file.name,
                                        size: file.size,
                                        type: file.type
                                    }
                                    // 防止 folder_files 中出现重复项
                                    if (!this.folder_files.some((f) => f.name === file.name)) {
                                        this.folder_files.push(fileInfo)
                                    }
                                    // this.folder_files.push(fileInfo)
                                    const formData = new FormData()
                                    formData.append('file', this.fileListAdd[i])
                                    formData.append('upload_type', 'local')
                                    formData.append('file_name', file.name)
                                    formData.append('number', numberValue)
                                    // 添加到 files_url 时也确保不重复
                                    if (
                                        !this.files_url.some(
                                            (f) => f.get('file_name') === file.name && f.get('number') === numberValue
                                        )
                                    ) {
                                        this.files_url.push(formData)
                                    }
                                    // this.files_url.push(formData)
                                    // console.log(file,this.files_url,'qweqweqweqw')
                                }
                            })
                        }

                    } else {
                        ElMessage.error('没有图纸编号')
                    }
                }
            })
            // console.log(this.files_url);
            if (this.files_url.length <= 0) {
                ElMessage.error('没有符合图纸编号的文件')
            }
            //创建一个空数组来存储结果  
            const array = [];
            // 使用Object.keys()遍历对象的键  
            Object.keys(this.fileListAdd).forEach(key => {
                // 将每个键对应的值添加到数组中  
                array.push(this.fileListAdd[key]);
            });
            console.log(array, 'fileList11111111111');
            const newData = array.map((item) => ({ name: item.name }))
            // console.log(newData, ' 全部图纸')
            // console.log(this.folder_files[0], 'result')
            let difference = newData.filter((item2) => !this.folder_files.some((item1) => item1.name === item2.name))
            // // console.log(difference, '差集');
            this.differenceSet = difference

        },
        async confirmUploadInto() {
            this.sheetName = false
            const loading = this.$loading({
                lock: true,
                text: '上传中',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            })
            // alert('11111111111111')
            let type = ''
            if (this.radio == 3) {
                type = 1
            } else {
                type = ''
            }
            await this.$HTTP.get('drawing/get_bom_all', { type: type }).then((res) => {
                // this.list = res.result.data
                if (res.errcode != 0) {
                    this.loading = false
                    ElMessage.error(res.errmsg)
                } else {
                    const numbers = res.result
                    // this.dialog2Loading = true
                    this.dialog2 = true
                    loading.close();
                    this.switchisShow = true
                    if (numbers.length > 0) {
                        for (let i = 0; i < this.FolderDrop.length; i++) {
                            // console.log(this.FolderDrop[i], ' 全部图纸');
                            // const regex = /^(.*)\./;
                            // const regex = /(\d+)(.*)/ // 匹配数字和后面的所有字符
                            const regex = /TZJ-\d{2}/
                            // const result = str.match(regex)[2] // 获取匹配结果的第二组
                            const file = this.FolderDrop[i]
                            const parts = file.name.split(' ')
                            console.log(parts[0], 'file？？？？？？？？？？？？？？？')
                            // const fileNameWithoutExt = file.name.split('.')[0];
                            var result = null
                            if (this.radio == 1) {
                                result = file.name.match(/^(\S*)/)[0]
                            } else if (this.radio == 2) {
                                result = file.name.slice(0, -4)
                            } else if (this.radio == 3) {
                                result = parts[0]
                            }
                            // 检查当前文件是否已存在于上传列表中
                            let isDuplicate = false
                            if (!isDuplicate) {
                                numbers.forEach((item) => {
                                    // console.log(item);
                                    this.files_url.forEach((formDatum) => {
                                        if (
                                            formDatum.get('file_name') === file.name &&
                                            formDatum.get('number') === item.number
                                        ) {
                                            // console.log('1');
                                            isDuplicate = true
                                            return
                                        }
                                    })
                                    let numberValue=item.number.toUpperCase()
                                    console.log(numberValue,'numberValue');

                                    // console.log(result +"-------",item.number ,'TXL-'+item.number,'111111111');
                                    if (
                                        result.toUpperCase() == numberValue ||
                                        result.toUpperCase() == 'TXL-' + numberValue ||
                                        result.toUpperCase() == 'TMZ-' + numberValue
                                    ) {
                                        const fileInfo = {
                                            name: file.name,
                                            size: file.size,
                                            type: file.type
                                        }
                                        // 防止 folder_files 中出现重复项
                                        if (!this.folder_files.some((f) => f.name === file.name)) {
                                            this.folder_files.push(fileInfo)
                                        }

                                        const formData = new FormData()
                                        formData.append('file', file)
                                        formData.append('upload_type', 'local')
                                        formData.append('file_name', file.name)
                                        formData.append('number', numberValue)

                                        // 添加到 files_url 时也确保不重复
                                        if (
                                            !this.files_url.some(
                                                (f) =>
                                                    f.get('file_name') === file.name && f.get('number') === numberValue
                                            )
                                        ) {
                                            this.files_url.push(formData)
                                        }
                                    }
                                })
                            }
                        }
                    } else {
                        ElMessage.error('没有图纸编号')
                    }
                }
                if (this.files_url.length <= 0) {
                    this.$alert('没有符合图纸编号的文件', '提示!', {
                        confirmButtonText: '确定'
                        // callback: (action) => {
                        //     this.$message({
                        //         type: 'info'
                        //     })
                        // }
                    })
                }
            })
            const newData = this.FolderDrop.map((item) => ({ name: item.name }))
            // console.log(newData, ' 全部图纸')
            // console.log(this.folder_files[0], 'result')
            let difference = newData.filter((item2) => !this.folder_files.some((item1) => item1.name === item2.name))
            // console.log(difference, '差集');
            this.differenceSet = difference

        },
        async add_files() {
            try {

                let filesList = []
                console.log(this.files_url,'files_url');
                // for (const item of this.files_url) {
                for (const i in this.files_url) {
                    // 假设item是一个包含文件信息的对象，你可能需要调整这部分  
                    // 如果item是FormData，那么就不需要从它那里get任何东西，直接传FormData即可  
                    // 这里我假设item是一个普通对象，包含了文件名和编号等信息  

                    // 发送POST请求  
                    let item = this.files_url[i];
                    const res = await this.$HTTP.post(`${import.meta.env.VITE_APP_FILE_UPLOAD}file/post_upload`, item);

                    if (res.errcode !== 0) {
                        ElMessage.error(res.errmsg);
                        // 如果需要，可以在这里处理错误，比如中断循环等  
                        // throw new Error(res.errmsg); // 取消注释可以中断循环并抛出错误  
                    } else {
                        // 构建新的对象  
                        let obj = {
                            file_name: item.get('file_name'), // 假设item直接有file_name属性  
                            url: res.result,
                            number: item.get('number'), // 假设item直接有number属性  
                            type: this.type,
                            fileType:this.fileType
                        };
                        // 调用另一个函数处理obj（如果需要的话）  
                        // this.post_add_url(obj); // 取消注释以调用
                        filesList.push(obj);
                        if (i == this.files_url.length - 1) {
                            console.log(filesList,'filesList');
                            this.maptest(filesList); // 取消注释以调用
                        }
                    }
                }

                // 如果需要，可以在这里清空files_url等数组  
                // this.folder_files = [];  
                // this.files_url = [];  
                // this.FolderDrop = [];  
            } catch (error) {
                // 处理任何在循环中抛出的错误  
                ElMessage.error(error.message);
            }
        },
        async maptest(list) {
            const loading = this.$loading({
                lock: true,
                text: '上传中',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            })
            let activeRequests = 0;
            console.log(list, 'list');
            for (const op of list) {
                console.log(list, 'listlistlistlistlistlist');
                // this.post_add_url(op);
                console.log(op, 'op11111111111111111111');
                const res = await this.$HTTP.post('drawing/post_add', {...op,fileType:this.fileType})
                if (res.errcode != 0) {
                    this.$notify({
                        title: op.get('file_name'),
                        message: '上传失败 ',
                        type: 'error',
                        duration: 5000
                    })
                } else {
                    activeRequests++;
                }
            }
            console.log(list.length, '00000000000000000000000000000');
            if (list.length === activeRequests) {
                loading.close()
                this.dialogTableVisible = true
                this.dialog = false
                this.loading = false
                this.$emit('success', true)
                this.gridData = list
            } else {
                alert('上传中')
            }
            // console.log(activeRequests!=0,'activeRequestsactiveRequestsactiveRequests');
        },
        // add_files() {
        //     const requests = new Map();

        //     this.files_url.forEach((item, index) => {
        //         const request = this.$HTTP.post(`${import.meta.env.VITE_APP_FILE_UPLOAD}file/post_upload`, item)
        //             .then(response => ({
        //                     file_name: item.get('file_name'), // 假设item直接有file_name属性  
        //                     url: response.result,
        //                     number: item.get('number'), // 假设item直接有number属性  
        //                     type: this.type
        //                 }))
        //             .catch(error => ({ index, error }));
        //             // requests.set(index, request);
        //             console.log(request, 'requests');
        //             request.then((res)=>{
        //                 this.post_add_url(res)
        //                 console.log(res,'resssssssssssssssssssssssssssssssssss');
        //             })
        //         });
        //     // Promise.all(requests.values()).then(results => {
        //     //     results.forEach(result => {
        //     //         if (result.error) {
        //     //             console.error(`Error on item ${result.index}:`, result.error);
        //     //         } else {
        //     //             console.log( result,'lllllllllllllllllllllllllllllllllll');
        //     //             this.post_add_url(result)
        //     //         }
        //     //     });
        //     // });
        // },
        post_add_url(op) {
            console.log(op, 'op11111111111111111111');
            // this.$HTTP.post('drawing/post_add', op).then((res) => {
            //     if (res.errcode != 0) {
            //         this.$notify({
            //             title: item.get('file_name'),
            //             message: '上传失败 ',
            //             type: 'error',
            //             duration: 5000
            //         })
            //         // ElMessage.error(res.errmsg)
            //     } else {
            //         // activeRequests--;
            //         // if (activeRequests === 0) {
            //         this.dialogTableVisible = true
            //         this.dialog = false
            //         this.loading = false
            //         this.$emit('success', true)
            //         this.gridData = this.folder_files
            //         //console.log('所有请求都结束了');
            //         // }
            //         // if(this.signLoading==false){
            //         //     console.log(this.folder_files,'llllllllllllllllllll')
            //         //     this.$alert(`${this.folder_files.map(item.name).join(',')}上传成功`, '提示!', {
            //         //         confirmButtonText: '确定'

            //         //         // callback: (action) => {
            //         //         //     this.$message({
            //         //         //         type: 'info'
            //         //         //     })
            //         //         // }
            //         //     })
            //         //     // this.treeList('material/get_category_product_all')
            //         //     this.signLoading=true
            //         // }
            //         // this.$notify({
            //         //     title: item.get('file_name'),
            //         //     message: '上传成功 ',
            //         //     type: 'success',
            //         //     duration: 5000
            //         // })
            //     }
            // })
            // .finally(() => {
            //     activeRequests--;
            //     if (activeRequests === 0) {
            //         this.dialogTableVisible = true
            //     this.dialog = false
            //     this.loading = false
            //     this.$emit('success', true)
            //     this.gridData = this.folder_files
            //         console.log('所有请求都结束了');
            //     }
            // });
        }
    }
}
</script>

<style lang="scss" scoped>
.upload-demo {
    display: flex;
    justify-content: center;
    align-items: center;
}

.el-upload {
    width: 100%;
    position: relative;
    font-size: 14px;
    display: inline-block;
    z-index: 1;
    padding: 70px 0;
    border: 1px dashed #aaa;
    text-align: center;
    vertical-align: middle;

    &:hover {
        cursor: pointer;
    }
}

.el-upload-dragger {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    opacity: 0;

    &:hover {
        cursor: pointer;
    }
}

.el-upload-dragger:hover {
    border-color: #3594f4;
}

.add {
    position: absolute;
    text-align: center;
}

.el-upload__text span {
    color: #3594f4;
}

.files_css {
    width: 80%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .iconX {
        margin: 5px 0 0 20px;
        cursor: pointer;
    }
}

.el-icon {
    margin-right: 7px;
}

.file_name {
    margin-top: 5px;
    display: flex;
    align-items: center;
}
</style>
