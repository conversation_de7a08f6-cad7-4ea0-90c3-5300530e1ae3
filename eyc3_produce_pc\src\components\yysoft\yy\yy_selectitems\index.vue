<template>
    <el-form
        :inline="true"
        :model="value"
    >
        <el-form-item>
            <el-select
                @visible-change="getData"
                v-model="value"
                clearable
                filterable
                :placeholder="placeholder"
            >
                <el-option
                    v-for="option in items"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                >
                </el-option>
                <el-option
                    v-for="item in remoteoptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                >
                </el-option>
            </el-select>
            <el-select
                v-model="value1"
                filterable
                clearable
                :placeholder="placeholder"
                v-if="disnode"
                :loading="loading"
                @visible-change="getList"
            >
                <el-option
                    v-for="item in remotetows"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                ></el-option>
            </el-select>
        </el-form-item>
    </el-form>
</template>

<script>
export default {
    name: 'yy_selectitems',
    props: {
        placeholder: { type: String, default: '请选择' },
        props: { type: Object, default: () => {} },
        form: { type: Object, default: () => {} },
        remote: { type: Object, default: () => {} },
        item: { type: Object, default: () => {} }
    },
    data() {
        return {
            loading: false,
            remoteoptions: [],
            remotetows: [],
            value: '',
            value1: '',
            disnode: false,
            postid: '',
            postData: {},
        }
    },
    watch: {
        value(val) {
            this.postid = val
            this.form[this.item.name] = val
            this.disnode = true
            if (val == '') {
                this.value1 = null
                this.value = this.items[0].value
                this.disnode = false
            }
        },
        value1(val) {
            this.form[this.item.name] = val
            if(val == ''){
                this.form[this.item.name] = this.postid
            }
        },
    },
    created(){
        this.items = this.item.options.items
        if(this.items.length>0){
            this.value = this.items[0].value
        }
    },
    mounted() {},
    methods: {
        //处理远程选项数据
        getData(e) {
            if (e) {
                if (this.remote) {
                    this.remoteoptions = []
                    this.loading = true
                    this.postData = {}
                    var rs = this.remote.data
                    for (const key in rs) {
                        if (typeof rs[key] == 'number') {
                            this.postData[key] = rs[key]
                        } else if (rs[key].substring(0, 1) == '$') {
                            this.postData[key] = this.form[rs[key].substring(1)]
                            if (this.postData[key] == '') return
                        } else {
                            this.postData[key] = rs[key]
                        }
                    }
                    console.log(this.postData,'this.postData');
                    this.$HTTP.get(this.remote.api, this.postData).then((res) => {
                        if (res.errcode != 0) {
                            ElMessage.error(res.errmsg)
                        } else {
                            this.handleData(res.result)
                            this.loading = false
                        }
                    })
                }
            }
        },
        getList(e) {
            if (e) {
                this.remotetows = []
                // this.postData[this.remote.value] = this.postid
                this.remote.data[this.remote.value] = this.postid
                this.$HTTP.get(this.remote.api, this.remote.data).then((res) => {
                    if (res.errcode != 0) {
                        ElMessage.error(res.errmsg)
                    } else {
                        this.handleDatatow(res.result)
                        this.loading = false
                    }
                })
            }
        },
        // 处理获取到的数据
        handleData(info) {
            info.forEach((item) => {
                console.log(this.remote)
                var handleData = {}
                handleData.value = item[this.remote.value]
                handleData.label = item[this.remote.label]
                this.remoteoptions.push(handleData)
            })
        },
        handleDatatow(info) {
            info.forEach((item) => {
                var handleData = {}
                handleData.value = item[this.remote.value]
                handleData.label = item[this.remote.label]
                this.remotetows.push(handleData)
            })
        }
    }
}
</script>

<style></style>
