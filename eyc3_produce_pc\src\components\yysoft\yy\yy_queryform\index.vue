<!--
 * @Descripttion: 动态表单渲染器
 * @version: 1.0
 * @Author: sakuya
 * @Date: 2021年9月22日09:26:25
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-01-05 16:42:28
-->

<template>
    <el-skeleton
        v-if="renderLoading || Object.keys(form).length == 0"
        animated
    />

    <el-form
        v-else
        ref="form"
        :model="form"
        :inline="true"
        :label-width="labelwidth"
        v-loading="loading"
        element-loading-text="Loading..."
    >
        <div class="table-header">
            <div class="search">
                <template
                    v-for="(item, index) in filed"
                    :key="index"
                >
                    <!-- <el-col
                        :span="item.span || 24"
                        v-if="!hideHandle(item)"
                    > -->
                    <div
                        class="form-title"
                        v-if="item.component == 'title'"
                    >
                        {{ item.label }}
                    </div>
                    <template v-else-if="!item.children">
                        <el-form-item
                            v-if="item.options && !item.options.merge"
                            :prop="item.name"
                            :rules="item.rules"
                        >
                            <template #label>
                                {{ item.label }}
                                <el-tooltip
                                    v-if="item.tips"
                                    :content="item.tips"
                                >
                                    <el-icon><el-icon-question-filled /></el-icon>
                                </el-tooltip>
                            </template>
                            <template v-if="item.component">
                                <formslot
                                    :item="item.options ? item : Object.assign(item, { options: {} })"
                                    :form="form"
                                    @weekdata="weekdata"
                                ></formslot>
                            </template>
                            <div
                                v-if="item.message"
                                class="el-form-item-msg"
                            >
                                {{ item.message }}
                            </div>
                        </el-form-item>
                    </template>
                    <template v-else-if="item.children">
                        <el-form-item
                            :prop="item.name"
                            :rules="item.rules"
                        >
                            <template #label>
                                {{ item.label }}
                                <el-tooltip
                                    v-if="item.tips"
                                    :content="item.tips"
                                >
                                    <el-icon><el-icon-question-filled /></el-icon>
                                </el-tooltip>
                            </template>
                            <template v-if="item.component">
                                <formslot
                                    :item="item.options ? item : Object.assign(item, { options: {} })"
                                    :form="form"
                                    @weekdata="weekdata"
                                ></formslot>
                            </template>
                            <div
                                v-if="item.message"
                                class="el-form-item-msg"
                            >
                                {{ item.message }}
                            </div>
                        </el-form-item>
                        <template
                            v-for="(itm, inx) in item.children"
                            :key="inx"
                        >
                            <template v-if="!itm.options.merge">
                                <el-form-item
                                    :prop="itm.name"
                                    :rules="itm.rules"
                                >
                                    <template #label>
                                        {{ itm.label }}
                                        <el-tooltip
                                            v-if="itm.tips"
                                            :content="itm.tips"
                                        >
                                            <el-icon><el-icon-question-filled /></el-icon>
                                        </el-tooltip>
                                    </template>
                                    <template v-if="itm.component">
                                        <formslot
                                            :item="itm.options ? itm : Object.assign(itm, { options: {} })"
                                            :form="form"
                                            @weekdata="weekdata"
                                        ></formslot>
                                    </template>
                                    <div
                                        v-if="itm.message"
                                        class="el-form-item-msg"
                                    >
                                        {{ itm.message }}
                                    </div>
                                </el-form-item>
                            </template>
                        </template>
                    </template>
                    <!-- </el-col> -->
                </template>
            </div>

            <!-- <el-col :span="24">
				<el-form-item> -->
            <slot>
                <el-button
                    type="primary"
                    @click="submit"
                    >提交</el-button
                >
            </slot>
            <!-- </el-form-item>
			</el-col> -->
        </div>
    </el-form>
</template>

<script>
import http from '@/utils/request'
import useTabs from '@/utils/useTabs'

import { defineAsyncComponent } from 'vue'
import { dayjs } from 'element-plus'
const tableselectRender = defineAsyncComponent(() => import('./items/tableselect'))
const formslot = defineAsyncComponent(() => import('./items/formslot'))
const scEditor = defineAsyncComponent(() => import('@/components/scEditor'))

export default {
    name: 'yy_queryform',
    props: {
        filed: { type: Object, default: () => {} },
        movel: { type: Object, default: () => {} },
        modelValue: {
            type: Object,
            default: () => {
                return {}
            }
        },
        labelwidth: { type: String, default: '120px' },

        loading: { type: Boolean, default: false }
    },
    emits: ['update:modelValue', 'weekdata'],
    components: {
        tableselectRender,
        formslot,
        scEditor
    },
    data() {
        return {
            form: {},
            ruleFormRef: {},
            renderLoading: false,
            weekdataform: {},
            earlyHandleData: {}
        }
    },
    watch: {
        modelValue(val) {
            if (this.hasConfig) {
                // console.log('modelValue', val)
                var arr = Object.keys(this.modelValue)
                if (arr.length >= 1) {
                    this.form = this.modelValue
                }
                // console.log('modelValue',this.form)
                // this.deepMerge(this.form, this.modelValue)
            }
        },
        config() {
            this.render()
        },
        form: {
            handler(val) {
                // console.log('form',this.modelValue)
                this.$emit('update:modelValue', val)
                this.handlerformitems(val)
                // this.$forceUpdate()
            },
            deep: true
        }
    },
    computed: {
        hasConfig() {
            // console.log(this.filed)
            return Object.keys(this.filed).length > 0
        },
        hasValue() {
            return Object.keys(this.modelValue).length > 0
        }
    },
    created() {
        if (this.hasConfig) {
            this.render()
        }
        // console.log(this.modelValue)
        var arr = Object.keys(this.modelValue)
        if (arr.length >= 1) {
            this.form = this.modelValue
        }
        // earlyHandleData
        this.filed.forEach((el) => {
            if (el.options) {
                if (el.options.earlyHandleData) {
                    Object.assign(this.earlyHandleData, el.options.earlyHandleData)
                }
            }
        })
    },
    mounted() {
        Object.assign(this.form, this.earlyHandleData)
    },
    methods: {
        //构建form对象
        render() {
            this.filed.forEach((item) => {
                if (item.component == 'checkbox') {
                    if (item.name) {
                        const value = {}
                        item.options.items.forEach((option) => {
                            value[option.name] = option.value
                        })
                        this.form[item.name] = value
                    } else {
                        item.options.items.forEach((option) => {
                            this.form[option.name] = option.value
                        })
                    }
                } else if (item.component == 'upload') {
                    if (item.name) {
                        const value = {}
                        item.options.items.forEach((option) => {
                            value[option.name] = option.value
                        })
                        this.form[item.name] = value
                    } else {
                        item.options.items.forEach((option) => {
                            this.form[option.name] = option.value
                        })
                    }
                } else {
                    this.form[item.name] = item.value
                }
            })
            this.$emit('update:modelValue', this.form)
            // if (this.hasValue) {
            //     this.form = this.deepMerge(this.form, this.modelValue)
            // }
            // this.getData()
            // this.renderLoading = true
            // Promise.all(remoteData).then(()=>{
            // 	this.renderLoading = false
            // })
        },
        async getruleFormRef() {
            let res = await this.$refs.form.validate()
            return res
        },
        //处理远程选项数据
        getData() {
            this.renderLoading = true
            var remoteData = []
            this.filed.forEach((item) => {
                if (item.options && item.options.remote) {
                    var postData = {}
                    var rs = item.options.remote.data
                    for (const key in rs) {
                        if (rs[key].substring(0, 1) == '$') {
                            postData[key] = this.form[rs[key].substring(1)]
                            if (postData[key] == '') return
                        } else {
                            postData[key] = rs[key]
                        }
                    }
                    var req = http.get(item.options.remote.api, postData).then((res) => {
                        item.options.remoteitems = res.result
                    })
                    remoteData.push(req)
                }
            })
            Promise.all(remoteData).then(() => {
                this.renderLoading = false
            })
        },
        //合并深结构对象
        deepMerge(obj1, obj2) {
            let key
            for (key in obj2) {
                obj1[key] =
                    obj1[key] &&
                    obj1[key].toString() === '[object Object]' &&
                    obj2[key] &&
                    obj2[key].toString() === '[object Object]'
                        ? this.deepMerge(obj1[key], obj2[key])
                        : (obj1[key] = obj2[key])
            }
            return obj1
            //return JSON.parse(JSON.stringify(obj1))
        },
        //处理动态隐藏
        hideHandle(item) {
            if (item.hideHandle) {
                const exp = eval(item.hideHandle.replace(/\$/g, 'this.form'))
                return exp
            }
            return false
        },
        //处理动态必填
        rulesHandle(item) {
            if (item.requiredHandle) {
                const exp = eval(item.requiredHandle.replace(/\$/g, 'this.form'))
                var requiredRule = item.rules.find((t) => 'required' in t)
                requiredRule.required = exp
            }
            return item.rules
        },
        //数据验证
        validate(valid, obj) {
            return this.$refs.form.validate(valid, obj)
        },
        scrollToField(prop) {
            return this.$refs.form.scrollToField(prop)
        },
        resetFields() {
            return this.$refs.form.resetFields()
        },
        //提交
        submit() {
            // // console.log('提交')
            // // console.log(this.form)
            // Object.assign(this.form, this.weekdataform)
            this.$emit('submit', this.form)
        },
        // 处理formitems
        handlerformitems(form) {
            this.filed.forEach((item) => {
                if (item.children) {
                    for (let i in item.children) {
                        let itm = item.children[i]
                        if (itm.options) {
                            if (itm.options.mergecontent) {
                                if (form[item.name] === itm.options.mergecontent) {
                                    // console.log('数值一致消失', form[item.name], itm.options.mergecontent)
                                    itm.options.merge = true
                                    if (!item.options.relatedata) {
                                        form[itm.name] = undefined
                                    }
                                } else {
                                    // console.log('数值不一致出现', form[item.name], itm.options.mergecontent)
                                    itm.options.merge = false
                                }
                            } else if (itm.options.showcontent || itm.options.showcontent == 0) {
                                if (form[item.name] === itm.options.showcontent) {
                                    itm.options.merge = false
                                } else {
                                    // console.log('数值不一致消失', form[item.name], itm.options.showcontent)
                                    itm.options.merge = true
                                    if (!item.options.relatedata) {
                                        form[itm.name] = undefined
                                    }
                                }
                            }
                        }
                    }
                }
            })
        },
        // 处理week数据
        weekdata(date) {
            this.$emit('weekdata', date)
        }
    }
}
</script>

<style></style>
