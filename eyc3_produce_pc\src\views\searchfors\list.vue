<template>
    <el-container>
        <el-header class="search-header">
            <div class="right-panel-search">
                <el-form inline>
                    <el-form-item label="物料名称">
                        <el-input v-model="Dialogsearch.title" placeholder="物料名称" clearable></el-input>
                    </el-form-item>
                    <el-form-item label="规格">
                        <el-input v-model="Dialogsearch.specs" placeholder="规格" clearable></el-input>
                    </el-form-item>
                    <el-form-item label="图号">
                        <el-input v-model="Dialogsearch.drawing_number" placeholder="图号" clearable></el-input>
                    </el-form-item>
                    <el-form-item label="物料编码">
                        <el-input v-model="Dialogsearch.number" placeholder="物料编码" clearable></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" icon="el-icon-search" @click="Confirm">搜索</el-button>
                    </el-form-item>
                </el-form>
            </div>
        </el-header>
        <el-main class="nopadding" v-loading="pictLoading">
            <scTable ref="multipleTableRef" :data="tableData" style="width: 100%; height: 100%" :fit="true"
                :highlight-current-row="true" @row-click="handleRowClick" @row-contextmenu="rowContextmenu"
                :lthority="limauthority" row-key="id" hidePagination hideDo>
                <template #empty>
                    <el-empty :description="description" :image-size="200"></el-empty>
                </template>
                <el-table-column type="index" label="序号" align="center" width="50">
                </el-table-column>
                <el-table-column width="50" label="图标" #default="scope">
                    <el-icon size="16px" color="color" v-if="scope.row.type == 3">
                        <!-- 原材料 -->
                        <sc-icon-raw-material />
                    </el-icon>
                    <el-icon size="16px" color="color" v-if="scope.row.type == 0">
                        <!-- 产品 -->
                        <sc-icon-product />
                    </el-icon>
                    <el-icon size="16px" color="color" v-if="scope.row.type == 2">
                        <!-- 零件 -->
                        <sc-icon-part />
                    </el-icon>
                    <el-icon size="16px" color="red" v-if="scope.row.type == 1">
                        <!-- 部件 -->
                        <sc-icon-mponent />
                    </el-icon>
                </el-table-column>
                <!-- <el-table-column
                    label="ID"
                    min-width="70"
                    align="center"
                >
                    <template #default="scope">
                        <span>{{ scope.row.id }}</span>
                    </template>
                </el-table-column> -->
                <el-table-column prop="number" label="代号" min-width="100" align="center">
                    <template #default="scope">
                        <span>{{ scope.row.number }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="title" label="名称" min-width="100" align="center" sortable>
                    <template #default="scope">
                        <span>{{ scope.row.title }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="drawing_number" label="图号" min-width="100" align="center" sortable>
                    <template #default="scope">
                        <span>{{ scope.row.drawing_number }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="specs" label="规格" min-width="100" align="center" sortable>
                    <template #default="scope">
                        <span>{{ scope.row.specs }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="texture_title" label="材质" min-width="100" align="center">
                    <template #default="scope">
                        <span>{{ scope.row.texture_title }}</span>
                    </template>
                </el-table-column>
            </scTable>
        </el-main>
        <el-footer>
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page="Dialogsearch.page" :page-sizes="[10, 20, 30, 40]" :page-size="Dialogsearch.per_page"
                layout="total, sizes, prev, pager, next, jumper" :total="total" small>
            </el-pagination>
        </el-footer>
    </el-container>
    <div class="box-menu" v-if="menuVisible" :style="{ left: menu_left + 'px', top: menu_top + 'px' }">
        <div @click.stop="pegging_click()">
            <el-button type="link" icon="el-icon-edit">BOM反查</el-button>
        </div>
    </div>
</template>

<script>
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
export default {
    name: 'searchfors',
    data() {
        return {
            menuVisible: false,
            pictLoading: false,
            checkBom: null,
            keyword: '',
            menu_left: 0,
            menu_top: 0,
            total: 0,
            tableData: [],
            description: '暂无数据',
            Dialogsearch: {
                page: 1,
                per_page: 50,
                title: "",//零件类型
                number: "",//物料编码
                specs: "",//规格
                drawing_number: "",//图号
            },
        }
    },
    mounted() {
        console.log(this.$route.params, 'this.$route.params')
        if (JSON.stringify(this.$route.params) != '{}') {
            this.keyword = this.$route.params.tags
            if (this.keyword) {
                this.Confirm()
            } else {
                ElMessage.error('此数据没有可查询的标签')
            }
        }

        // 添加键盘事件监听器，按回车键触发搜索
        document.addEventListener('keydown', this.handleKeyDown)
    },

    beforeDestroy() {
        // 移除键盘事件监听器
        document.removeEventListener('keydown', this.handleKeyDown)
    },
    methods: {
        // 处理键盘事件，按回车键触发搜索
        handleKeyDown(event) {
            // 如果按下的是回车键（keyCode 13）
            if (event.keyCode === 13 || event.key === 'Enter') {
                // 检查当前焦点是否在输入框上
                const activeElement = document.activeElement;
                const isInputFocused = activeElement.tagName === 'INPUT' &&
                                      activeElement.classList.contains('el-input__inner');

                // 如果焦点在搜索输入框上，或者没有特定元素获得焦点
                if (isInputFocused || activeElement === document.body) {
                    // 触发搜索功能
                    this.Confirm();
                }
            }
        },

        pegging_click() {
            console.log(this.checkBom, 'this.BomObj')
            this.$store.state.isShowOutTree = false
            this.form.parent_number = this.checkBom.number
            this.$store.state.listObj_bom = {
                isShow: true,
                isShowTree: true,
                items: this.checkBom,
                table: false,
                upload: false
            }
            this.$router.push({ path: '/search/tree' })

            this.menuVisible = false
        },
        rowContextmenu(row, column, event) {
            this.$emit('rownotification', false)
            this.$store.state.menuVisibletop = true
            console.log(this.$store.state, this.$store.state.menuVisibletop, 'this.$store.menuVisibletop')
            this.BomObj = row
            this.checkBom = {
                ctype: 'product',
                drawing_number: row.drawing_number,
                id: row.id,
                title: row.title,
                son: row.son,
                type: row.type,
                number: row.number,
                dosage: row.dosage,
                part_type: row.part_type,
                status: row.status,
                specs: row.specs
            }
            this.$store.state.border_bom = [
                {
                    id: row.id,
                    title: row.title,
                    number: row.number
                }
            ]
            this.$store.state.cost_number = row.number
            this.menuVisible = true
            // 节点数据
            this.form = {
                parent_material_number: row.number,
                material_number: '',
                title: '',
                type: '',
                drawing_number: '',
                specs: '',
                weight: '',
                unit_title: '',
                unit_id: '',
                texture_id: '',
                texture_title: '',
                price: '',
                quota: '',
                part_type_id: '',
                part_type_title: '',
                dosage: '',
                category_title: '',
                category_id: ''
            }
            // 将菜单显示在鼠标点击旁边定位
            this.menu_left = event.clientX + 50
            this.menu_top = event.clientY - 0
            document.addEventListener('click', this.foo)
        },
        foo() {
            this.menuVisible = false
            document.removeEventListener('click', this.foo)
        },
        Confirm() {
            if (this.Dialogsearch) {
                // console.log('搜索');
                this.pictLoading = true
                this.$HTTP.post('material/get_ls', this.Dialogsearch).then((res) => {
                    if (res.errcode != 0) {
                        this.pictLoading = false
                        if (res.errcode != 510) {
                            ElMessage.error(res.errmsg)
                        } else {
                            this.description = '权限不足'
                        }
                    } else {
                        // console.log('====================================');
                        // console.log(res);
                        this.pictLoading = false
                        // console.log(res.result,'res.result111');
                        this.tableData = res.result.data
                        this.total = res.result.total
                    }
                })
            } else {
                ElMessage.error('请先输入查询的内容')
            }
        },
        handleSizeChange(val) {
            // console.log(`每页 ${val} 条`)
            this.Dialogsearch.per_page = val
            this.Confirm()
        },
        handleCurrentChange(val) {
            // console.log(`当前页: ${val}`)
            this.Dialogsearch.page = val
            this.Confirm()
        },
        handleRowClick(op, event, column) {
            this.$store.state.listObj = {
                isShow: false,
                items: op,
                table: false,
                upload: false
            }
            let myArray =
            {
                id: op.id,
                craft_small_ver: op.craft_small,
                ctype: "product",
                drawing_number: op.drawing_number,
                title: op.title,
                number: op.number,
                type: op.type,
            }

            this.$store.state.bomAddIndex = myArray
            this.$router.push({ path: '/index/crud/IndexopenBom' })
        }
    }
}
</script>

<style scoped lang="scss">
.search-header {
    display: flex;
    flex-direction: column;
    height: 200px;
    line-height: 150px;
}

.box-menu {
    // width: 100px;
    position: absolute;
    z-index: 1000;
    background-color: #fff;
    box-shadow: 0px 0px 10px #ccc, 0px 0px 20px #ccc, 0px 0px 30px #ccc;
    padding: 10px;

    div {
        cursor: pointer;
        line-height: 30px;
    }
}
</style>
