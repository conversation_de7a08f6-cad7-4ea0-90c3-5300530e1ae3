<template>
    <div class="result">
        <el-col>
            <div
                ref="chart1"
                :style="{ width: `${this.width}px`, height: `${this.height}px` }"
            ></div>
        </el-col>
    </div>
</template>

// 词云图
<script>
import * as echarts from 'echarts'
import 'echarts-wordcloud/dist/echarts-wordcloud'
import 'echarts-wordcloud/dist/echarts-wordcloud.min'

export default {
    name: 'VisitShow',
    props: {
        width: {
            type: Number,
            default: 500
        },
        height: {
            type: Number,
            default: 340
        },
        wordCloud: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            userVisitNum: [],
            date: [
                {
                    name: '暂无热门地址',
                    value: 30
                },
              ],
            goodVisitNum: [],
            goodsName: [],
            name: '0'
        }
    },
    mounted() {
        this.showEeharts()
    },

    methods: {
        showEeharts() {
            this.$HTTP.get(this.wordCloud.url).then((res) => {
              if(res.result.length>0){
                this.date = res.result
              }
              this.echartsvord()
            })
        },
        echartsvord(){
          var chart1 = echarts.init(this.$refs.chart1)
            var chart1Option = {
                title: {
                    text: '热门地点-词云', //标题
                    x: 'center',
                    textStyle: {
                        fontSize: 23,
                        lineHeight: 30, // 行高
                        textBorderColor: "transparent", // 文字本身的描边颜色。
                        textBorderWidth: 0, // 文字本身的描边宽度。
                        textShadowColor: "transparent", // 文字本身的阴影颜色。
                        textShadowBlur: 0, // 文字本身的阴影长度。
                        textShadowOffsetX: 0, // 文字本身的阴影 X 偏移。
                        textShadowOffsetY: 0, //  文字本身的阴影 Y 偏移。
                    }
                },
                backgroundColor: '#fff', //F7F7F7
                tooltip: {
                    show: true
                },
                series: [
                    {
                        name: '热门地点', //数据提示窗标题
                        type: 'wordCloud',
                        sizeRange: [12, 64], //画布范围，如果设置太大会出现少词（溢出屏幕）
                        rotationRange: [-45, 90], //数据翻转范围
                        //shape: 'circle',
                        textPadding: 0,
                        autoSize: {
                            enable: true,
                            minSize: 6
                        },
                        textStyle: {
                            normal: {
                                color: function () {
                                    return (
                                        'rgb(' +
                                        [
                                            Math.round(Math.random() * 160),
                                            Math.round(Math.random() * 160),
                                            Math.round(Math.random() * 160)
                                        ].join(',') +
                                        ')'
                                    )
                                }
                            },
                            emphasis: {
                                shadowBlur: 10,
                                shadowColor: '#333'
                            }
                        },
                        data: this.date
                    }
                ]
            }
            chart1.setOption(chart1Option)
        }

    }
}
</script>
<style lang="less"></style>
