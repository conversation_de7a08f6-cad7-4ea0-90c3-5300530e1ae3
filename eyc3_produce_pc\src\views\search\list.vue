<template>
    <el-container>
        <el-container class="teble-content">
            <el-header
                ><el-breadcrumb
                    :separator-icon="ArrowRight"
                    style="padding-bottom: 10px"
                >
                    <el-breadcrumb-item
                        v-for="item in arraylist"
                        :key="item"
                        ><a
                            href="javascript:void(0);"
                            @click="breadClick(item)"
                            >{{ item.title }}</a
                        ></el-breadcrumb-item
                    >
                </el-breadcrumb></el-header
            >
            <el-main class="nopadding">
                <el-table
                    ref="multipleTableRef"
                    :data="list.tableData"
                    style="width: 100%; height: 100%"
                    :fit="true"
                    :highlight-current-row="true"
                    @row-contextmenu="rowContextmenu"
                >
                    <template #empty>
                        <el-empty
                            description="暂无数据"
                            :image-size="200"
                        ></el-empty>
                    </template>
                    <el-table-column
                        prop="number"
                        label="代号"
                        min-width="100"
                        align="center"
                    >
                        <template #default="scope">
                            <span @click="titleClick(scope.row)">{{ scope.row.number }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="title"
                        label="名称"
                        min-width="100"
                        align="center"
                    >
                        <template #default="scope">
                            <span>{{ scope.row.title }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="dosage"
                        label="用量"
                        min-width="100"
                        align="center"
                    >
                        <template #default="scope">
                            <el-input
                                v-if="scope.row.edit"
                                v-model="scope.row.dosage"
                                placeholder="用量"
                            ></el-input>
                            <span v-else>{{ scope.row.dosage }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="dosage"
                        label="操作"
                        min-width="100"
                        align="center"
                    >
                        <template #default="scope">
                            <el-button
                                @click.stop="returnData(scope.row)"
                                type="primary"
                                size="medium"
                            >
                                <span
                                    class="el-icon-check"
                                    aria-hidden="true"
                                    >返回BOM表单</span
                                >
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-main>
        </el-container>
        <el-aside>
            <el-main>
                <div class="card-box">
                    <el-card shadow="never" header="详情信息" style="margin-top: 20px;">
                        <el-descriptions border :column="1">
                            <el-descriptions-item label="物料标题">{{ BOM_list.title?BOM_list.title:'暂无数据' }}</el-descriptions-item>
                            <el-descriptions-item label="编号">{{ BOM_list.number?BOM_list.number:'暂无数据' }}</el-descriptions-item>
                            <el-descriptions-item label="类型">{{ BOM_list.type==0?'产品':BOM_list.type==1?'部件':BOM_list.type==2?'零件':BOM_list.type==3?'原材料':'暂无数据' }}</el-descriptions-item>
                            <el-descriptions-item label="版本">{{ BOM_list.ver?BOM_list.ver:'暂无数据' }}</el-descriptions-item>
                            <el-descriptions-item label="单位标题">{{ BOM_list.unit_title?BOM_list.unit_title:'暂无数据' }}</el-descriptions-item>
                            <el-descriptions-item label="分类标题">{{ BOM_list.category_title?BOM_list.category_title:'暂无数据' }}</el-descriptions-item>
                            <el-descriptions-item label="单位重量">{{ BOM_list.weight?BOM_list.weight:'暂无数据' }}</el-descriptions-item>
                            <el-descriptions-item label="材料定额">{{ BOM_list.quota?BOM_list.quota:'暂无数据' }}</el-descriptions-item>
                            <el-descriptions-item label="材质标题">{{ BOM_list.texture_title?BOM_list.texture_title:'暂无数据' }}</el-descriptions-item>
                            <el-descriptions-item label="零件类型标题">{{ BOM_list.part_type_title?BOM_list.part_type_title:'暂无数据' }}</el-descriptions-item>
                            <el-descriptions-item label="规格">{{ BOM_list.specs?BOM_list.specs:'暂无数据' }}</el-descriptions-item>
                            <el-descriptions-item label="图纸编号">{{ BOM_list.drawing_number?BOM_list.drawing_number:'暂无数据' }}</el-descriptions-item>
                            <el-descriptions-item label="添加人姓名">{{ BOM_list.user_name?BOM_list.user_name:'暂无数据' }}</el-descriptions-item>
                            <el-descriptions-item label="最后修改人姓名">{{ BOM_list.bom_modify_user_name?BOM_list.bom_modify_user_name:'暂无数据' }}</el-descriptions-item>
                            <el-descriptions-item label="创建时间">{{ BOM_list.created_at?BOM_list.created_at:'暂无数据' }}</el-descriptions-item>
                            <el-descriptions-item label="最后修改时间">{{ BOM_list.updated_at?BOM_list.updated_at:'暂无数据' }}</el-descriptions-item>
                        </el-descriptions>
                    </el-card>
                    <!-- <div v-else>
                        <el-empty
                            description="暂无数据"
                            :image-size="100"
                        ></el-empty>
                    </div> -->
                </div>
            </el-main>
        </el-aside>
    </el-container>
</template>

<script>
import { mapState } from 'vuex'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
export default {
    name: 'tableBase',
    data() {
        return {
            list: {
                tableData: []
            },
            dialog2: false,
            BOM_list:{},
        }
    },
    computed: {},
    created() {
        if(this.$store.state.border_bom){
            let obj = this.$store.state.border_bom[0]
            this.arraylist = this.$store.state.border_bom
            this.post_data({material_number:obj.number})
            this.BOM_var_post({id:obj.id})
        }
    },
    methods: {
        // 返回BOM按钮
        returnData(op){
            this.$store.state.listObj = {
                    isShow:true,
                    items:op,
                    table:false,
                    upload:false,
            }
            let myArray = [
                {
                    id: op.id,
                    title: op.title,
                    number: op.number,
                }
            ]
            // console.log(this.$store.state.listObj);
            localStorage.setItem("arraylist",JSON.stringify(myArray))
            localStorage.setItem("parent_material_number",op.number)
            localStorage.setItem("material_id",op.id)
            this.$router.push('/product/index')
        },
        // 面包屑
        breadClick(op) {
            this.arraylist.splice(this.arraylist.indexOf(op) + 1)
            this.post_data({ material_number: op.number })
            this.BOM_var_post({id:op.id})
        },
        titleClick(item, column, event){
            this.arraylist.push({
                id: item.id,
                title: item.title,
                number: item.number,
            });
            this.post_data({material_number:item.number})
            this.BOM_var_post({id:item.id})
        },
        post_data(op){
            this.$HTTP.post('bom/get_parent_material', op).then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    this.list.tableData = res.result
                }
            })
        },
        BOM_var_post(op){
            this.$HTTP.post('material/get_info', op).then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    // console.log(res.result);
                    this.BOM_list = res.result
                }
            })
        },
    }
}
</script>

<style scoped lang="scss">
.teble-content {
    width: 100%;
    height: 95%;
}
.pagin {
    position: absolute;
    bottom: 20px;
    min-width: 200px;
}
.box-menu {
    width: 100px;
    position: absolute;
    z-index: 1000;
    background-color: #fff;
    box-shadow: 0px 0px 10px #ccc, 0px 0px 20px #ccc, 0px 0px 30px #ccc;
    padding: 10px;

    div {
        cursor: pointer;
        line-height: 30px;
    }
}

.demo-drawer__footer {
    position: fixed;
    bottom: 10px;
    right: 10px;
}
.demo-form-inline {
    padding: 0 20px;
}
.formType {
    margin-bottom: 50px;
    .title {
        font-size: 17px;
        font-weight: 550;
        padding: 20px;
        border-bottom: 2px solid #e9e9e9;
        margin: 0 0 10px 0;
    }
}
</style>
