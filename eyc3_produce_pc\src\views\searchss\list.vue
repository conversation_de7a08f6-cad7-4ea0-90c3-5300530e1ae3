<template>
    <el-header>
        <el-page-header
            @back="goBack"
            content="打印"
        >
        </el-page-header>
    </el-header>
    <el-main v-loading="Loading_log">
        <el-card shadow="never">
            <el-tabs tab-position="top">
                <el-button
                    type="primary"
                    @click="print"
                    >打印</el-button
                >
                <!-- Form -->
                <el-button
                    text
                    @click="dialogFormClick()"
                >
                    打印编辑
                </el-button>
                <div style="height: 20px"></div>
                <div
                    class="printMain"
                    ref="printMain"
                >
                    <div
                        class="tablelist"
                        v-for="item in gonyi"
                        :key="item"
                    >
                        <table
                            border="1"
                            width="95%"
                            @dblclick="double"
                        >
                            <thead>
                                <tr class="firstHead">
                                    <th
                                        colspan="2"
                                        rowspan="2"
                                        width="5%"
                                    >
                                        <img
                                            class="logo_cass"
                                            src="http://files.qixuw.com/dingb9614df94342f570a1320dcb25e91351/canyin/2023-09-26/Imi4donswOIJl7FjWGrhSRhdFuxVDJqG.png"
                                            alt=""
                                        />
                                    </th>
                                    <th
                                        colspan="2"
                                        rowspan="2"
                                    >
                                        工艺卡
                                    </th>
                                    <th colspan="2">编码</th>
                                    <th colspan="1">{{ item.number ? item.number : '暂无' }}</th>
                                    <!-- 固号 -->
                                    <th
                                        colspan="1"
                                        width="50px"
                                    >
                                        图号
                                    </th>
                                    <th colspan="3">
                                        {{ item.drawing_number ? item.drawing_number : '暂无' }}
                                    </th>
                                    <th
                                        colspan="1"
                                        width="50px"
                                    >
                                        版本
                                    </th>
                                    <th
                                        colspan="1"
                                        width="50px"
                                    >
                                        {{ item.craft_ver ? item.craft_ver : '暂无' }}
                                    </th>
                                    <th
                                        colspan="1"
                                        width="50px"
                                    >
                                        编制
                                    </th>
                                    <th colspan="1">
                                        {{ item.craft_user_name ? item.craft_user_name : '暂无' }}
                                    </th>
                                    <th
                                        colspan="1"
                                        rowspan="3"
                                    >
                                        <img
                                            src="http://files.qixuw.com/dingb9614df94342f570a1320dcb25e91351/canyin/2023-09-26/gvL6klYjqyy0G9nWQN8ZDLeW8318TJHX.jpg"
                                            alt=""
                                            class="QRcode"
                                        />
                                    </th>
                                </tr>
                                <tr class="firstHead">
                                    <th
                                        colspan="2"
                                        width="50px"
                                    >
                                        材料名称
                                    </th>
                                    <th colspan="1">{{ item.title ? item.title : '暂无' }}</th>
                                    <th colspan="1">名称</th>
                                    <th colspan="3">{{ item.title ? item.title : '暂无' }}</th>
                                    <th colspan="1">版次</th>
                                    <th colspan="1">
                                        {{ item.craft_small_ver ? item.craft_small_ver : '暂无' }}
                                    </th>
                                    <th colspan="1">校对</th>
                                    <th colspan="1">暂无</th>
                                </tr>
                                <tr class="firstHead">
                                    <th colspan="2">材料类型</th>
                                    <th width="50px">
                                        {{
                                            item.type == 0
                                                ? '产品'
                                                : item.type == 1
                                                ? '部件'
                                                : item.type == 2
                                                ? '零件'
                                                : item.type == 3
                                                ? '原材料'
                                                : '暂无'
                                        }}
                                    </th>
                                    <th
                                        colspan="1"
                                        width="50px"
                                    >
                                        材质
                                    </th>
                                    <th
                                        colspan="3"
                                        width="50px"
                                    >
                                        {{ item.texture_title ? item.texture_title : '暂无' }}
                                    </th>
                                    <th
                                        colspan="1"
                                        width="50px"
                                    >
                                        规格
                                    </th>
                                    <th colspan="1">{{ item.specs ? item.specs : '暂无' }}</th>
                                    <th
                                        colspan="1"
                                        width="50px"
                                    >
                                        重量
                                    </th>
                                    <th colspan="1">{{ item.weight ? item.weight : '暂无' }}</th>
                                    <th
                                        colspan="1"
                                        width="50px"
                                    >
                                        单位
                                    </th>
                                    <th colspan="1">
                                        {{ item.unit_title ? item.unit_title : '暂无' }}
                                    </th>
                                    <th
                                        colspan="1"
                                        width="50px"
                                    >
                                        日期
                                    </th>
                                    <th colspan="1">{{ item.created_at ? item.created_at : '暂无' }}</th>
                                </tr>
                                <tr class="firstHead">
                                    <th width="100px">序号</th>
                                    <th width="100px">工序名称</th>
                                    <th colspan="11">工序内容</th>
                                    <th>机床代号</th>
                                    <th>单件工时</th>
                                    <th>备注</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr
                                    v-for="(key, index) in item.crafts"
                                    :key="key.id"
                                >
                                    <td style="text-align: center">{{ index + 1 }}</td>
                                    <td style="text-align: center">{{ key.process_title }}</td>
                                    <td colspan="11">{{ key.process_content }}</td>
                                    <td style="text-align: center">{{ key.tool_code }}</td>
                                    <td style="text-align: center">{{ key.unit_hour }}</td>
                                    <td style="text-align: center">{{ key.desc }}</td>
                                </tr>
                            </tbody>
                        </table>
                        <div style="page-break-after: always"></div>
                    </div>
                    <el-empty description="暂无内容" v-if="gonyi.length==0"></el-empty>
                </div>
            </el-tabs>
        </el-card>
    </el-main>
    <el-dialog
        v-model="dialogFormVisible"
        title="打印编辑页"
    >
        <div
            class="tabldialog"
            v-for="item in gonyitow"
            :key="item"
        >
            <table
                border="1"
                width="100%"
                @dblclick="double"
            >
                <thead>
                    <tr class="firstHead">
                        <th
                            colspan="2"
                            rowspan="2"
                            width="5%"
                        >
                            <img
                                class="logo_cass"
                                src="http://files.qixuw.com/dingb9614df94342f570a1320dcb25e91351/canyin/2023-09-26/Imi4donswOIJl7FjWGrhSRhdFuxVDJqG.png"
                                alt=""
                            />
                        </th>
                        <th
                            colspan="2"
                            rowspan="2"
                        >
                            工艺卡
                        </th>
                        <th colspan="2">编码</th>
                        <th colspan="1">{{ item.number ? item.number : '暂无' }}</th>
                        <th
                            colspan="1"
                            width="50px"
                        >
                            图号
                        </th>
                        <th colspan="3">
                            {{ item.drawing_number ? item.drawing_number : '暂无' }}
                        </th>
                        <th
                            colspan="1"
                            width="50px"
                        >
                            版本
                        </th>
                        <th
                            colspan="1"
                            width="50px"
                        >
                            {{ item.craft_ver ? item.craft_ver : '暂无' }}
                        </th>
                        <th
                            colspan="1"
                            width="50px"
                        >
                            编制
                        </th>
                        <th colspan="1">
                            {{ item.craft_user_name ? item.craft_user_name : '暂无' }}
                        </th>
                        <th
                            colspan="1"
                            rowspan="3"
                        >
                            <img
                                src="http://files.qixuw.com/dingb9614df94342f570a1320dcb25e91351/canyin/2023-09-26/gvL6klYjqyy0G9nWQN8ZDLeW8318TJHX.jpg"
                                alt=""
                                class="QRcode"
                            />
                        </th>
                    </tr>
                    <tr class="firstHead">
                        <th
                            colspan="2"
                            width="50px"
                        >
                            材料名称
                        </th>
                        <th colspan="1">{{ item.title ? item.title : '暂无' }}</th>
                        <th colspan="1">名称</th>
                        <th colspan="3">{{ item.title ? item.title : '暂无' }}</th>
                        <th colspan="1">版次</th>
                        <th colspan="1">
                            {{ item.craft_small_ver ? item.craft_small_ver : '暂无' }}
                        </th>
                        <th colspan="1">校对</th>
                        <th colspan="1">暂无</th>
                    </tr>
                    <tr class="firstHead">
                        <th colspan="2">材料类型</th>
                        <th width="50px">
                            {{
                                item.type == 0
                                    ? '产品'
                                    : item.type == 1
                                    ? '部件'
                                    : item.type == 2
                                    ? '零件'
                                    : item.type == 3
                                    ? '原材料'
                                    : '暂无'
                            }}
                        </th>

                        <th
                            colspan="1"
                            width="50px"
                        >
                            材质
                        </th>
                        <th
                            colspan="3"
                            width="50px"
                        >
                            {{ item.texture_title ? item.texture_title : '暂无' }}
                        </th>
                        <th
                            colspan="1"
                            width="50px"
                        >
                            规格
                        </th>
                        <th colspan="1">{{ item.specs ? item.specs : '暂无' }}</th>
                        <th
                            colspan="1"
                            width="50px"
                        >
                            重量
                        </th>
                        <th colspan="1">{{ item.weight ? item.weight : '暂无' }}</th>
                        <th
                            colspan="1"
                            width="50px"
                        >
                            单位
                        </th>
                        <th colspan="1">
                            {{ item.unit_title ? item.unit_title : '暂无' }}
                        </th>
                        <th
                            colspan="1"
                            width="50px"
                        >
                            日期
                        </th>
                        <th colspan="1">{{ item.created_at ? item.created_at : '暂无' }}</th>
                    </tr>
                    <tr class="firstHead">
                        <th width="100px">序号</th>
                        <th width="100px">工序名称</th>
                        <th colspan="11">工序内容</th>
                        <th>机床代号</th>
                        <th>单件工时</th>
                        <th>备注</th>
                    </tr>
                </thead>
                <tbody>
                    <tr
                        v-for="(key, index) in item.crafts"
                        :key="key.id"
                    >
                        <td style="text-align: center">{{ index + 1 }}</td>
                        <td style="text-align: center">{{ key.process_title }}</td>
                        <td colspan="11">{{ key.process_content }}</td>
                        <td style="text-align: center">{{ key.tool_code }}</td>
                        <td style="text-align: center">{{ key.unit_hour }}</td>
                        <td style="text-align: center">{{ key.desc }}</td>
                    </tr>
                </tbody>
            </table>
            <el-button
                type="danger"
                plain
                style="margin-left: 20px"
                @click="delbutton(item)"
                >删除</el-button
            >
        </div>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="dialogFormVisible = false">取消</el-button>
                <el-button
                    type="primary"
                    @click="dialogFormok()"
                >
                    确认
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script>
import print from '@/utils/print'

export default {
    name: 'print',
    data() {
        return {
            Loading_log: false,
            gonyi: [],
            gonyitow: [],
            dialogTableVisible: false,
            dialogFormVisible: false,
            formLabelWidth: '140px'
        }
    },
    created() {
        this.post_add()
    },
    methods: {
        dialogFormClick() {
            this.dialogFormVisible = true
            this.gonyitow = this.gonyi
        },
        dialogFormok() {
            this.gonyi = this.gonyitow
            this.dialogFormVisible = false
        },
        delbutton(op) {
            let array = []
            this.gonyitow.forEach((item) => {
                if (item.id != op.id) {
                    array.push(item)
                }
            })
            this.gonyitow = array
        },
        //返回上一页
        goBack() {
            this.$router.go(-1)
        },
        print() {
            //直接传入REF或者querySelector
            print(this.$refs.printMain)
        },
        post_add() {
            this.Loading_log = true
            console.log(this.$route.query, 'this.$route.query')
            if (this.$route.query.type == 3 || this.$route.query.part_type == 1) {
                this.gonyi = []
                this.Loading_log = false
            } else {
                this.$HTTP.post('craft/get_batch_export', { material_number: this.$route.query.number }).then((res) => {
                    if (res.errcode != 0) {
                        this.Loading_log = false
                    } else {
                        console.log(res.result, 'result>>>')
                        // const filteredData = res.result.filter(item => item.type !== '原材料' && item.type !== '外购件');
                        // console.log(filteredData,'filteredData');
                        this.gonyi = res.result
                        this.Loading_log = false
                    }
                })
            }
        }
    }
}
</script>

<style scoped>
.tablelist {
    margin-bottom: 20px;
}
.tabldialog {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}
.printMain p {
    margin-top: 20px;
    color: #999;
}
.logo_cass {
    width: 110px;
    margin: 5px;
}
.QRcode {
    width: 100px;
    height: 100px;
    margin: 2px;
}
table {
    border-collapse: collapse;
}
</style>
