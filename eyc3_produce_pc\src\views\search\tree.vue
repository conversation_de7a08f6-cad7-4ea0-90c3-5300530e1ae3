<template>
	<el-container @contextmenu.prevent="openMenu" @click="hideMenu" v-loading="filesLoading">
		<el-aside style="width: 25%;" :style="{ width: asideWidth + 'px' }">
			<el-container>
				<el-header>
					<el-input placeholder="输入关键字" v-model="groupFilterText"></el-input> <el-button type="primary"
						style="margin-left: 10px;" @click="btnClick">查询</el-button>
				</el-header>
				<el-main class="nopadding" v-loading="pictLoading">
					<el-tree class="menu" :data="treeData" style="width: 100%" :allow-drop="handleAllowDrop" draggable
						node-key="id" highlight-current :props="defaultProps"
						@node-click="groupClick" @node-contextmenu="rightClick" :current-node-key="selectedNodeId" :load="loadNode" lazy>
						<!-- <el-tree
        class="menu"
        :data="treeData"
        style="width: 500%"
        :allow-drop="handleAllowDrop"
        draggable
            node-key="id"
            default-expand-all
            highlight-current
            :props="defaultProps"
            :load="loadNode"
            @node-drop="nodeDrop"
            @node-click="groupClick"
            @node-contextmenu="rightClick"
            @node-expand="onNodeExpand"
            :allow-drag="allowDrag"
        > -->
						<template #default="{ node, data }">
							<span class="custom-tree-node">
								<span>
									<el-icon size="16px" color="#409EFC" class="no-inherit">
										<el-icon-folder v-if="data.ctype == 'category'" />
										<el-icon v-if="data.ctype == 'product'" size="16px" color="#409EFC"
											class="no-inherit">
											<sc-icon-raw-material v-if="data.type == 3" />
											<el-icon size="16px" color="color" v-else-if="data.type == 2">
												<!-- 零件 -->
												<sc-icon-part />
											</el-icon>
											<el-icon size="16px" color="red" v-else-if="data.type == 1">
												<!-- 部件 -->
												<sc-icon-mponent />
											</el-icon>
											<el-icon-document v-else-if="data.ctype == 'product'" />
										</el-icon>
									</el-icon>
									<span v-if="data.ctype == 'category'"
										:class="{ 'custom-highlight': isSelectedNode(data.id) }">{{ node.label }}</span>
									<span v-if="data.ctype == 'product'">
										<el-tooltip :content="`${data.number} ${data.dosage ? '【' + data.dosage + '】' : ''
                                            }【${node.label}】${data.drawing_number ? '—' + data.drawing_number : ''}${data.specs ? '—' + data.specs : ''
                                            }${data.texture_title ? '—' + data.texture_title : ''
                                            }`" placement="right-start" effect="dark">
                                            <span :class="`${data.part_type == 1 ? 'text_or' : 'text_over'}`">{{
                                                data.number }}【{{ node.label }}】
                                                {{ data.dosage ? '【' + data.dosage + '】' : '' }}
                                                {{ data.specs ? '—' + data.specs : '' }}
                                                {{ data.drawing_number ? '—' + data.drawing_number : '' }}  {{ data.texture_title ? '—' + data.texture_title : '' }}</span>
                                        </el-tooltip>
									</span>
								</span>
							</span>
						</template>
					</el-tree>
					<div class="box-menu" v-if="menuVisible" :style="{ left: menu_left + 'px', top: menu_top + 'px' }">
						<div @click.stop="tree_add()">
							<el-button type="link" icon="el-icon-plus">新建</el-button>
						</div>
						<div v-if="subdata">
							<el-button type="link" icon="el-icon-circle-plus" @click.stop="searchRef()">添加</el-button>
						</div>
						<div v-if="subdata">
							<el-button type="link" icon="el-icon-document-copy" @click.stop="bomcopy()">复制</el-button>
						</div>
						<div>
							<el-button type="link" icon="el-icon-Finished" @click.stop="copyClick()">衍生</el-button>
						</div>
						<div>
							<el-button type="link" icon="el-icon-edit" @click.stop="dicEdit()">修改</el-button>
						</div>
						<div>
							<el-button type="link" icon="el-icon-search" @click.stop="backward()">反查</el-button>
						</div>
						<div>
							<el-button type="link" icon="el-icon-refresh-left" @click.stop="Refresh()">刷新</el-button>
						</div>
						<div>
							<el-button type="link" icon="el-icon-view" @click.stop="detection()">检测</el-button>
						</div>

						<div>
							<el-button type="link" icon="el-icon-tickets" @click.stop="costClick()">成本</el-button>
						</div>
						<div v-if="subdata">
							<el-button type="link" icon="el-icon-circle-close" @click.stop="Deactivate()"
								v-show="deactivate">停用</el-button>
						</div>
						<div>
							<el-button type="link" icon="el-icon-MessageBox" @click.stop="printClick()">打印</el-button>
						</div>
						<div v-if="subdata">
							<el-button type="link" icon="el-icon-Download" @click.stop="download(currentData)"
								v-show="downloadData">下载</el-button>
						</div>




						<div v-if="bomp">
							<el-button type="link" icon="el-icon-document-checked"
								@click.stop="bompaste()">粘贴</el-button>
						</div>
						<div>
							<!-- <el-button type="link" icon="el-icon-delete" @click.stop="dicDel()">删除</el-button> -->
						</div>
						<div @click.stop="uploadClick()" v-if="!subdata">
							<Upload v-if="upload" :label="upload.label" :templateUrl="upload.templateUrl"
								:columnData="upload.columnData" :url="upload.url" :maxSize="upload.maxSize"
								:accept="upload.accept" :filename="upload.filename"
								:dataType="{ type: 0, category_id: currentData.id }" :url_loading="upload.url_loading"
								@uploadload="uploadload">
							</Upload>

						</div>
					</div>
				</el-main>
			</el-container>
			<div class="resizer" @mousedown="startResizing"></div>
		</el-aside>

		<el-container>
			<el-main class="nopadding">
				<listitem @drawer="drawer"></listitem>
			</el-main>
		</el-container>


	</el-container>


	<!-- 产品添加弹窗 -->
	<el-drawer ref="drawerRef" v-model="dialog" :title="doing_title" :before-close="handleClose" direction="rtl"
		class="demo-drawer" size="70%">
		<div class="demo-drawer__content">
			<div class="formType">
				<el-form :model="form" label-position="top" :rules="rules" ref="form" :inline="true"
					class="demo-form-inline">
					<div class="title" style="width: 100%">
						基本属性
					</div>
					<el-form-item label="父级物料编号" :label-width="formLabelWidth">
						<el-input v-model="form.parent_number" placeholder="请输入内容" style="min-width: 300px"
							:disabled="true" />
					</el-form-item>
					<el-form-item label="标签" :label-width="formLabelWidth">
						<el-select v-model="form.tags" multiple filterable allow-create default-first-option
							collapse-tags collapse-tags-tooltip @visible-change="tagsgetData" :loading="tagsloading"
							placeholder="请选择标签" style="min-width: 300px">
							<el-option v-for="item in tagsArray" :key="item.id" :label="item.title"
								:value="item.title" />
						</el-select>
					</el-form-item>
					<el-form-item label="编号" :label-width="formLabelWidth" prop="number">
						<el-autocomplete class="inline-input" v-model="form.number" :fetch-suggestions="querySearch"
							placeholder="请输入内容" :trigger-on-focus="false" style="min-width: 300px" @select="(item) => {
								handleSelect(item)
							}
								" clearable>
							<template #default="{ item }">
								<div class="name">{{ item.title }}</div>
							</template>
						</el-autocomplete>
						<!-- <el-input v-model="form.number" placeholder="请输入内容" style="min-width: 300px;"/> -->
					</el-form-item>
					<el-form-item label="名称" :label-width="formLabelWidth" prop="title">
						<el-autocomplete class="inline-input" v-model="form.title" :fetch-suggestions="querySearch"
							placeholder="请输入内容" :trigger-on-focus="false" style="min-width: 300px" @select="(item) => {
								handleSelect(item)
							}
								" clearable>
							<template #default="{ item }">
								<div class="name">{{ item.title }}</div>
							</template>
						</el-autocomplete>
						<!-- <el-input v-model="form.title" placeholder="请输入内容" style="min-width: 300px;"/> -->
					</el-form-item>
					<el-form-item label="类型" :label-width="formLabelWidth" prop="type">
						<el-select v-model="form.type" placeholder="请选择" style="min-width: 300px">
							<el-option v-for="item in listType" :key="item.id" :label="item.title"
								:value="item.value" />
						</el-select>
					</el-form-item>
					<el-form-item label="图号" :label-width="formLabelWidth" prop="drawing_number">
						<el-autocomplete class="inline-input" v-model="form.drawing_number"
							:fetch-suggestions="querySearch" placeholder="请输入内容" :trigger-on-focus="false"
							style="min-width: 300px" @select="(item) => {
								handleSelect(item)
							}
								" clearable>
							<template #default="{ item }">
								<div class="name">{{ item.title }}</div>
							</template>
						</el-autocomplete>
						<!-- <el-input v-model="form.drawing_number" placeholder="请输入内容" style="min-width: 300px;"/> -->
					</el-form-item>
					<el-form-item label="规格" :label-width="formLabelWidth" prop="specs">
						<el-input v-model="form.specs" placeholder="请输入内容" style="min-width: 300px" />
					</el-form-item>
					<el-form-item label="单重" :label-width="formLabelWidth" prop="weight">
						<el-input v-model="form.weight" placeholder="请输入内容" style="min-width: 300px" />
					</el-form-item>
					<el-form-item label="单位" :label-width="formLabelWidth" prop="unit_title">
						<el-select v-model="form.unit_title" placeholder="请选择" style="min-width: 300px" value-key="id"
							filterable @visible-change="unit_getData" @change="selectUnit($event)">
							<el-option v-for="item in unitTitle" :key="item.id" :label="item.title"
								:value="item.title" />
						</el-select>
					</el-form-item>
					<el-form-item label="材质" :label-width="formLabelWidth" prop="texture_title">
						<el-select v-model="form.texture_title" placeholder="请选择" style="min-width: 300px"
							value-key="id" filterable @visible-change="getData" @change="selectget($event)">
							<el-option v-for="item in texture" :key="item.id" :label="item.title" :value="item.title" />
						</el-select>
					</el-form-item>
					<el-form-item label="分类" :label-width="formLabelWidth" v-if="categoryShow" prop="category_title">
						<el-select v-model="form.category_title" placeholder="请选择" style="min-width: 300px"
							value-key="id" filterable @visible-change="categorydata" @change="selectCategory($event)">
							<el-option v-for="item in categoryArray" :key="item.id" :label="item.title"
								:value="item.title" />
						</el-select>
					</el-form-item>
					<el-form-item label="零件类型" :label-width="formLabelWidth" prop="part_type_title">
						<el-select v-model="form.part_type_title" placeholder="请选择" style="min-width: 300px"
							value-key="id" filterable @visible-change="partType_title" @change="partType($event)">
							<el-option v-for="item in partTypeArray" :key="item.id" :label="item.title"
								:value="item.title" />
						</el-select>
					</el-form-item>
					<div class="title" style="width: 100%">
						财务信息
					</div>
					<el-form-item label="计划价" :label-width="formLabelWidth" prop="price">
						<el-input v-model="form.price" placeholder="请输入内容" style="min-width: 300px" />
					</el-form-item>
					<div class="title" style="width: 100%">
						工艺信息
					</div>
					<el-form-item label="材料定额" :label-width="formLabelWidth" prop="quota">
						<el-input v-model="form.quota" placeholder="请输入内容" style="min-width: 300px" />
					</el-form-item>
					<el-form-item label="BOM量" :label-width="formLabelWidth" v-if="dosaShow" prop="dosage">
						<el-input v-model="form.dosage" placeholder="请输入内容" style="min-width: 300px" />
					</el-form-item>

					<div class="demo-drawer__footer">
						<el-button @click="cancelForm">关 闭</el-button>
						<el-button type="primary" @click="matpost_data('form')" :loading="loading">{{ loading ? '提交中...' :
							'确 定' }}</el-button>
					</div>
				</el-form>
			</div>
		</div>
	</el-drawer>
	<!-- 物料修改添加弹窗 -->
	<el-drawer ref="drawerRef" :title="materialTitle" v-model="materialDialog" :before-close="materialHandleClose"
		direction="rtl" class="demo-drawer" size="70%">
		<div class="demo-drawer__content">
			<div class="formType">
				<el-form :model="materialform" label-position="top" :rules="rules" ref="form" :inline="true"
					class="demo-form-inline">
					<div class="title" style="width: 100%">
						基本属性
					</div>
					<el-form-item label="父级物料编号" :label-width="formLabelWidth">
						<el-input v-model="materialform.parent_material_number" placeholder="请输入内容"
							style="min-width: 300px" :disabled="true" />
					</el-form-item>
					<el-form-item label="标签" :label-width="formLabelWidth">
						<el-select v-model="materialform.tags" multiple filterable allow-create default-first-option
							collapse-tags collapse-tags-tooltip @visible-change="tagsgetData" :loading="tagsloading"
							placeholder="请选择标签" style="min-width: 300px">
							<el-option v-for="item in tagsArray" :key="item.id" :label="item.title"
								:value="item.title" />
						</el-select>
					</el-form-item>
					<el-form-item label="编号" :label-width="formLabelWidth" prop="material_number">
						<el-autocomplete class="inline-input" v-model="materialform.material_number"
							:fetch-suggestions="querySearch" placeholder="请输入内容" :trigger-on-focus="false"
							style="min-width: 300px" @select="(item) => {
								handleSelect(item)
							}
								" clearable>
							<template #default="{ item }">
								<div class="name">{{ item.title }}</div>
							</template>
						</el-autocomplete>
						<!-- <el-input v-model="form.number" placeholder="请输入内容" style="min-width: 300px;"/> -->
					</el-form-item>
					<el-form-item label="名称" :label-width="formLabelWidth" prop="title">
						<el-autocomplete class="inline-input" v-model="materialform.title"
							:fetch-suggestions="querySearch" placeholder="请输入内容" :trigger-on-focus="false"
							style="min-width: 300px" @select="(item) => {
								handleSelect(item)
							}
								" clearable>
							<template #default="{ item }">
								<div class="name">{{ item.title }}</div>
							</template>
						</el-autocomplete>
						<!-- <el-input v-model="form.title" placeholder="请输入内容" style="min-width: 300px;"/> -->
					</el-form-item>
					<el-form-item label="类型" :label-width="formLabelWidth" prop="type">
						<el-select v-model="materialform.type" placeholder="请选择" style="min-width: 300px">
							<el-option v-for="item in listType" :key="item.id" :label="item.title"
								:value="item.value" />
						</el-select>
					</el-form-item>
					<el-form-item label="图号" :label-width="formLabelWidth" prop="drawing_number">
						<el-autocomplete class="inline-input" v-model="materialform.drawing_number"
							:fetch-suggestions="querySearch" placeholder="请输入内容" :trigger-on-focus="false"
							style="min-width: 300px" @select="(item) => {
								handleSelect(item)
							}
								" clearable>
							<template #default="{ item }">
								<div class="name">{{ item.title }}</div>
							</template>
						</el-autocomplete>
						<!-- <el-input v-model="form.drawing_number" placeholder="请输入内容" style="min-width: 300px;"/> -->
					</el-form-item>
					<el-form-item label="规格" :label-width="formLabelWidth" prop="specs">
						<el-input v-model="materialform.specs" placeholder="请输入内容" style="min-width: 300px" />
					</el-form-item>
					<el-form-item label="单重" :label-width="formLabelWidth" prop="weight">
						<el-input v-model="materialform.weight" placeholder="请输入内容" style="min-width: 300px" />
					</el-form-item>
					<!-- <el-form-item label="单位" :label-width="formLabelWidth" prop="unit_title">
						<el-select v-model="materialform.unit_title" placeholder="请选择" style="min-width: 300px"
							value-key="id" filterable @visible-change="unit_getData" @change="selectUnit($event)" :disabled="unitData">
							<el-option v-for="item in unitTitle" :key="item.id" :label="item.title"
								:value="item.title" />
						</el-select>
					</el-form-item> -->
					<el-form-item
                    v-if="materialTitle=='添加物料'"
                        label="单位"
                        :label-width="formLabelWidth"
                        prop="unit_title"
                    >

                        <el-select
                            v-model="materialform.unit_title"
                            placeholder="请选择"
                            style="min-width: 300px"
                            value-key="id"
                            filterable
                            @visible-change="unit_getData"
                            @change="selectUnit($event)"
                        >
                            <el-option
                                v-for="item in unitTitle"
                                :key="item.id"
                                :label="item.title"
                                :value="item.title"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item
                      v-else
                        label="单位"
                        :label-width="formLabelWidth"
                        prop="unit_title"
                    >

                        <el-select
                            v-model="materialform.unit_title"
                            placeholder="请选择"
                            style="min-width: 300px"
                            value-key="id"
                            filterable
                            @visible-change="unit_getData"
                            @change="selectUnit($event)"
                            :disabled="unitData"
                        >
                            <el-option
                                v-for="item in unitTitle"
                                :key="item.id"
                                :label="item.title"
                                :value="item.title"
                            />
                        </el-select>
                    </el-form-item>
					<el-form-item label="材质" :label-width="formLabelWidth" prop="texture_title">
						<el-select v-model="materialform.texture_title" placeholder="请选择" style="min-width: 300px"
							value-key="id" filterable @visible-change="getData" @change="selectget($event)">
							<el-option v-for="item in texture" :key="item.id" :label="item.title" :value="item.title" />
						</el-select>
					</el-form-item>
					<el-form-item label="分类" :label-width="formLabelWidth" v-if="categoryShow" prop="category_title">
						<el-select v-model="materialform.category_title" placeholder="请选择" style="min-width: 300px"
							value-key="id" filterable @visible-change="categorydata" @change="selectCategory($event)">
							<el-option v-for="item in categoryArray" :key="item.id" :label="item.title"
								:value="item.title" />
						</el-select>
					</el-form-item>
					<el-form-item label="零件类型" :label-width="formLabelWidth" prop="part_type_title">
						<el-select v-model="materialform.part_type_title" placeholder="请选择" style="min-width: 300px"
							value-key="id" filterable @visible-change="partType_title" @change="partType($event)">
							<el-option v-for="item in partTypeArray" :key="item.id" :label="item.title"
								:value="item.title" />
						</el-select>
					</el-form-item>
					<div class="title" style="width: 100%">
						财务信息
					</div>
					<el-form-item label="计划价" :label-width="formLabelWidth" prop="price">
						<el-input v-model="materialform.price" placeholder="请输入内容" style="min-width: 300px" />
					</el-form-item>
					<div class="title" style="width: 100%">
						工艺信息
					</div>
					<el-form-item label="材料定额" :label-width="formLabelWidth" prop="quota">
						<el-input v-model="materialform.quota" placeholder="请输入内容" style="min-width: 300px" />
					</el-form-item>
					<el-form-item label="BOM量" :label-width="formLabelWidth" v-if="dosaShow" prop="dosage">
						<el-input v-model="materialform.dosage" placeholder="请输入内容" style="min-width: 300px" />
					</el-form-item>

					<div class="demo-drawer__footer">
						<el-button @click="materialCelForm">关 闭</el-button>
						<el-button type="primary" @click="material_data('form')" :loading="materialLoading">{{
							materialLoading ? '提交中 ...' : '确 定' }}</el-button>
					</div>
				</el-form>
			</div>
		</div>
	</el-drawer>
	<!-- 衍生弹窗 -->
	<el-drawer ref="drawerRef" v-model="copydialog" title="BOM复制基础信息" :before-close="copyhandleClose" direction="rtl"
		class="demo-drawer" size="70%">
		<div class="demo-drawer__content">
			<div class="formType">
				<el-form :model="copyform" label-position="top" :rules="rules" ref="form" :inline="true"
					class="demo-form-inline">
					<div class="title" style="width: 100%">
						基本属性
					</div>
					<el-form-item label="源物料编号" :label-width="formLabelWidth">
						<el-input v-model="copyform.sou_number" placeholder="请输入内容" style="min-width: 300px"
							:disabled="true" />
					</el-form-item>
					<el-form-item label="编号" :label-width="formLabelWidth" prop="number">
						<el-input v-model="copyform.number" placeholder="请输入内容" style="min-width: 300px" />
					</el-form-item>
					<el-form-item label="名称" :label-width="formLabelWidth" prop="title">
						<el-input v-model="copyform.title" placeholder="请输入内容" style="min-width: 300px" />
					</el-form-item>
					<el-form-item label="类型" :label-width="formLabelWidth" prop="type">
						<el-select v-model="copyform.type" placeholder="请选择" style="min-width: 300px">
							<el-option v-for="item in listType" :key="item.id" :label="item.title"
								:value="item.value" />
						</el-select>
					</el-form-item>
					<el-form-item label="图号" :label-width="formLabelWidth" prop="drawing_number">
						<el-input v-model="copyform.drawing_number" placeholder="请输入内容" style="min-width: 300px" />
					</el-form-item>
					<el-form-item label="规格" :label-width="formLabelWidth" prop="specs">
						<el-input v-model="copyform.specs" placeholder="请输入内容" style="min-width: 300px" />
					</el-form-item>
					<el-form-item label="单重" :label-width="formLabelWidth" prop="weight">
						<el-input v-model="copyform.weight" placeholder="请输入内容" style="min-width: 300px" />
					</el-form-item>
					<el-form-item label="单位" :label-width="formLabelWidth" prop="unit_title">
						<el-select v-model="copyform.unit_title" placeholder="请选择" style="min-width: 300px"
							value-key="id" filterable @visible-change="unit_getData" @change="selectUnit($event)">
							<el-option v-for="item in unitTitle" :key="item.id" :label="item.title"
								:value="item.title" />
						</el-select>
					</el-form-item>
					<el-form-item label="材质" :label-width="formLabelWidth" prop="texture_title">
						<el-select v-model="copyform.texture_title" placeholder="请选择" style="min-width: 300px"
							value-key="id" filterable @visible-change="getData" @change="selectget($event)">
							<el-option v-for="item in texture" :key="item.id" :label="item.title" :value="item.title" />
						</el-select>
					</el-form-item>
					<el-form-item label="分类" :label-width="formLabelWidth" v-if="categoryShow" prop="category_title">
						<el-select v-model="copyform.category_title" placeholder="请选择" style="min-width: 300px"
							value-key="id" filterable @visible-change="categorydata" @change="selectCategory($event)">
							<el-option v-for="item in categoryArray" :key="item.id" :label="item.title"
								:value="item.title" />
						</el-select>
					</el-form-item>
					<el-form-item label="零件类型" :label-width="formLabelWidth" prop="part_type_title">
						<el-select v-model="copyform.part_type_title" placeholder="请选择" style="min-width: 300px"
							value-key="id" filterable @visible-change="partType_title" @change="partType($event)">
							<el-option v-for="item in partTypeArray" :key="item.id" :label="item.title"
								:value="item.title" />
						</el-select>
					</el-form-item>
					<div class="title" style="width: 100%">
						财务信息
					</div>
					<el-form-item label="计划价" :label-width="formLabelWidth" prop="price">
						<el-input v-model="copyform.price" placeholder="请输入内容" style="min-width: 300px" />
					</el-form-item>
					<div class="title" style="width: 100%">
						工艺信息
					</div>
					<el-form-item label="材料定额" :label-width="formLabelWidth" prop="quota">
						<el-input v-model="copyform.quota" placeholder="请输入内容" style="min-width: 300px" />
					</el-form-item>
					<el-form-item label="BOM量" :label-width="formLabelWidth" v-if="dosaShow" prop="dosage">
						<el-input v-model="copyform.dosage" placeholder="请输入内容" style="min-width: 300px" />
					</el-form-item>

					<div class="demo-drawer__footer">
						<el-button @click="copycancelForm">关 闭</el-button>
						<el-button type="primary" @click="copyonclickAdd('form')" :loading="copyloading">{{ copyloading
							?
							'提交中 ...' : '确 定' }}</el-button>
					</div>
				</el-form>
			</div>
		</div>
	</el-drawer>
	<!-- 复制用量弹窗 -->
	<el-dialog v-model="dialogVisible" title="复制物料用量" width="30%" :before-close="handleClose">
		<el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm"
			@submit.native.prevent>
			<el-form-item label="用量：" prop="dosage">
				<el-input placeholder="请输入用量" v-model="ruleForm.dosage"></el-input>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="submitForm('ruleForm')">提交</el-button>
				<el-button @click="dialogVisible = false">关闭</el-button>
			</el-form-item>
		</el-form>
	</el-dialog>
	<!--全页面右键弹窗-->
	<div class="box-menu" v-show="menuVisibletop" :style="{ left: menu_left + 'px', top: menu_top + 'px' }">
		<Upload v-if="upload" :label="upload.label" :templateUrl="upload.templateUrl" :columnData="upload.columnData"
			:url="upload.url" :maxSize="upload.maxSize" :accept="upload.accept" :filename="upload.filename"
			:dataType="upload.dataType" :url_loading="upload.url_loading" @uploadload="uploadload"></Upload>
		<!-- :templateUrl="pdmupload.templateUrl" -->
		<Upload v-if="pdmupload" :label="pdmupload.label" :columnData="pdmupload.columnData" :url="pdmupload.url"
			:maxSize="pdmupload.maxSize" :dataType="pdmupload.dataType" :accept="pdmupload.accept"
			:filename="pdmupload.filename" :url_loading="pdmupload.url_loading" @uploadload="uploadload"></Upload>
		<pdfupload @success="success" :postDataObj='postDataObj'></pdfupload>
	</div>
	<!-- 弹出框搜索 -->
	<el-dialog v-model="DialogsearchFor" title="BOM搜索" width="75%">
		<div class="right-panel-search">
			<el-form inline>
				<el-form-item label="物料名称">
					<el-input v-model="Dialogsearch.title" placeholder="物料名称" clearable></el-input>
				</el-form-item>
				<el-form-item label="规格">
					<el-input v-model="Dialogsearch.specs" placeholder="规格" clearable></el-input>
				</el-form-item>
				<el-form-item label="图号">
					<el-input v-model="Dialogsearch.drawing_number" placeholder="图号" clearable></el-input>
				</el-form-item>
				<el-form-item label="物料编码">
					<el-input v-model="Dialogsearch.number" placeholder="物料编码" clearable></el-input>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" icon="el-icon-search" @click="Query_button"></el-button>
				</el-form-item>
			</el-form>
		</div>
		<el-table :data="gridData" v-loading="DialogsearchLoading" @row-click="handleRowClick">
			<el-table-column type="index" label="序号" align="center" width="50">
			</el-table-column>
			<el-table-column width="50" label="图标" #default="scope">
				<el-icon size="16px" color="color" v-if="scope.row.type == 3"> <!-- 原材料 -->
					<sc-icon-raw-material />
				</el-icon>
				<el-icon size="16px" color="color" v-if="scope.row.type == 0"> <!-- 产品 -->
					<sc-icon-product />
				</el-icon>
				<el-icon size="16px" color="color" v-if="scope.row.type == 2"> <!-- 零件 -->
					<sc-icon-part />
				</el-icon>
				<el-icon size="16px" color="red" v-if="scope.row.type == 1"> <!-- 部件 -->
					<sc-icon-mponent />
				</el-icon>
			</el-table-column>
			<el-table-column label="ID" min-width="80" align="center" sortable>
				<template #default="scope">
					<span>{{ scope.row.id }}</span>
				</template>
			</el-table-column>
			<el-table-column prop="number" label="代号" min-width="80" align="center" sortable>
				<template #default="scope">
					<span>{{ scope.row.number }}</span>
				</template>
			</el-table-column>
			<el-table-column prop="title" label="名称" min-width="80" align="center" sortable>
				<template #default="scope">
					<span>{{ scope.row.title }}</span>
				</template>
			</el-table-column>
			<el-table-column prop="drawing_number" label="图号" min-width="80" align="center" sortable>
				<template #default="scope">
					<span>{{ scope.row.drawing_number }}</span>
				</template>
			</el-table-column>
			<el-table-column prop="specs" label="规格" min-width="80" align="center" sortable>
				<template #default="scope">
					<span>{{ scope.row.specs }}</span>
				</template>
			</el-table-column>
			<el-table-column prop="texture_title" label="材质" min-width="80" align="center" sortable>
				<template #default="scope">
					<span>{{ scope.row.texture_title }}</span>
				</template>
			</el-table-column>
		</el-table>
	</el-dialog>
</template>

<script>
// import scSelectFilter from '@/components/scSelectFilter'
// import scContextmenu from '@/components/scContextmenu'
// import scContextmenuItem from '@/components/scContextmenu/item'
import listitem from '../product/list.vue'
// import Upload from './components/upload'
// import Versin_query from './versinQuery.vue'
// import pdfupload from './components/pdfupload.vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import tool from '@/utils/tool'
import JSZip from 'jszip';
import { saveAs } from 'file-saver';
// import { number } from 'echarts'
export default {
	name: 'listTree',
	components: {
		// Versin_query,
		listitem,
		// scSelectFilter,
		// scContextmenu,
		// scContextmenuItem,
		// Upload,
		// pdfupload
	},
	data() {
		return {
			asideWidth: 500, // 初始宽度  
			isResizing: false,
			startX: 0,
			startWidth: 0,
			treeData: [],
			Select: null,
			DisplaySelect: null,
			Display: null,
			targetNodeId: '',
			displayAll: false,
			selectedNodeId: null,
			DialogsearchLoading: false,
			Dialogsearch: {
				title: "",//零件类型
				number: "",//物料编码
				specs: "",//规格
				drawing_number: "",//图号
			},
			downloadData: null,
			deactivate: null,
			DialogsearchFor: false,
			bomp: false,
			bomtype: '0',
			filesLoading: false,
			menuVisibletop: false,
			// 复制弹窗
			dialogVisible: false,
			ruleForm: {
				dosage: '',
				material_number: '',
				parent_material_number: ''
			},
			//物料弹窗
			materialDialog: false,
			materialLoading: false,
			// 物料form信息
			materialform: {
				parent_material_number: '',
				material_number: '',
				title: '',
				type: '',
				drawing_number: '',
				specs: '',
				weight: '',
				unit_title: '',
				unit_id: '',
				texture_id: '',
				texture_title: '',
				price: '',
				quota: '',
				part_type_id: '',
				part_type_title: '',
				dosage: '',
				category_title: '',
				category_id: ''
			},
			// 树形参数配置
			defaultProps: {
				children: 'children',
				label: 'title'
			},
			// 复制
			copydialog: false,
			copyloading: false,
			// 复制form信息
			copyform: {
				parent_number: '',
				number: '',
				title: '',
				type: '',
				drawing_number: '',
				specs: '',
				weight: '',
				unit_title: '',
				unit_id: '',
				texture_id: '',
				texture_title: '',
				price: '',
				quota: '',
				part_type_id: '',
				part_type_title: '',
				dosage: '',
				category_title: '',
				category_id: ''
			},
			// 树形上方搜索
			post_page: {
				keyword: ''
			},
			// 导入
			upload: {
				label: 'BOM导入',
				component: 'upload',
				url: 'material/post_import',
				url_loading: 'material/post_check_info',
				templateUrl: 'material/get_template',
				accept: '',
				dataType: {
					type: 0
				},
				maxSize: 500,
			},
			pdmupload: {
				label: 'pdm导入',
				component: 'upload',
				url_loading: 'material/post_check_info',
				url: 'material/get_pdm_file',
				templateUrl: 'material/get_template',
				dataType: {
					type: 1
				},
				accept: '',
				maxSize: 500
			},
			// 鼠标右键
			pictLoading: false,
			subdata: false,
			menuVisible: false,
			menu_left: 0,
			menu_top: 0,

			defaultProps: {
				children: 'children',
				label: 'title'
			},

			groupFilterText: '',
			productAll: [],
			getAll: [],
			// 树形下拉
			groupData: [],
			tableData: [],
			search: {
				keyword: ''
			},
			groupId: '0',
			// 产品弹出框状态
			dialog: false,
			loading: false,
			form: {
				parent_number: '',
				number: '',
				title: '',
				type: '',
				drawing_number: '',
				specs: '',
				weight: '',
				unit_title: '',
				unit_id: '',
				texture_id: '',
				texture_title: '',
				price: '',
				quota: '',
				part_type_id: '',
				part_type_title: '',
				dosage: '',
				category_title: '',
				category_id: '',
				tags: ''
			},
			rules: {
				title: [{ required: true, message: '请输入名称', trigger: 'blur' }],
				type: [{ required: true, message: '请选择类型', trigger: 'change' }],
				number: [{ required: true, message: '请输入编号', trigger: 'blur' }],
				material_number: [{ required: true, message: '请输入编号', trigger: 'blur' }],
				unit_title: [{ required: true, message: '请选择单位', trigger: 'change' }],
				texture_title: [{ required: true, message: '请选择材质', trigger: 'change' }],
				category_title: [{ required: false, message: '请选择分类', trigger: 'change' }],
				drawing_number: [{ required: true, message: '请输入图号', trigger: 'blur' }],
				specs: [{ required: true, message: '请输入规格', trigger: 'blur' }],
				// weight: [{ required: true, message: '请输入单重', trigger: 'blur' }],
				// price: [{ required: true, message: '请输入计划价', trigger: 'blur' }],
				// quota: [{ required: true, message: '请输入材料定额', trigger: 'blur' }],
				dosage: [{ required: true, message: '请输入BOM量', trigger: 'blur' }],
				part_type_title: [{ required: true, message: '请选择零件类型', trigger: 'change' }],
			},
			formLabelWidth: '80px',
			timer: null,
			// 单位
			unitTitle: [],
			// 材质
			texture: [],
			// 零件类型
			partTypeArray: [],
			// BOM量显示
			dosaShow: false,
			// 分类数据
			categoryArray: [],
			// 分类显示
			categoryShow: false,
			// 弹出框title
			doing_title: '添加产品',
			//类型
			listType: [],
			currentData: {},
			// 添加修改接口
			posturl: '',
			treeArray: null,
			// 标签
			tagsloading: false,
			tagsArray: [],
			tagstitle: [],
			editForm: {
				node: null, // 存储被点击的节点
				add: null,//存储添加的数据
			},
			dataAll: null,
			unitData:true
		}
	},
	watch: {
		// 监听左侧树输入筛选
		groupFilterText(val) {
			if (val) {
				// this.$HTTP.post('material/get_search', { keyword: val }).then((res) => {
				// 	if (res.errcode != 0) {
				// 		this.loading = false
				// 		ElMessage.error(res.errmsg)
				// 	} else {
				// 		let resItem = res.result
				// 		console.log(resItem,'====================================');
				// 		resItem.map((item) => {
				// 			if (item.son == 1) {
				// 				item.leaf = false
				// 			} else {
				// 				item.leaf = true
				// 			}
				// 		})
				// 		this.groupData = resItem
				// 	}
				// })
			} else {
				this.groupData = []
				this.pictLoading = true
				this.$HTTP.post('material/get_category_product_all').then((res) => {
					let resItem = res.result
					resItem.map((item) => {
						if (item.son == 1) {
							item.leaf = false
						} else {
							item.leaf = true
						}
					})
					this.pictLoading = false
					this.groupData = resItem
				})
			}
		},
	},
	created() {
		if (this.$store.state.listObj_bom) {
			console.log(this.$store.state.listObj_bom, 'this.$store.state.listObj_bom');
			this.targetNodeId = this.$store.state.listObj_bom.items.number
			const gridDataAll=[this.$store.state.listObj_bom.items]
			// console.log(gridDataAll,'gridDataAll');
			this.$HTTP
				.post('bom/get_parent_material', {
					material_number: this.targetNodeId
				})
				.then((res) => {
					let myArray = gridDataAll.map((item) => ({
                    ...item,
                    children: res.result
                }))
					this.treeData = this.convertToTree(myArray)
				})
		}
	},
	methods: {
		startResizing(event) {
			this.isResizing = true;
			this.startX = event.clientX;
			this.startWidth = this.asideWidth;

			document.addEventListener('mousemove', this.resizeAside);
			document.addEventListener('mouseup', this.stopResizing);
		},
		resizeAside(event) {
			if (this.isResizing) {
				const newWidth = this.startWidth + (event.clientX - this.startX);
				this.asideWidth = newWidth > 50 ? newWidth : 50; // 设置最小宽度为 50px  
			}
		},
		stopResizing() {
			this.isResizing = false;
			document.removeEventListener('mousemove', this.resizeAside);
			document.removeEventListener('mouseup', this.stopResizing);
		},
		//点击查询筛选
		btnClick() {
			if (this.groupFilterText) {
				this.$HTTP.post('material/get_search', { keyword: this.groupFilterText }).then((res) => {
					if (res.errcode != 0) {
						this.loading = false
						ElMessage.error(res.errmsg)
					} else {
						let resItem = res.result
						console.log(resItem, '====================================');
						resItem.map((item) => {
							if (item.son == 1) {
								item.leaf = false
							} else {
								item.leaf = true
							}
						})
						this.groupData = resItem
					}
				})
			} else {
				this.groupData = []
				this.pictLoading = true
				this.$HTTP.post('material/get_category_product_all').then((res) => {
					let resItem = res.result
					resItem.map((item) => {
						if (item.son == 1) {
							item.leaf = false
						} else {
							item.leaf = true
						}
					})
					this.pictLoading = false
					this.groupData = resItem
				})
			}
		},

		convertToTree(data) {
			return data.map((item) => ({
				...item,
				label: item.title, // 使用title作为显示的标签
				children: item.children
					? item.children.map((child) => ({
						...child,
						label: child.title, // 使用title作为显示的标签
						// 如果child还有children，则递归转换
						children: child.children
							? child.children.map((grandchild) => ({
								...grandchild,
								label: grandchild.title, // 使用title作为显示的标签
								// 如果grandchild是叶子节点，则不再递归
								children: grandchild.leaf ? undefined : grandchild.children
							}))
							: undefined
					}))
					: undefined
			}))
		},
		// 查询最初文件夹
		folderNodeAll(data, checkArray) {
			console.log(checkArray, 'checkArray');
			this.$HTTP
				.post('category/get_parent_category', {
					// number: this.targetNodeId
					id: data
				})
				.then((res) => {
					console.log(res.result, '是最后的文件夹吗？111111111111111111111')
					let folderCheck = res.result.map((item) => ({
						...item,
						children: checkArray
					}))
					console.log(folderCheck, 'folderCheck最后的数据');
					if (res.result.length == 0) {
						this.treeData = this.convertToTree(checkArray)
						console.log('结束反查')
						return
					} else {
						this.folderNodeAll(folderCheck[0].id, folderCheck)
					}
					// this.treeData = this.convertToTree(myArray)
				})
		},
		// 全局导入右键弹窗
		openMenu(event, object) {
			if (!this.$store.state.menuVisibletop) {
				const windowHeight = window.innerHeight;
				const windowWidth = window.innerWidth;
				// 判断鼠标点击的位置是否接近窗口底部
				const isNearBottom = event.clientY > windowHeight - 150; // 这里的100是可调整的阈值
				const isNearBottomwidth = event.clientX > windowWidth - 180; // 这里的100是可调整的阈值
				// console.log(isNearBottomwidth,event.clientX,windowWidth)
				if (isNearBottom) {
					// console.log('鼠标点击的位置接近窗口底部');
					// 将菜单显示在鼠标点击旁边定位
					this.menu_left = event.clientX + 50
					this.menu_top = event.clientY - 150
					// 在这里显示你的弹窗，并将它定位到靠近窗口底部的位置
				} else {
					// console.log('鼠标点击的位置不是接近窗口底部');
					// 在这里显示你的弹窗，并将它定位到其他合适的位置将菜单显示在鼠标点击旁边定位
					this.menu_left = event.clientX + 50
					this.menu_top = event.clientY - 0
				}
				if (isNearBottomwidth) {
					this.menu_left = event.clientX - 150
					this.menu_top = event.clientY - 0
				}
				event.preventDefault();
				this.menuVisibletop = true
				this.menuVisible = false
				// this.menu_left = event.clientX
				// this.menu_top = event.clientY - 0
			}
		},
		// 取消右键全局弹窗
		hideMenu() {
			this.menuVisibletop = false;
			this.$store.state.menuVisibletop = false
		},
		drawer(op) {
			if (!op) {
				this.menuVisibletop = op;
				this.$store.state.menuVisibletop = false
			}
		},
		allowDrag(node) {
			// console.log(node,'1111111111111111111')
			// 只允许第一层节点拖拽
			return node.level === 1;
		},
		// 检测拖拽类型
		handleAllowDrop(draggingNode, dropNode, dropType) {
			if (
				(draggingNode.level !== dropNode.level) ||
				(draggingNode.parent === dropNode) || (draggingNode.parent !== dropNode.parent)
			) {
				return false; // 禁止拖动
			} else {
				if (dropType === 'inner') {
					return false; // 返回false以阻止拖拽操作
				} else {
					return true; // 返回true以允许拖拽操作
				}
			}
		},
		//树拖拽
		nodeDrop(draggingNode, dropNode, dropType) {
			// 其他处理代码...
			this.$HTTP.post('category/post_move_position', { sou: draggingNode.data.id, obj: dropNode.data.id, type: dropType }).then((res) => {
				if (res.errcode != 0) {
					ElMessage.error(res.errmsg)
				} else {
					this.treeList('material/get_category_product_all')
					this.$notify({
						title: '位置移动',
						message: '操作成功 ',
						type: 'success',
						duration: 2000
					})
				}
			})
			// console.log('移动的数据:', draggingNode.data);
			// console.log('释放的数据:', dropNode.data);
			// console.log('释放数据的位置:', dropType);
			// this.$refs.save.setData({})
			// this.$message(`拖拽对象：${draggingNode.data.meta.title}, 释放对象：${dropNode.data.meta.title}, 释放对象的位置：${dropType}`)
		},
		// 左侧树下拉数据
		    // 左侧树下拉数据
			loadNode(node, resolve) {
            console.log(node.data, '下拉数据this.$refs.tree')
            if(node.data.ctype=='category'){
                // alert('1111111111')
                this.$HTTP
                .post('category/get_parent_category', {
                    // number: this.targetNodeId
                    id: node.data.id
                })
                .then((res) => {
                    let resItem = res.result
                    resItem.map((item) => {
                        if (item.son == 1) {
                            item.leaf = false
                        } else {
                            item.leaf = true
                        }
                    })
                    console.log(resItem, 'resItem11111111文件夹');
                    resItem.map((item) => {
                        if (item.son == 1) {
                            item.leaf = false
                        } else {
                            item.leaf = true
                        }
                    })
                     for (let i = 0; i < this.treeData.length; i++) {
                         if (this.treeData[i].id === node.data.id) {
                             this.treeData[i].children = resItem
                             break
                         }
                     }
                     this.$nextTick(() => {
                         this.$refs.tree.setCheckedKeys(this.Display)
                     })
                     return resolve(resItem)
                })
            }else{
                this.$HTTP
                    .post('bom/get_parent_material', {
                        // number: this.targetNodeId
                        material_number: node.data.number
                    })
                    .then((res) => {
                        console.log(node,'node.level');
                        let resItem = res.result
                        // console.log(resItem,'resItemresItemresItem');
                        if (resItem.length == 0) {
                            this.$HTTP
                                .post('category/get_parent_category', {
                                    // number: this.targetNodeId
                                    number: node.data.number,
                                    // id:node.data.id
                                })
                                .then((res) => {
                                    let resItem = res.result
                                    console.log(resItem, 'resItem');
                                    console.log(node,'文件夹');
                                    // if (resItem.length == 0) {
                                        resItem.map((item) => {
                                            if (item.son == 1) {
                                                item.leaf = false
                                            } else {
                                                item.leaf = true
                                            }
                                        })
                                        for (let i = 0; i < this.treeData.length; i++) {
                                            if (this.treeData[i].id === node.data.id) {
                                                this.treeData[i].children = resItem
                                                break
                                            }
                                        }
                                        this.$nextTick(() => {
                                            this.$refs.tree.setCheckedKeys(this.Display)
                                        })
                                        return resolve(resItem)
    
                                    //  } else {
                                    //     //  this.folderAll(node.data.id,resolve)
                                    //  }
                                })
                        } else {
                            resItem.map((item) => {
                                if (item.son == 1) {
                                    item.leaf = false
                                } else {
                                    item.leaf = true
                                }
                            })
                            for (let i = 0; i < this.treeData.length; i++) {
                                if (this.treeData[i].id === node.data.id) {
                                    this.treeData[i].children = resItem
                                    break
                                }
                            }
                            this.$nextTick(() => {
                                this.$refs.tree.setCheckedKeys(this.Display)
                            })
                            return resolve(resItem)
                        }
                        // this.pictLoading = false
                        // this.$nextTick(() => {
                        //     this.$refs.tree.setCheckedKeys(this.Display)
                        // })
                        // return resolve(myArray)
                        // if (res.result.length == 0) {
                        //     this.treeData = this.convertToTree(myArrayStr)
                        // } else {
                        //     this.folderNodeAll(myArrayStr[0].id,myArrayStr)
                        // }
                    })
            }

            // if (node.level === 0) {
            //     this.pictLoading = true
            //     this.$HTTP.post('material/get_category_product_all').then((res) => {
            //         let resItem = res.result
            //         resItem.map((item) => {
            //             if (item.son == 1) {
            //                 item.leaf = false
            //             } else {
            //                 item.leaf = true
            //             }
            //         })
            //         this.groupData = resItem // 将根节点数据保存在treeData中
            //         console.log(this.groupData, 'this.groupData')

            //         this.pictLoading = false
            //         this.$nextTick(() => {
            //             this.$refs.tree.setCheckedKeys(this.Display)
            //         })
            //         return resolve(resItem)
            //     })
            // } else {
            //     this.$HTTP
            //         .post('material/get_category_product_all', {
            //             category_id: node.data.id,
            //             material_number: node.data.number
            //         })
            //         .then((res) => {
            //             let resItem = res.result
            //             console.log(resItem, 'this.resItem')

            //             resItem.map((item) => {
            //                 if (item.son == 1) {
            //                     item.leaf = false
            //                 } else {
            //                     item.leaf = true
            //                 }
            //             })
            //             for (let i = 0; i < this.groupData.length; i++) {
            //                 if (this.groupData[i].id === node.data.id) {
            //                     this.groupData[i].children = resItem
            //                     break
            //                 }
            //             }
            //             this.$nextTick(() => {
            //                 this.$refs.tree.setCheckedKeys(this.Display)
            //             })
            //             return resolve(resItem)
            //         })

            //     // 等待节点刷新之后，再去设置选中的节点，问题即可解决
            // }

            // // 刷新树组件以更新选中状态
            // this.$nextTick(() => {
            //     this.Display = [node.data.id]
            //     console.log(this.$refs.tree, '下拉数据this.$refs.tree')
            //     // this.$refs.tree.setCheckedKeys(this.Display);
            // })
        },
		mounted() {
			// 触发顶级节点的加载
			this.$refs.tree.load(this.groupData[0]);
		},
		//BOM复制
		copyClick() {
			// console.log(this.currentData,'this.currentData');
			this.copydialog = true
			this.menuVisible = false
			this.dosaShow = false
			this.categoryShow = true
			this.listType = [
				{
					title: '产品',
					value: 0
				}
			]
			this.$HTTP.post('material/get_info', { id: this.currentData.id }).then((res) => {
				if (res.errcode != 0) {
					ElMessage.error(res.errmsg)
				} else {
					let item = res.result
					this.copyform = item
					// this.copyform.number = item.number
					this.copyform.sou_number = item.number
					// 		(this.copyform.title = item.title),
					// 		(this.copyform.type = item.type),
					// 		(this.copyform.drawing_number = item.drawing_number),
					// 		(this.copyform.specs = item.specs),
					// 		(this.copyform.weight = item.weight),
					// 		(this.copyform.unit_title = item.unit_title),
					// 		(this.copyform.unit_id = item.unit_id),
					// 		(this.copyform.texture_id = item.texture_id),
					// 		(this.copyform.texture_title = item.texture_title),
					// 		(this.copyform.price = item.price),
					// 		(this.copyform.quota = item.quota),
					// 		(this.copyform.part_type_id = item.part_type_id),
					// 		(this.copyform.part_type_title = item.part_type_title),
					// 		(this.copyform.dosage = item.dosage),
					// 		(this.copyform.category_title = item.category_title),
					// 		(this.copyform.category_id = item.category_id)
					this.treeList('material/get_category_product_all')
				}
			})
		},
		// input下拉框数据选择回显
		handleSelect(item) {
            if (item.tags) {
                const tagarray = JSON.parse(item.tags).map((item) => {
                    return item.id
                })
                item.tags = tagarray
                this.materialform.tags = tagarray
            }
            this.form = item

            // 修复这段有语法错误的代码
            this.materialform.material_number = item.number;
            this.materialform.parent_material_number = sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`) || '';
            this.materialform.title = item.title;
            this.materialform.type = item.type;
            this.materialform.drawing_number = item.drawing_number;
            this.materialform.specs = item.specs;
            this.materialform.weight = item.weight;
            this.materialform.unit_title = item.unit_title;
            this.materialform.unit_id = item.unit_id;
            this.materialform.texture_id = item.texture_id;
            this.materialform.texture_title = item.texture_title;
            this.materialform.price = item.price;
            this.materialform.quota = item.quota;
            this.materialform.part_type_id = item.part_type_id;
            this.materialform.part_type_title = item.part_type_title;
            this.materialform.dosage = item.dosage;
            this.materialform.category_title = item.category_title;
            this.materialform.category_id = item.category_id;
        },
		// 编号input下拉输入选择
		querySearch(queryString, cb) {
			this.post_page.keyword = queryString
			// var restaurants = []
			this.$HTTP.post('material/get_all', this.post_page).then((res) => {
				if (res.errcode != 0) {
					ElMessage.error(res.errmsg)
				} else {
					// console.log(res.result)
					// restaurants = res.result
					// var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants
					// 调用 callback 返回建议列表的数据
					cb(res.result)
				}
			})
		},

		// 标签数据
		tagsgetData(e) {
			if (e) {
				this.tagsloading = true
				this.$HTTP.post('tag/get_all').then((res) => {
					if (res.errcode != 0) {
						this.tagsloading = false
						ElMessage.error(res.errmsg)
					} else {
						this.tagsloading = false
						this.tagsArray = res.result
					}
				})
			}
		},
		//upload
		uploadload() {
			this.menuVisibletop = false
			this.treeList('material/get_category_product_all')
			if (this.$store.state.listObj) {
				if (this.$store.state.listObj.isShow) {
					this.$store.state.listObj.ver = !this.$store.state.listObj.ver
					this.$store.state.listObj.table = !this.$store.state.listObj.table

				}
			}
			// this.$store.state.listObj.upload = true
			// document.removeEventListener('click', this.fo)
		},
		uploadClick() {
			this.paramsType = {
				type: 0,
				category_id: this.currentData.id
			}
			// return this.paramsType
			console.log('走了吗？？？？', this.currentData);
		},
		// 树形下拉数据添加
		tree_add() {
			// console.log(this.currentData);
			if (this.currentData.ctype == 'category') {
				this.form = {
					parent_number: null,
					number: '',
					title: '',
					type: '',
					drawing_number: '',
					specs: '',
					weight: '',
					unit_title: '',
					unit_id: '',
					texture_id: '',
					texture_title: '',
					price: '',
					quota: '',
					part_type_id: '',
					part_type_title: '',
					dosage: '',
					category_title: '',
					category_id: ''
				}
				this.doing_title = '添加产品'
				this.listType = [
					{
						title: '产品',
						value: 0
					},
					{
						title: '部件',
						value: 1
					},
					{
						title: '零件',
						value: 2
					}
				]
				this.dosaShow = false
				this.categoryShow = true
				this.dialog = true
				this.posturl = 'material/post_add'
			} else {
				// 物料添加
				this.dosaShow = true
				this.categoryShow = false
				this.listType = [
					// {
					//     title: '产品',
					//     value: 0
					// },
					{
						title: '部件',
						value: 1
					},
					{
						title: '零件',
						value: 2
					},
					{
						title: '原材料',
						value: 3
					}
				]
				this.materialform = {
					parent_material_number: this.currentData.number,
					material_number: '',
					opt_type: 0,
					title: '',
					type: '',
					drawing_number: '',
					specs: '',
					weight: '',
					unit_title: '',
					unit_id: '',
					texture_id: '',
					texture_title: '',
					price: '',
					quota: '',
					part_type_id: '',
					part_type_title: '',
					dosage: '',
					category_title: '',
					category_id: ''
				}
				this.materialTitle = '添加物料'
				this.materialDialog = true
				this.posturl = 'bom/post_save'
			}
			this.menuVisible = false
		},
		// 树形下拉数据修改
		dicEdit() {
			const array = localStorage.getItem('ADMIN')
            if(array==1){
                this.unitData=false
            }
			this.$HTTP.post('material/get_info', { id: this.currentData.id }).then((res) => {
				if (res.errcode != 0) {
					ElMessage.error(res.errmsg)
				} else {
					var a = res.result
					// console.log('====================================')
					// console.log(res.result)
					// console.log('====================================')
					if (this.currentData.ctype == 'category') {
						if (a.tags) {
							let arr = JSON.parse(a.tags)
							let array = []
							arr.forEach((item) => {
								array.push(item.title)
							})
							a.tags = array
							this.form = a
						} else {
							this.form = res.result
						}
						this.dialog = true
						this.doing_title = '修改产品'
						this.listType = [
							{
								title: '产品',
								value: 0
							},
							{
								title: "部件",
								value: 1
							},
							{
								title: "零件",
								value: 2
							}
						]
						this.dosaShow = false
						this.categoryShow = true
						this.posturl = 'material/post_modify'
					} else {
						if (a.tags) {
							let arr = JSON.parse(a.tags)
							let array = []
							arr.forEach((item) => {
								array.push(item.title)
							})
							a.tags = array
							this.materialform = a
						} else {
							this.materialform = res.result
						}
						this.materialDialog = true
						this.dosaShow = false
						this.categoryShow = true
						this.listType = [
							{
								title: '产品',
								value: 0
							},
							{
								title: "部件",
								value: 1
							},
							{
								title: "零件",
								value: 2
							},
							{
								title: "原材料",
								value: 3
							}
						]
						this.materialform.parent_material_number = res.result.number
						// console.log(this.materialform, 'aaaaaaaaaaa')
						this.materialTitle = '修改部件'
						this.posturl = 'material/post_modify'
					}
					this.menuVisible = false

				}
			})
		},
		// 物料反查
		backward() {
			console.log('222222222222', this.currentData);

			this.$store.state.isShowOutTree = false
			this.form.parent_number = this.currentData.number
			this.$store.state.listObj = {
				isShow: true,
				isShowTree: true,
				items: this.currentData,
				table: false,
				upload: false
			}
			this.menuVisible = false
		},
		// // 树形数据删除
		// // dicDel() {
		// // 	ElMessageBox.confirm(
		// // 		'是否确认删除此数据?',
		// // 		'删除',
		// // 		{
		// // 			confirmButtonText: '确认',
		// // 			cancelButtonText: '取消',
		// // 			type: 'warning',
		// // 		}
		// // 	)
		// // 		.then(() => {
		// // 			this.menuVisible = false
		// // 			this.$HTTP.post('material/post_del', { id: this.currentData.id }).then((res) => {
		// // 				if (res.errcode != 0) {
		// // 					ElMessage.error(res.errmsg)
		// // 				} else {
		// // 					this.treeList('material/get_category_product_all')
		// // 					// this.removeNode(this.editForm.node)
		// // 					this.$notify({
		// // 						title: '删除',
		// // 						message: '操作成功 ',
		// // 						type: 'success',
		// // 						duration: 2000
		// // 					})
		// // 				}
		// // 			})
		// // 		})
		// // 		.catch(() => {
		// // 			ElMessage({
		// // 				type: 'info',
		// // 				message: '取消删除',
		// // 			})
		// // 		})
		// // },
		// removeNode(node) {
		// 	// console.log(this.groupData)
		// 	// 找到要删除的节点的父节点
		// 	let parentNode = this.findParentNode(this.groupData, node.id);

		// 	if (parentNode) {
		// 	// 从父节点的children数组中移除该节点
		// 	const index = parentNode.children.indexOf(node);
		// 	if (index > -1) {
		// 		parentNode.children.splice(index, 1);
		// 	}
		// 	}
		// 	this.groupData = [...this.groupData]
		// },

		// 查找父节点
		findParentNode(data, nodeId) {
			for (let i = 0; i < data.length; i++) {
				if (data[i].children && data[i].children.length > 0) {
					for (let j = 0; j < data[i].children.length; j++) {
						if (data[i].children[j].id === nodeId) {
							return data[i];
						} else if (data[i].children[j].children && data[i].children[j].children.length > 0) {
							let result = this.findParentNode(data[i].children[j].children, nodeId);
							if (result) {
								return result;
							}
						}
					}
				}
			}
			return null;
		},
		//树过滤
		groupFilterNode(value, data) {
			if (!value) return true
			return data.title.indexOf(value) !== -1
		},
		nodeExpand(data) {
			this.treeList('material/get_category_product_all', { category_id: data.id }, true)
		},
		// 树形下拉数据
		treeList(url, page, op) {
			this.pictLoading = true
			this.$HTTP.post(url, page).then((res) => {
				let resItem = res.result
				resItem.map((item) => {
					if (item.son == 1) {
						item.leaf = false
					} else {
						item.leaf = true
					}
				})
				this.pictLoading = false
				this.groupData = resItem
			})
		},
		//树点击事件
		groupClick(data) {
			console.log(data, 'data选中的数据')
			this.selectedNodeId = data.id; // 设置当前点击节点为选中节点
			this.treeArray = data
			if (data.ctype == 'product') {
				if (data.son) {
					console.log('22222222233333333333333333333');
					this.form.parent_number = data.number
					this.$store.state.listObj = {
						isShow: true,
						items: data,
						table: false,
						upload: false
					}
					sessionStorage.setItem(`parent_material_number_${sessionStorage.getItem('windowId')}`, data.number)
					sessionStorage.setItem(`material_id_${sessionStorage.getItem('windowId')}`, data.id)
					let myArray = [
						{
							id: data.id,
							title: data.title,
							number: data.number
						}
					]
					sessionStorage.setItem(`arraylist_${sessionStorage.getItem('windowId')}`, JSON.stringify(myArray))
					// console.log(data,'1111');
					if (data.type == '3') {
						this.$store.state.curIndex = 4
					} else if (data.type == '2') {
						this.$store.state.curIndex = 2
					} else {
						this.$store.state.curIndex = 1
						this.$store.state.menuVisibletop = false
					}
					// this.treeList('material/get_category_product_all',{category_id:data.id,material_number:data.number},true)
				} else {
					console.log('344444444444444444444444');
					// console.log(data,'222');
					this.form.parent_number = data.number
					sessionStorage.setItem(`parent_material_number_${sessionStorage.getItem('windowId')}`, data.number)
					sessionStorage.setItem(`material_id_${sessionStorage.getItem('windowId')}`, data.id)
					this.$store.state.listObj = {
						isShow: true,
						items: data,
						table: false,
						upload: false
					}
					sessionStorage.setItem(`parent_material_number_${sessionStorage.getItem('windowId')}`, data.number)
					sessionStorage.setItem(`material_id_${sessionStorage.getItem('windowId')}`, data.id)
					let myArray = [
						{
							id: data.id,
							title: data.title,
							number: data.number
						}
					]
					sessionStorage.setItem(`arraylist_${sessionStorage.getItem('windowId')}`, JSON.stringify(myArray))
					if (data.type == '3') {
						this.$store.state.curIndex = 4
					} else if (data.type == '2') {
						this.$store.state.curIndex = 2
					} else {
						this.$store.state.curIndex = 1
						this.$store.state.menuVisibletop = false
					}
				}
			} else if (data.type == '0' || data.type == '1' || data.type == '2' || data.type == '3') {
				this.form.parent_number = data.number
				this.$store.state.listObj = {
					isShow: true,
					items: data,
					table: false,
					upload: false
				}
				sessionStorage.setItem(`parent_material_number_${sessionStorage.getItem('windowId')}`, data.number)
				sessionStorage.setItem(`material_id_${sessionStorage.getItem('windowId')}`, data.id)
				let myArray = [
					{
						id: data.id,
						title: data.title,
						number: data.number
					}
				]
				sessionStorage.setItem(`arraylist_${sessionStorage.getItem('windowId')}`, JSON.stringify(myArray))
				if (data.type == '3') {
					this.$store.state.curIndex = 4
				} else if (data.type == '2') {
					this.$store.state.curIndex = 2
				} else {
					this.$store.state.curIndex = 1
					this.$store.state.menuVisibletop = false
				}
			}
			this.menuVisible = false
		},
		isSelectedNode(nodeId) {
			return nodeId === this.selectedNodeId;
		},
		//成本
		costClick() {
			this.$router.push('/cost')
		},
		//搜索
		upsearch() {
			this.$refs.table.upData(this.search)
		},
		//标签切换
		tabChange(name) {
			var params = {
				groupId: name
			}
			this.$refs.table.reload(params)
		},
		filterChange(data) {
			this.$refs.table.upData(data)
		},
		// 鼠标右击事件
		rightClick(event, object, Node, element) {
			this.editForm.node = object;//鼠标点击的树节点
			this.editFormNode = Node;//鼠标点击的树节点
			// console.log(event, object, Node, element, '1231231231232132131232131')
			object.ctype != 'product' ? (this.post_page = { type: 0 }) : (this.post_page = { include: '1,2,3' })
			if (object.ctype === 'category') {
				this.subdata = false
				this.bomp = false
				// console.log('文件夹')
			} else {
				// console.log('不是文件夹')
				this.subdata = true
				if (this.bomtype) {
					if (this.bomtype != 0) {
						this.bomp = true
					} else {
						this.bomp = false
					}
				} else {
					this.bomp = false
				}
			}

			this.menuVisibletop = false
			this.menuVisible = true
			// 节点数据
			this.currentData = object
			this.$store.state.cost_number = object.number
			// console.log(event.clientX,event.clientY,event,'event.clientX,event.clientY');
			// 获取浏览器窗口的大小
			const windowHeight = window.innerHeight;
			const windowWidth = window.innerWidth;
			// 判断鼠标点击的位置是否接近窗口底部
			const isNearBottom = event.clientY > windowHeight - 320; // 这里的100是可调整的阈值
			if (isNearBottom) {
				// console.log('鼠标点击的位置接近窗口底部');
				// 将菜单显示在鼠标点击旁边定位
				this.menu_left = event.clientX + 50
				this.menu_top = event.clientY - 320
				// 在这里显示你的弹窗，并将它定位到靠近窗口底部的位置
			} else {
				// console.log('鼠标点击的位置不是接近窗口底部');
				// 在这里显示你的弹窗，并将它定位到其他合适的位置将菜单显示在鼠标点击旁边定位
				this.menu_left = event.clientX + 50
				this.menu_top = event.clientY - 0
			}

			document.addEventListener('click', this.foo)
		},
		// 打印按钮
		printClick() {
			this.$router.push({ path: '/searchss', query: this.currentData })
		},
		visibleChange(visible) {
			if (!visible) {
				this.$refs.table.setCurrentRow()
			}
		},
		foo() {
			this.menuVisible = false
			document.removeEventListener('click', this.foo)
		},
		//弹出面板
		// 产品
		handleClose(done) {
			this.dialog = false
			this.loading = false
		},
		cancelForm() {
			this.loading = false
			this.dialog = false
			clearTimeout(this.timer)
		},
		// 衍生
		copyhandleClose() {
			this.copyloading = false
			this.copydialog = false
		},
		copycancelForm() {
			this.copyloading = false
			this.copydialog = false
			clearTimeout(this.timer)
		},
		// 检测
		detection() {
			this.$router.push({ name: 'index', params: { title: '检测', material_number: this.currentData.number } })
		},
		// 物料
		materialHandleClose() {
			this.materialDialog = false
			this.materialLoading = false
		},
		materialCelForm() {
			this.materialDialog = false
			this.materialLoading = false
			clearTimeout(this.timer)
		},

		// 停用
		Deactivate() {
			ElMessageBox.confirm(
				'是否确认停用此数据?',
				'停用',
				{
					confirmButtonText: '确认',
					cancelButtonText: '取消',
					type: 'warning',
				}
			)
				.then(() => {
					this.menuVisible = false
					const currentNode = this.editFormNode;
					this.$HTTP.post('material/post_del', { id: this.currentData.id }).then((res) => {
						if (res.errcode != 0) {
							ElMessage.error(res.errmsg)
						} else {
							console.log(this.editFormNode = currentNode.parent);
							this.Refresh()
							// this.treeList('material/get_category_product_all')
							this.$notify({
								title: '停用',
								message: '操作成功 ',
								type: 'success',
								duration: 2000
							})
						}
					})
				})
				.catch(() => {
					ElMessage({
						type: 'info',
						message: '取消停用',
					})
				})
			// console.log(this.editFormNode,'停用');
		},
		// 复制
		bomcopy() {
			// console.log(this.currentData)
			this.bomp = false
			this.subdata = false
			this.bomtype = this.currentData.type
			this.ruleForm.material_number = this.currentData.number
			this.menuVisible = false
		},
		// 粘贴
		bompaste() {
			if (this.ruleForm.material_number) {
				this.dialogVisible = true
				this.menuVisible = false
				this.ruleForm.parent_material_number = this.currentData.number
			} else {
				ElMessage.error('请先复制')
			}
		},
		// 刷新
		Refresh() {
			if (this.$refs.tree && this.editFormNode) {
				const currentNode = this.editFormNode;
				// currentNode.expanded = false;
				// 清空原有子节点数据（如果需要）
				if (currentNode.data.children) {
					currentNode.data.children = [];
				}
				console.log(currentNode.childNodes = []);
				// 调用获取子节点数据的方法，并直接将结果赋值给当前节点的children
				const loadData = () => {
					return new Promise((resolve) => {
						const params = {
							category_id: currentNode.data.id,
							material_number: currentNode.data.number,
						};

						if (currentNode.level === 0) {
							// 如果是根节点，调用获取所有类别和产品的接口
							this.$HTTP.post('material/get_category_product_all').then((res) => {
								let resItem = res.result;
								resItem.forEach((item) => {
									item.leaf = item.son === 1 ? false : true;
								});

								// 将新数据赋给当前节点的子节点
								currentNode.data.children = resItem;
								resolve();
							});
						} else {
							// 对于非根节点，调用对应的获取子节点接口
							this.$HTTP.post('material/get_category_product_all', params).then((res) => {
								let resItem = res.result;
								resItem.forEach((item) => {
									item.leaf = item.son === 1 ? false : true;
								});

								// 将新数据赋给当前节点的子节点
								currentNode.data.children = resItem;
								resolve();
							});
						}
					});
				};

				// 执行数据加载
				loadData().then(() => {
					// 展开当前节点以显示新加载的数据
					currentNode.expanded = true;

					// //   如果需要同步到 groupData，这里需要根据实际需求决定是否要更新整个 groupData
					// //   如果只需要更新单个节点的子节点，通常不需要同步到整个 groupData
					// //   若确实需要同步，则：
					// this.groupData = [...this.groupData]; // 先复制一份原始数据
					// //   更新对应节点的子节点信息
					// const updatedGroupData = this.groupData.map(item => {
					// 	if (item.id === currentNode.data.id) {
					// 	return { ...item, children: currentNode.data.children };
					// 	}
					// 	return item;
					// });
					// this.groupData = updatedGroupData;
					// // 展开所有父节点
					// let parentNode = currentNode.parent;
					// while (parentNode) {
					// 	parentNode.expanded = true;
					// 	parentNode = parentNode.parent;
					// }

					// console.log(this.groupData, 'this.groupData');
				});
			}
		},
		// 搜索
		searchRef() {
			this.Dialogsearch = {}
			this.gridData = []
			this.DialogsearchFor = true
		},
		Query_button() {
			this.DialogsearchLoading = true
			this.$HTTP
				.post('material/get_all', this.Dialogsearch)
				.then((res) => {
					if (res.errcode != 0) {
						ElMessage.error(res.errmsg)
						this.DialogsearchFor = false
						this.DialogsearchLoading = false
					} else {
						this.gridData = res.result
						this.DialogsearchLoading = false
					}
				})
		},
		handleRowClick(e) {
			// console.log(e);
			this.materialform = e
			this.materialform.material_number = e.number,
				this.materialform.parent_material_number = sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`) ? sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`) : ""
			this.materialform.opt_type = 0
			this.DialogsearchFor = false
			this.materialDialog = true
			this.dosaShow = true
			this.materialTitle = '添加物料'
			this.posturl = 'bom/post_save'
			this.listType = [
				{
					title: '部件',
					value: 1
				},
				{
					title: '零件',
					value: 2
				},
				{
					title: '原材料',
					value: 3
				}
			]
		},
		submitForm(formName) {
			// console.log(this.ruleForm, 'this.ruleForm')
			this.$refs[formName].validate((valid) => {
				if (valid) {
					this.$HTTP.post('bom/post_add', this.ruleForm).then((res) => {
						if (res.errcode != 0) {
							ElMessage.error(res.errmsg)
						} else {
							this.dialogVisible = false
							this.Refresh()
							this.$notify({
								title: '粘贴',
								message: '操作成功 ',
								type: 'success',
								duration: 2000
							})
							// 随后补充页面刷新
							this.$store.state.listObj.ver = !this.$store.state.listObj.ver
							this.$store.state.listObj.table = !this.$store.state.listObj.table
							// this.$store.state.technology = !this.$store.state.technology
							// this.Vspost(this.url, postData)//版本记录

						}
					})
				} else {
					ElMessage.error('带*为必填')
					return false;
				}
			});
		},
		// 产品添加修改提交
		matpost_data(formName) {
			var title = ''
			if (this.posturl == 'material/post_add') {
				title = '添加'
			} else {
				title = '修改'
			}
			this.$refs[formName].validate((valid) => {
				if (valid) {
					this.tagstitle = this.form.tags
					if (this.form.tags) {
						var postarr = []
						this.$HTTP.post('tag/get_all').then((res) => {
							if (res.errcode != 0) {
								ElMessage.error(res.errmsg)
							} else {
								postarr = res.result
								const mergedArray = this.form.tags.map((tag) => {
									const existingPost = postarr.find((post) => post.title === tag)
									if (existingPost) {
										return { title: existingPost.title, id: existingPost.id }
									} else {
										return { title: tag, id: '' }
									}
								})
								this.form.tags = JSON.stringify(mergedArray)
							}
							this.loading = true
							// 调取接口

							this.$HTTP
								.post(this.posturl, this.form)
								.then(async (res) => {
									if (res.errcode != 0) {
										this.loading = false
										this.form.tags = this.tagstitle
										ElMessage.error(res.errmsg)
									} else {
										if (this.doing_title == '添加物料' || this.doing_title == '修改部件') {
											if (this.$store.state.listObj) {
												this.$store.state.listObj.table = !this.$store.state.listObj.table
											}
											// this.groupData = []
											// this.treeList('material/get_category_product_all')
										} else if (this.doing_title == '添加产品' || this.doing_title == '修改产品') {
											// this.groupData = []
											// this.treeList('material/get_category_product_all')

										}
										await this.Refresh()
										this.dialog = false
										this.loading = false
										this.$notify({
											title: title,
											message: '操作成功 ',
											type: 'success',
											duration: 2000
										})
									}
								})
								.catch(() => {
									this.loading = false
									this.form.tags = this.tagstitle
								})
						})
					} else {
						this.loading = true
						// 调取接口

						this.$HTTP
							.post(this.posturl, this.form)
							.then(async (res) => {
								if (res.errcode != 0) {
									this.loading = false
									this.form.tags = this.tagstitle
									ElMessage.error(res.errmsg)
								} else {
									if (this.doing_title == '添加物料' || this.doing_title == '修改部件') {
										if (this.$store.state.listObj) {
											this.$store.state.listObj.table = !this.$store.state.listObj.table
										}
										// this.groupData = []
										// this.treeList('material/get_category_product_all')
									} else if (this.doing_title == '添加产品' || this.doing_title == '修改产品') {
										// this.groupData = []
										// this.treeList('material/get_category_product_all')
									}
									await this.Refresh()
									this.dialog = false
									this.loading = false
									this.$notify({
										title: title,
										message: '操作成功 ',
										type: 'success',
										duration: 2000
									})
								}
							})
							.catch(() => {
								this.loading = false
								this.form.tags = this.tagstitle
							})
					}
				} else {
					this.form.tags = ''
					ElMessage.error('带*为必填')
					return false
				}
			})
		},
		// 物料添加修改提交
		material_data(formName) {
			var title = ''
			if (this.materialform.opt_type == 0) {
				title = '添加'
			} else {
				title = '修改'
			}
			this.$refs[formName].validate((valid) => {
				if (valid) {
					this.tagstitle = this.materialform.tags
					if (this.materialform.tags) {
						var postarr = []
						this.$HTTP.post('tag/get_all').then((res) => {
							if (res.errcode != 0) {
								ElMessage.error(res.errmsg)
							} else {
								postarr = res.result
								const mergedArray = this.materialform.tags.map((tag) => {
									const existingPost = postarr.find((post) => post.title === tag)
									if (existingPost) {
										return { title: existingPost.title, id: existingPost.id }
									} else {
										return { title: tag, id: '' }
									}
								})
								this.materialform.tags = JSON.stringify(mergedArray)
							}
							this.materialLoading = true
							// 调取接口

							this.$HTTP
								.post(this.posturl, this.materialform)
								.then((res) => {
									if (res.errcode != 0) {
										this.materialform.tags = this.tagstitle
										this.materialLoading = false
										ElMessage.error(res.errmsg)
									} else {
										if (this.materialTitle == '添加物料' || this.materialTitle == '修改部件') {
											if (this.$store.state.listObj) {
												this.$store.state.listObj.table = !this.$store.state.listObj.table
											}
											this.Refresh()
                                            this.materialDialog = false
                                            this.materialLoading = false
                                            this.materialDialogAdd = false
                                            this.$notify({
                                                title: title,
                                                message: '操作成功 ',
                                                type: 'success',
                                                duration: 2000
                                            })
											this.$store.state.listObj.ver = !this.$store.state.listObj.ver
										}
										this.Refresh()
										this.materialDialog = false
										this.materialLoading = false
										this.$notify({
											title: title,
											message: '操作成功 ',
											type: 'success',
											duration: 2000
										})
									}
								})
								.catch(() => {
									this.materialform.tags = this.tagstitle
								})
						})
					} else {
						this.materialLoading = true
						// 调取接口

						this.$HTTP
							.post(this.posturl, this.materialform)
							.then((res) => {
								if (res.errcode != 0) {
									this.materialform.tags = this.tagstitle
									this.materialLoading = false
									ElMessage.error(res.errmsg)
								} else {
									if (this.materialTitle == '添加物料' || this.materialTitle == '修改部件') {
										if (this.$store.state.listObj) {
											this.$store.state.listObj.table = !this.$store.state.listObj.table
										}
										this.$store.state.listObj.ver = !this.$store.state.listObj.ver
									}
									this.Refresh()
									this.materialDialog = false
									this.materialLoading = false
									this.$notify({
										title: title,
										message: '操作成功 ',
										type: 'success',
										duration: 2000
									})
								}
							})
							.catch(() => {
								this.materialform.tags = this.tagstitle
							})
					}
				} else {
					// this.materialform.tags = ''
					// this.materialDialog = false
					this.materialLoading = false
					ElMessage.error('带*为必填')
					return false
				}
			})
		},
		// 复制弹窗提交
		copyonclickAdd(formName) {
			this.$refs[formName].validate((valid) => {
				if (valid) {
					this.copyloading = true
					// 调取接口
					this.$HTTP.post('bom/post_copy_bom', this.copyform).then((res) => {
						if (res.errcode != 0) {
							this.copyloading = false
							ElMessage.error(res.errmsg)
						} else {
							this.Refresh()
							this.copydialog = false
							this.copyloading = false
							this.$notify({
								title: title,
								message: '操作成功 ',
								type: 'success',
								duration: 2000
							})
						}
					})
				} else {
					ElMessage.error('带*为必填')
					return false
				}
			})
		},

		// 材质数据
		getData() {
			this.$HTTP.post('texture/get_all').then((res) => {
				this.texture = res.result
			})
		},
		selectget(e) {
			let dir = this.texture.find((item) => item.title === e)
			this.form.texture_id = dir.id
			this.materialform.texture_id = dir.id
		},
		// 单位数据
		unit_getData() {
			this.$HTTP.post('unit/get_all').then((res) => {
				this.unitTitle = res.result
			})
		},
		selectUnit(e) {
			let dir = this.unitTitle.find((item) => item.title === e)
			this.form.unit_id = dir.id
			this.materialform.unit_id = dir.id
		},
		// 零件类型
		partType_title() {
			this.$HTTP.post('part_type/get_all').then((res) => {
				this.partTypeArray = res.result
			})
		},
		partType(e) {
			let dir = this.partTypeArray.find((item) => item.title === e)
			this.form.part_type_id = dir.id
			this.materialform.part_type_id = dir.id
		},
		// 分类数据
		categorydata() {
			this.$HTTP.post('category/get_all').then((res) => {
				this.categoryArray = res.result
			})
		},
		selectCategory(e) {
			let dir = this.categoryArray.find((item) => item.title === e)
			this.form.category_id = dir.id
			this.materialform.category_id = dir.id
		},
		// 下载
		download(op) {
			console.log(op, 'op')

			// this.$HTTP.get('drawing/get_ls',{number:op.number}).then((res) => {
			// 	// this.partTypeArray = res.result

			// })
			let array = op.number
			let arrayStr = op.title
			this.filesLoading = true
			this.foo()
			this.$HTTP.post('drawing/get_drawing_file', { material_number: this.$store.state.cost_number }, { responseType: 'blob' }).then((res) => {
				let reader = new FileReader();
				reader.readAsText(res)
				const thit = this
				reader.onload = function (result) {
					try {
						let resData = JSON.parse(result.target.result)
						if (resData.errcode) {
							ElMessage.error(resData.errmsg)
							thit.filesLoading = false
						}
					} catch (err) {
						let blob = new Blob([res], { type: 'application/octet-stream' })
						let url = window.URL.createObjectURL(blob)
						const link = document.createElement('a') // 创建a标签
						link.href = url
						link.download = array + '【' + arrayStr + '】' + '.zip' // 重命名文件
						document.body.appendChild(link)
						link.click()
						document.body.removeChild(link)
						thit.filesLoading = false
					}
				}
			}).catch((error) => {
				ElMessage.error(error)
				this.filesLoading = false
			})
		},
	},
	mounted() {
		document.addEventListener('mouseup', this.stopResizing);
	},
	beforeDestroy() {
		document.removeEventListener('mouseup', this.stopResizing);
	}
}
</script>

<style lang="scss" scoped>
.el-aside {
	position: relative;
}

.resizer {
	position: absolute;
    top: 45%;
    right: 0;
    width: 8px;
    height: 10%;
    cursor: ew-resize;
    background-color: rgba(243, 239, 239, 0.2);
    opacity: 0.5;
    transition: all 0.3s;

    &:hover {
        background-color: #a7cbff;
        opacity: 1;
    }

    &::after {
        content: "⋮⋮";
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        color: #666;
        font-size: 12px;
    }
}

// .custom-highlight {
//   background-color: #eee; /* 自定义选中节点的背景色 */
// }
:deep {
	.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
		background-color: #81caff !important;
	}

	.oversized {
		height: 100px;
		/* 进度条变大时的高度 */
	}
}

.dropdown_menu {
	margin: 0 10px;
}

.no-inherit {
	vertical-align: bottom;
	margin-right: 10px;
}

.demo-drawer__footer {
	position: fixed;
	bottom: 10px;
	right: 10px;
}

.demo-form-inline {
	padding: 0 20px;
}

.formType {
	margin-bottom: 50px;

	.title {
		font-size: 17px;
		font-weight: 550;
		padding: 20px;
		border-bottom: 2px solid #e9e9e9;
		margin: 0 0 10px 0;
	}
}

.custom-tree-node {
	display: flex;
	flex: 1;
	align-items: center;
	justify-content: space-between;
	font-size: 14px;
	padding-right: 24px;
	height: 100%;
}

.custom-tree-node .do {
	margin-left: 20px;
	display: none;
}

.custom-tree-node .do i {
	margin-left: 5px;
	color: #999;
}

.custom-tree-node .do i:hover {
	color: #333;
}

.custom-tree-node:hover .do {
	display: inline-block;
}

.box-menu {
	// width: 100px;
	position: absolute;
	z-index: 1000;
	background-color: #fff;
	box-shadow: 0px 0px 10px #ccc, 0px 0px 20px #ccc, 0px 0px 30px #ccc;
	padding: 10px;

	div {
		cursor: pointer;
		line-height: 30px;
	}
}

.text_or {
	color: #67c23a;
}

// .text_over{
//     display: inline-block;
//     // overflow: hidden;
//     text-overflow: ellipsis;
//     white-space: nowrap;
//     width: 220px;
// }</style>
