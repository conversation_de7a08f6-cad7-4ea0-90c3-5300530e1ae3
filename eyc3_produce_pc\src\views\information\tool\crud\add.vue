<template>
    <el-main style="padding: 0 30px" class="el-main">
        <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" class="demo-form-inline" :size="formSize"
            status-icon :inline="true" label-position="top">
            <el-row>
                <el-col>
                    <el-form-item label="机床名称" prop="title">
                        <el-input v-model="ruleForm.title" placeholder="请输入" style="min-width: 580px" />
                    </el-form-item>
                    <el-form-item label="机床编码" prop="code">
                        <el-input v-model="ruleForm.code" placeholder="请输入" style="min-width: 580px" />
                    </el-form-item>
                    <el-form-item label="所属车间" prop="center_code">
                        <el-select v-model="ruleForm.center_code" filterable placeholder="请选择" style="min-width: 580px">
                            <el-option v-for="item in workshopList" :key="item.corpid" :label="item.title"
                                :value="item.code" />
                        </el-select>
                    </el-form-item>
                    <!-- <el-form-item label="车间费率" prop="hour_rate">
                        <el-input type="number" v-model="ruleForm.hour_rate" placeholder="请输入"
                            style="min-width: 580px" />
                    </el-form-item> -->
                    <!-- <el-form-item
                        label="综合小时费率"
                        prop="hourly_rate"
                    >
                        <el-input
                            type="number"
                            v-model="ruleForm.hourly_rate"
                            placeholder="请输入"
                            style="min-width: 580px"
                        />
                    </el-form-item> -->
                    <el-form-item label="人工分钟费率" prop="process_hour_rate">
                        <el-input type="number" v-model="ruleForm.process_hour_rate" placeholder="请输入"
                            style="min-width: 580px" />
                    </el-form-item>
                    <el-form-item label="工艺名称" prop="process">
                        <el-select v-model="ruleForm.process" multiple style="min-width: 580px" @click="getData()"
                            filterable placeholder="请选择">
                            <el-option v-for="item in texture_all" :key="item.id" :label="item.title"
                                :value="item.title">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <yy_uploads v-if="upload" :label="upload.label" :templateUrl="upload.templateUrl"
                        :columnData="upload.columnData" :url="upload.url" :maxSize="upload.maxSize"
                        :accept="upload.accept" :filename="upload.filename" @upload="uploaditem" :data="upload.data">
                    </yy_uploads>
                </el-col>
            </el-row>
        </el-form>
    </el-main>
    <div class="el-buttonA">
        <el-button type="primary" :loading="isSaveing" @click="submit()">提 交</el-button>
        <el-button @click="cancel">取 消</el-button>
    </div>
</template>

<script>
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
export default {
    name: 'verificate',
    data() {
        return {
            workshopList: [],
            dialogVisible: false,
            ruleForm: {
                title: '',
                code: '',
                hour_rate: '',
                process: [],
                center_code: "",
                hourly_rate: 0,
                process_hour_rate: 0
            },
            upload: {
                label: '附件导入',
                component: 'upload',
                url: `${import.meta.env.VITE_APP_FILE_UPLOAD}file/post_upload`,
                accept: '',
                maxSize: 500,
                data: {
                    upload_type: 'local',
                    // name_type:'org',
                    // dir_name:'tool'
                }
            },
            texture_all: [],
            isSaveing: false,
            rules: {
                title: [
                    {
                        required: true,
                        message: '请输入机床名称',
                        trigger: 'blur'
                    }
                ],
                code: [
                    {
                        required: true,
                        message: '请输入机床编码',
                        trigger: 'blur'
                    }
                ],
                center_code: [
                    {
                        required: true,
                        message: '请选择所属车间',
                        trigger: 'change'
                    }
                ],
                hourly_rate: [
                    {
                        required: true,
                        message: '请输入综合小时费率',
                        trigger: 'blur'
                    }
                ],
                process_hour_rate: [
                    {
                        required: true,
                        message: '请输入人工分钟费率',
                        trigger: 'blur'
                    }
                ],
                hour_rate: [
                    {
                        required: true,
                        message: '请输入费率',
                        trigger: 'blur'
                    }
                ],
                process: [
                    {
                        required: true,
                        message: '请选择工艺',
                        trigger: 'blur'
                    }
                ],
            },
        }
    },
    methods: {
        uploaditem(op) {
            this.ruleForm.url = op
        },
        addsaveData(op) {
            this.api = op.add
            this.getWorkshopList()
        },
        saveData(op) {
            this.api = op.edit
            this.getWorkshopList()
            this.getData()
            this.$HTTP.post('tool/get_info', { id: op.id }).then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    let obj = res.result
                    if (obj.process) {
                        const array = JSON.parse(obj.process).map(item => {
                            return item.process_title
                        })
                        obj.process = array
                    }
                    this.ruleForm = obj
                }
            })
        },
        //获取所有车间列表
        getWorkshopList() {
            this.$HTTP.get('center/get_all').then((res) => {
                if (res.errcode == 0) {
                    this.workshopList = res.result
                }
            })
        },
        cancel() {
            this.$emit('saveclosure', false)
        },
        getData() {
            this.$HTTP.post('process/get_all').then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    this.texture_all = res.result
                }
            })
        },
        //表单提交方法
        submit() {
            this.isSaveing = true
            this.$refs.ruleFormRef.validate(async (valid) => {
                if (valid) {
                    const matchedData = this.texture_all.filter(item => this.ruleForm.process.includes(item.title)).map(item => ({ process_id: item.id, process_title: item.title }));
                    console.log(matchedData, this.ruleForm.process, 'this.ruleForm.process')
                    this.ruleForm.process = JSON.stringify(matchedData)
                    this.$HTTP.post(this.api, this.ruleForm).then((res) => {
                        if (res.errcode == 0) {
                            this.$emit('saveclosure', false)
                            ElMessage.success('操作成功!')
                            this.isSaveing = false
                            this.$emit('transfer', '成功')
                        } else {
                            ElMessage.error(res.errmsg)
                            const matchedData = this.texture_all.filter(item => this.ruleForm.process.includes(item.title)).map(item => (item.title));
                            this.ruleForm.process = matchedData
                            this.isSaveing = false
                        }
                    })
                } else {
                    this.isSaveing = false
                    console.log('error submit!!')
                    return false
                }
            })
        },
    }
}
</script>
<style scoped>
.h1-div {
    width: 100%;
    height: 10px;
    border-bottom: 1px solid #dcdfe6;
    margin-left: 30px;
}

.el-main {
    margin-bottom: 80px;
}

.el-buttonA {
    width: 100%;
    padding: 20px;
    /* border-top: 1px solid #dcdfe6; */
    background: #fff;
    position: fixed;
    bottom: 0px;
    z-index: 999;
    box-sizing: border-box;
}
</style>
