import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/display.css'
import scui from './scui'
import i18n from './locales'
import router from './router'
import store from './store'
import { createApp } from 'vue'
import App from './App.vue'

// 处理 ResizeObserver 错误
const debounce = (fn, delay) => {
  let timer = null;
  return function() {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => fn.apply(this, arguments), delay);
  };
};

window.addEventListener('error', (e) => {
  if (e.message === 'ResizeObserver loop limit exceeded') {
    const resizeObserverErr = e;
    resizeObserverErr.stopImmediatePropagation();
  }
});

const app = createApp(App);

// window.addEventListener('popstate', (event) => {
//   // 阻止默认的前进后退行为
//   event.preventDefault()

//   // 可以添加一些自定义逻辑，或者什么都不做
//   // 例如，你可以在这里弹出一个提示，阻止用户离开页面
//   console.log('前进后退被禁止了！')
// })
// 


// // 页面加载时禁用前进和后退功能
// disableBackForward();

// // 路由切换时禁用前进和后退功能
// router.beforeEach((to, from, next) => {
//   disableBackForward();
//   next();
// });



// function disableBackForward() {
//   window.history.pushState(null, null, window.location.href);
//   window.onpopstate = function () {
//     window.history.pushState(null, null, window.location.href);
//   };
// }

// // 钉钉内置浏览器特殊处理
// if (typeof dd !== 'undefined') {
//   dd.ready(() => {
//     dd.biz.navigation.setLeft({
//       control: false, // 隐藏返回按钮
//     });
//   });
// }
app.use(store);
app.use(router);
app.use(ElementPlus);
app.use(i18n);
app.use(scui);

//挂载app
app.mount('#app');
