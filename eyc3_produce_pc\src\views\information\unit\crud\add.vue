<template>
    <el-main
        style="padding: 0 30px"
        class="el-main"
    >
        <el-form
            ref="ruleFormRef"
            :model="ruleForm"
            :rules="rules"
            class="demo-form-inline"
            :size="formSize"
            status-icon
            :inline="true"
            label-position="top"
            @submit.native.prevent
        >
            <el-row>
                <el-col>
                    <el-form-item
                        label="单位名称"
                        prop="title"
                    >
                        <el-input
                            v-model="ruleForm.title"
                            placeholder="请输入"
                            style="min-width: 580px"
                        />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </el-main>
    <div class="el-buttonA">
        <el-button
            type="primary"
            :loading="isSaveing"
            @click="submit()"
            >提 交</el-button
        >
        <el-button @click="cancel">取 消</el-button>
    </div>
</template>

<script>
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
export default {
    name: 'verificate',
    data() {
        return {
            dialogVisible: false,
            ruleForm: {
                title: ''
            },
            isSaveing: false,
            rules: {
                title: [
                    {
                        required: true,
                        message: '请输入单位名称',
                        trigger: 'blur'
                    }
                ],
            },
        }
    },
    methods: {
        addsaveData(op) {
            this.api = op.add
        },
        saveData(op) {
            this.api = op.edit
            this.$HTTP.post('unit/get_info', { id: op.id }).then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    this.ruleForm = res.result
                }
            })
        },
        cancel() {
            this.$emit('saveclosure', false)
        },
        // uploaditem(op){
        //     this.ruleForm.url = op
        // },
        //表单提交方法
        submit() {
            this.isSaveing = true
            this.$refs.ruleFormRef.validate(async (valid) => {
                if (valid) {
                    this.$HTTP.post(this.api, this.ruleForm).then((res) => {
                        if (res.errcode == 0) {
                            this.$emit('saveclosure', false)
                            ElMessage.success('操作成功!')
                            this.isSaveing = false
                            this.$emit('transfer', '成功')
                        } else {
                            ElMessage.error(res.errmsg)
                            this.isSaveing = false
                        }
                    })
                } else {
                    this.isSaveing = false
                    console.log('error submit!!')
                    return false
                }
            })
        },
        onSelect() {}
    }
}
</script>
<style scoped>
.h1-div {
    width: 100%;
    height: 10px;
    border-bottom: 1px solid #dcdfe6;
    margin-left: 30px;
}

.el-main {
    margin-bottom: 80px;
}
.el-buttonA {
    width: 100%;
    padding: 20px;
    /* border-top: 1px solid #dcdfe6; */
    background: #fff;
    position: fixed;
    bottom: 0px;
    z-index: 999;
    box-sizing: border-box;
}
</style>
