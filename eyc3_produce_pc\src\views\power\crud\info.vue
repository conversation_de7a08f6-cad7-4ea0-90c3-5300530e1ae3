<template>
    <el-main style="padding: 0 20px">
        <el-descriptions
            title="权限信息"
            :column="2"
            border
        >
            <el-descriptions-item label="权限名称">{{ infoForm.title }}</el-descriptions-item>
            <el-descriptions-item label="权限人员">{{ infoForm.userNames }}</el-descriptions-item>
            <el-descriptions-item label="管理范围">{{ infoForm.manage_scope==0?'全组织':infoForm.manage_scope==1?'所在部门和下级部门':'所在部门' }}</el-descriptions-item>
        </el-descriptions>

        <div class="actions-title">权限范围</div>
        <yy_selecttree
            v-model:value.sync="infoForm.actions"
            :remote="permissionRemote"
            :defaultProps="defaultProps"
            :disabled="true"
        ></yy_selecttree>
    </el-main>
</template>

<script>
export default {
    data() {
        return {
            infoForm: {},
            permissionRemote: {
                api: `permission_action/get_all`
            },
            defaultProps: {
                children: 'acts',
                label: 'title'
            }
        }
    },
    mounted() {},
    methods: {
        //注入数据
        setData(data) {
            console.log(data, '5555558')
            this.infoForm = data
            // this.getInfo()
        },
        // 产品信息
        getInfo() {
            this.loading = true
            this.$HTTP
                .get('permission_group/get_info', {
                    id: this.id
                })
                .then((res) => {
                    console.log(res)
                    this.infoForm = res.result
                    let userNames = JSON.parse(this.infoForm.userlst).users
                    let userName = ''
                    for (let i in userNames) {
                        // console.log(i==names.length-1);
                        if (i == userNames.length - 1) {
                            // console.log("最后一个");
                            userName = userName + userNames[i].name
                        } else {
                            userName = userName + userNames[i].name + '、'
                        }
                    }
                    this.infoForm.userNames = userName
                })
                .finally(() => {})
        }
    }
}
</script>

<style scoped>
.actions-title {
    margin-top: 24px;
    margin-bottom: 8px;
    font-size: 14px;
}
</style>
