<template>
    <el-main>
        <!-- <el-alert title="来自AVUE的灵感, 业务会有表单里含有对象数组的可能, 进行封装后进行增删改操作, 并且支持联动form表单的禁用属性" type="success" style="margin-bottom:20px;"></el-alert> -->
        <div id="drawer" :class="`${drawerbul ? 'drawercl' : 'drawerout'}`">
            <div class="header_ta">
                <h3>{{ tabulation_title }}</h3>
                <el-icon :size="25" @click="close_button()">
                    <el-icon-Close />
                </el-icon>
            </div>
            <div v-if="process_name">
                <table border="1" class="tablecos">
                    <tr>
                        <th style="max-width: 50px">序号</th>
                        <th>名称</th>
                    </tr>
                    <tr v-for="(item, index) in process_array" :key="item.id" @click="process_cli(item, '工序名称')">
                        <td>{{ index + 1 }}</td>
                        <td>{{ item.title }}</td>
                    </tr>
                </table>
            </div>
            <div v-if="process_content">
                <table border="1" class="tablecos">
                    <tr>
                        <th style="max-width: 50px">序号</th>
                        <th>内容</th>
                    </tr>
                    <tr v-for="(item, index) in process_array" :key="item.id" @click="process_cli(item, '工序内容')">
                        <td>{{ index + 1 }}</td>
                        <td>{{ item.content }}</td>
                    </tr>
                </table>
            </div>
            <div v-if="process_code">
                <table border="1" class="tablecos">
                    <tr>
                        <th style="max-width: 50px">序号</th>
                        <th>机床代号</th>
                        <th>工序名称</th>
                    </tr>
                    <tr v-for="(item, index) in process_array" :key="item.id" @click="process_cli(item, '机床代号')">
                        <td>{{ index + 1 }}</td>
                        <td>{{ item.code }}</td>
                        <td>{{ item.process_title }}</td>
                    </tr>
                </table>
            </div>
        </div>
        <el-row :gutter="22" justify="end">
            <el-dropdown trigger="click" placement="top">
                <el-button class="el-dropdown-link" icon="el-icon-arrow-down">
                    按钮菜单
                </el-button>
                <template #dropdown>
                    <el-dropdown-item>
                        <el-button type="primary" @click="editClick">编辑</el-button>
                    </el-dropdown-item>
                    <el-dropdown-item>
                        <el-button type="primary" @click="dialogFormclick()">复制
                        </el-button>
                    </el-dropdown-item>
                    <el-dropdown-item>
                        <el-button type="primary" @click="pasteClick">粘贴
                        </el-button>
                    </el-dropdown-item>
                </template>
            </el-dropdown>
        </el-row>
        <el-form v-show="isEdit" ref="ruleForm" :model="form" :rules="rules" label-width="0px">
            <el-form-item label="" prop="list">
                <sc-form-table v-model="form.list" :addTemplate="addTemplate" drag-sort @header-dragend.native="handleHeaderDragEnd"
                    @selection-change="handleSelectionChange" placeholder="暂无数据">
                    <el-table-column prop="process_title" label="工序名称" width="180">
                        <template #default="scope">
                            <el-input v-model="scope.row.process_title"
                                @focus="process_focus(scope.row.process_title, '工序名称', 'process/get_all', scope)"
                                @input="process_input(scope.row.process_title, '工序名称', 'process/get_all')"
                                placeholder="请输入工序名称"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="process_content" label="工序内容" min-width="180">
                        <template #default="scope">
                            <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" placeholder="请输入工序内容"
                                v-model="scope.row.process_content" @focus="
                                    process_focus(scope.row.process_title, '工序内容', 'process_content/get_all', scope)
                                    " @input="process_input(scope.row.process_content, '工序内容', 'process_content/get_all')">
                            </el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="tool_code" label="机床代号" width="120">
                        <template #default="scope">
                            <el-input v-model="scope.row.tool_code"
                                @focus="process_focus(scope.row.process_title, '机床代号', 'tool/get_all', scope)" @input="
                                    process_input(
                                        scope.row.tool_code,
                                        '机床代号',
                                        'tool/get_all',
                                        scope.row.process_title
                                    )
                                    " placeholder="请输入机床代号"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="unit_hour" label="单件工时" width="100" align="center">
                        <template #default="scope">
                            <el-input v-model="scope.row.unit_hour" placeholder="请输入内容" type="number"
                                @focus="(e) => inputChange('unit_hour', scope.row, e)">
                            </el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="desc" label="备注" width="180" align="center">
                        <template #default="scope">
                            <el-input v-model="scope.row.desc" type="textarea" :autosize="{ minRows: 2, maxRows: 4 }"
                                placeholder="请输入内容"></el-input>
                        </template>
                    </el-table-column>
                </sc-form-table>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="submitForm">保存</el-button>
                <el-button @click="StagingForm">暂存</el-button>
                <el-button @click="resetForm">重置</el-button>
            </el-form-item>
        </el-form>
        <el-table :data="form.list" border style="" v-show="!isEdit">
            <el-table-column prop="process_title" label="工序名称" width="180" />
            <el-table-column prop="process_content" label="工序内容" min-width="140" />
            <el-table-column prop="tool_code" label="机床代号" width="120" />
            <el-table-column prop="unit_hour" label="单件工时" width="85" />
            <el-table-column prop="desc" label="备注" width="85" />
        </el-table>
    </el-main>
    <el-dialog v-model="dialogFormVisible" title="复制内容选择">
        <el-table ref="multipleTable" :data="this.form.list" tooltip-effect="dark" style="width: 100%"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55">
            </el-table-column>
            <el-table-column prop="process_title" label="工序名称" width="180" />
            <el-table-column prop="process_content" label="工序内容" min-width="140" />
            <el-table-column prop="tool_code" label="机床代号" width="120" />
            <el-table-column prop="unit_hour" label="单件工时" width="85" />
            <el-table-column prop="desc" label="备注" width="85" />
        </el-table>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="dialogFormVisible = false">取消</el-button>
                <el-button type="primary" @click="copyClick()">
                    确认
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script>
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
export default {
    name: 'formtable',
    props: {
        crafts: { type: Object, default: () => { } }
    },
    data() {
        return {
            // 节点下标
            process_index: '',
            // 侧边弹窗选择
            drawerbul: false,
            // 弹窗标题
            tabulation_title: '',
            // 工序名称
            process_name: false,
            // 工序内容
            process_content: false,
            // 机床代号
            process_code: false,
            // 弹窗数据
            process_array: [],
            dialogFormVisible: false,
            multipleSelection: [],
            content: '',
            isEdit: false,
            addTemplate: {
                process_id: '', //工序id
                process_title: '', //工序
                process_content_id: '', //工序内容id
                process_content: '', //工序内容
                process_hour_rate: '', //人工单位工时费
                tool_id: '', //机床号id
                tool_code: '', //机床号
                tool_title: '', //机床标题
                tool_hour_rate: '', //机床单位工时费
                unit_hour: '', //单位工时
                desc: '' //备注
            },
            form: {
                list: []
            },
            loading: false,
            typeDic: [],
            material_numberAll: '',
            rules: {
                title: [{ required: true, message: '请上传', trigger: 'blur' }],
                list: [
                    {
                        required: true,
                        message: '工序列表不能为空',
                        trigger: 'change'
                    }
                ],
                process_title: [
                    {
                        required: true,
                        message: '工序名称不能为空',
                        trigger: 'blur'
                    }
                ]
            }
        }
    },
    emits: ['showDrawer'],
    created() {
        this.material_numberAll=sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`)
        let array = JSON.parse(localStorage.getItem('USER_INFO'))

        if (this.crafts.userid == array.content.userid) {
            console.log('1111111111111111111111111111111111111111111');
            this.$HTTP
                .get('craft/get_detail', { material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`) })
                .then((res) => {
                    if (res.errcode != 0) {
                        ElMessage.error(res.errmsg)
                    } else {
                        this.form.list = res.result.crafts
                    }
                })
        } else {
            this.form.list = this.crafts.crafts
        }
        // this.form.list = this.crafts
        this.$store.watch(
            (state) => state.technology,
            (newValue, oldValue) => {
                console.log(newValue, 'newValue1·1·1');
                console.log(oldValue, 'oldValue1212·21212');
                if (newValue == true && oldValue == false) {
                    this.$HTTP
                        .get('craft/get_detail', { material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`) })
                        .then((res) => {
                            if (res.errcode != 0) {
                                ElMessage.error(res.errmsg)
                            } else {
                                this.form.list = res.result.crafts
                            }
                        })
                }
                if(newValue==false&& oldValue==true){
                        this.$HTTP
                            .get('craft/get_detail', { material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`) })
                            .then((res) => {
                                if (res.errcode != 0) {
                                    ElMessage.error(res.errmsg)
                                } else {
                                    this.form.list = res.result.crafts
                                }
                            })

                }
                // if(newValue==true && oldValue==undefined){
                // }else if(newValue==true && oldValue==false){
                //     this.$HTTP
                //         .get('craft/get_detail', { material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`) })
                //         .then((res) => {
                //             if (res.errcode != 0) {
                //                 ElMessage.error(res.errmsg)
                //             } else {
                //                 this.form.list = res.result.crafts
                //             }
                //         })
                // }else if(newValue==false && oldValue==true){
                //     // alert('333333333333333333')

                // }
                // this.$HTTP
                //     .get('craft/get_detail', { material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`) })
                //     .then((res) => {
                //         if (res.errcode != 0) {
                //             ElMessage.error(res.errmsg)
                //         } else {
                //             this.form.list = res.result.crafts
                //         }
                //     })
            }
        )
        this.$store.watch(
            (state) => state.release_stlist,
            (newValue, oldValue) => {
                this.isEdit = false
                
                this.form.list = this.$store.state.release_stlist.crafts
            }
        )
        this.$store.watch(
            (state) => state.Unpublishedlist,
            (newValue, oldValue) => {
                this.isEdit = false
                this.form.list = this.$store.state.Unpublishedlist.crafts
            }
        )
    },
    mounted() { },
    methods: {
        process_cli(row, title) {
            switch (title) {
                case '工序名称':
                    this.form.list[this.process_index].process_id = row.id
                    this.form.list[this.process_index].process_title = row.title
                    break
                case '工序内容':
                    this.form.list[this.process_index].process_content = row.content
                    this.form.list[this.process_index].process_content_id = row.id
                    this.form.list[this.process_index].process_hour_rate = row.hour_rate
                    break
                case '机床代号':
                    this.form.list[this.process_index].tool_id = row.id
                    this.form.list[this.process_index].tool_code = row.code
                    this.form.list[this.process_index].tool_title = row.title
                    this.form.list[this.process_index].tool_hour_rate = row.hour_rate
                    break
            }
            // 选择完成后关闭抽屉
            this.drawerbul = false
            this.tabulation_title = ''
            this.process_array = []
        },

        //列表手动拉伸宽度调整
        handleHeaderDragEnd(newWidth, oldWidth, column){
            console.log(newWidth, oldWidth, column)
        },
        // 弹窗关闭
        close_button() {
            this.drawerbul = false
            this.tabulation_title = ''
            this.process_array = []
        },
        //process_focus 工序名称获取焦点
        process_focus(op, title, url, event) {
            this.process_index = event.$index
            this.process_array = []
            switch (title) {
                case '工序名称':
                    this.process_name = true
                    this.process_code = false
                    this.process_content = false
                    this.$emit('process_type', false)
                    break
                case '工序内容':
                    this.process_content = true
                    this.process_code = false
                    this.process_name = false
                    this.$emit('process_type', false)
                    break
                case '机床代号':
                    this.process_code = true
                    this.process_content = false
                    this.process_name = false
                    this.$emit('process_type', false)
                    break
            }
            this.drawerbul = true
            let page = { keyword: op }
            this.tabulation_title = title
            this.$HTTP.post(url, page).then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    let resData = res.result
                    resData.map((press) => {
                        if (press.process) {
                            const title = JSON.parse(press.process).map((item) => {
                                return item.process_title
                            })
                            press.process_title = title.join('，')
                        }
                    })
                    this.process_array = resData
                }
            })
        },
        // process_blur工序名称失去焦点
        process_blur(op, title, url) {
            let page = { keyword: op }
            this.process_array = []
            this.tabulation_title = title
            this.$HTTP.post(url, page).then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    let resData = res.result
                    resData.map((press) => {
                        if (press.process) {
                            const title = JSON.parse(press.process).map((item) => {
                                return item.process_title
                            })
                            press.process_title = title.join('，')
                        }
                    })
                    this.process_array = resData
                }
            })
        },
        // process_input工序名称发生变化
        process_input(op, title, url, processtitle) {
            let page = {}
            if (title == '机床代号') {
                page = { code: op, keyword: processtitle }
            } else {
                page = { keyword: op }
            }
            this.$HTTP.post(url, page).then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    let resData = res.result
                    resData.map((press) => {
                        if (press.process) {
                            const title = JSON.parse(press.process).map((item) => {
                                return item.process_title
                            })
                            press.process_title = title.join('，')
                        }
                    })
                    this.process_array = resData
                }
            })
        },
        // 复制按钮
        dialogFormclick() {
            let arr = []
            this.form.list.forEach((item) => {
                if (JSON.stringify(item) != '{}') {
                    arr.push(item)
                }
            })
            this.form.list = arr
            this.dialogFormVisible = true
        },
        copyClick() {
            this.dialogFormVisible = false
            localStorage.setItem('copyArray', JSON.stringify(this.multipleSelection))
        },
        handleSelectionChange(val) {
            this.multipleSelection = val
        },
        // 粘贴
        pasteClick() {
            if (localStorage.getItem('copyArray')) {
                this.form.list = [...JSON.parse(localStorage.getItem('copyArray')), ...this.form.list]
                localStorage.removeItem('copyArray')
            } else {
                ElMessage.error('请先复制')
            }
        },
        // 暂存按钮数据暂时存在本地
        StagingForm() {
            var StagingArray = []
            var arr = []
            let arrShow = false
            this.form.list.forEach((item) => {
                if (JSON.stringify(item) != '{}') {
                    arr.push(item)
                }
            })
            this.form.list = arr
            if (localStorage.getItem(`craftStaging_${this.material_numberAll}`)) {
                StagingArray = JSON.parse(localStorage.getItem(`craftStaging_${this.material_numberAll}`))
            }
            let obj
            if (this.form.list.length) {
                obj = {
                    material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`),
                    Array: this.form.list
                }
                if (StagingArray.length) {
                    for (let i = 0; i < StagingArray.length; i++) {
                        if (StagingArray[i].material_number == obj.material_number) {
                            arrShow = true
                            StagingArray[i] = obj
                        }
                    }
                    if (!arrShow) {
                        StagingArray.push(obj)
                    }
                } else {
                    StagingArray.push(obj)
                }
                localStorage.setItem(`craftStaging_${this.material_numberAll}`,JSON.stringify(StagingArray))
                this.isEdit = false
                this.$notify({
                    title: '暂存',
                    message: '操作成功 ',
                    type: 'success',
                    duration: 2000
                })
            } else {
                return
            }
        },
        // input下拉框数据选择
        handleSelect(item, row, key) {
            switch (key) {
                case 'process_title':
                    // 处理 process_title 的逻辑
                    row.process_title = item.title
                    row.process_id = item.id
                    break
                case 'process_content':
                    // 处理 process_content 的逻辑
                    row.process_content = item.content
                    row.process_content_id = item.id
                    row.process_hour_rate = item.hour_rate
                    break
                case 'tool_code':
                    // 处理 tool_code 的逻辑
                    row.tool_code = item.code
                    row.tool_id = item.id
                    row.tool_title = item.title
                    row.tool_hour_rate = item.hour_rate
                    break
            }
        },
        querySearch(queryString, cb) {
            var restaurants = []
            this.$HTTP.post('process_content/get_all').then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    restaurants = res.result
                    var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants
                    // 调用 callback 返回建议列表的数据
                    cb(results)
                }
            })
        },
        createFilter(queryString) {
            return (restaurant) => {
                return restaurant.content.toLowerCase().indexOf(queryString.toLowerCase()) === 0
            }
        },
        querySearch1(queryString, cb) {
            var restaurants = []
            this.$HTTP.post('process/get_all').then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    restaurants = res.result
                    var results = queryString ? restaurants.filter(this.createFilter1(queryString)) : restaurants
                    // 调用 callback 返回建议列表的数据
                    cb(results)
                }
            })
        },
        createFilter1(queryString) {
            return (restaurant) => {
                return restaurant.title.toLowerCase().indexOf(queryString.toLowerCase()) === 0
            }
        },
        querySearch2(queryString, cb) {
            var restaurants = []
            this.$HTTP.post('tool/get_all').then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    restaurants = res.result
                    var results = queryString ? restaurants.filter(this.createFilter2(queryString)) : restaurants
                    // 调用 callback 返回建议列表的数据
                    cb(results)
                }
            })
        },
        createFilter2(queryString) {
            return (restaurant) => {
                return restaurant.code.toLowerCase().indexOf(queryString.toLowerCase()) === 0
            }
        },
        getData(url) {
            this.typeDic = []
            this.loading = true
            this.$HTTP.post(url).then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    this.typeDic = res.result
                    this.loading = false
                }
            })
        },
        selectChange(key, row, scope) {
            switch (key) {
                case 'process_title':
                    // 处理 process_title 的逻辑
                    let process = this.typeDic.find((item) => item.title === row.process_title)
                    row.process_id = process.id
                    break
                case 'process_content':
                    // 处理 process_content 的逻辑
                    let content = this.typeDic.find((item) => item.content === row.process_content)
                    row.process_content_id = content.id
                    row.process_hour_rate = content.hour_rate
                    break
                case 'tool_code':
                    // 处理 tool_code 的逻辑
                    let tool = this.typeDic.find((item) => item.code === row.tool_code)
                    row.tool_id = tool.id
                    row.tool_title = tool.title
                    row.tool_hour_rate = tool.hour_rate
                    break
            }
        },
        inputChange(key, row, e) {
            console.log(key, 'key')
            console.log(row, 'row')
            if (key === 'unit_hour') {
                this.$emit('showDrawer', row)
            }
            this.drawerbul = false
            this.tabulation_title = ''
            this.process_array = []
        },
        submitForm() {
            this.$emit('process_type', false)
            let arr = []
            this.form.list.forEach((item) => {
                if (JSON.stringify(item) != '{}') {
                    arr.push(item)
                }
            })
            this.form.list = arr
            this.$refs.ruleForm.validate((valid) => {
                if (valid) {
                    let obj = Object.assign({
                        material_number: sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`),
                        crafts: this.form.list
                    })
                    // console.log(obj)
                    this.$HTTP.post('craft/post_add', obj).then((res) => {
                        if (res.errcode != 0) {
                            ElMessage.error(res.errmsg)
                        } else {
                            this.isEdit = false
                            this.$store.state.listObj.ver = !this.$store.state.listObj.ver
                            this.$store.state.technology = !this.$store.state.technology
                            this.$notify({
                                title: '提示',
                                message: '操作成功 ',
                                type: 'success',
                                duration: 2000
                            })
                            this.drawerbul = false
                            this.tabulation_title = ''
                            this.process_array = []
                            let Array = []
                            let pastArray = []
                            if (localStorage.getItem(`craftStaging_${this.material_numberAll}`)) {
                                Array = JSON.parse(localStorage.getItem(`craftStaging_${this.material_numberAll}`))
                            }
                            // console.log(Array, 'Array原数据')
                            Array.forEach((item) => {
                                if (item.material_number != sessionStorage.getItem(`parent_material_number_${sessionStorage.getItem('windowId')}`)) {
                                    pastArray.push(item)
                                }
                            })
                            localStorage.setItem(`craftStaging_${this.material_numberAll}`,JSON.stringify(pastArray))
                        }
                    })
                } else {
                    // console.log(valid, 'valid1')
                    // return false
                }
            })
        },
        resetForm() {
            this.$refs.ruleForm.resetFields()
        },
        editClick() {
            this.isEdit = true
            if (this.form.list.length < 10) {
                for (let i = 0; i < 10; i++) {
                    this.form.list.push({})
                    if (this.form.list.length == 1) {
                        return
                    }
                }
            }
        }
    }
}
</script>
<style lang="scss" scoped>
.el-col {
    text-align: right;
    padding: 0 0 10px;
}

// .el-button {
//     padding: 2px 9px;
//     height: 28px;
//     font-size: 14px;
// }
// 弹窗
.header_ta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 10px;
}

.drawerout {
    display: none;
}

.drawercl {
    display: block;
    position: absolute;
    top: 0;
    right: 0;
    width: 320px;
    height: 100%;
    z-index: 9;
    background-color: #ffffff;
}

.titleh3 {
    margin: 20px;
}

.tablecos {
    width: 90%;
    text-align: center;
}

th,
td {
    border: 1px solid black;
}

.el-dropdown-link {
    cursor: pointer;
    color: #409eff;
    margin: 0 10px 10px 0;
}

.el-icon-arrow-down {
    font-size: 12px;
}

.demonstration {
    display: block;
    color: #8492a6;
    font-size: 14px;
    margin-bottom: 20px;
}
</style>
