<template>
    <el-main style="padding: 0 20px">
        <el-descriptions
            :column="1"
            border
        >
            <el-descriptions-item
                v-for="(item, index) in listA"
                :key="index"
                :label="item.lable"
                width="150px"
                >{{ data[item.key] }}</el-descriptions-item
            >
        </el-descriptions>
    </el-main>
</template>


<script>
export default {
    data() {
        return {
            data: {},
            listA: [
                {
                    lable: '机床名称',
                    key: 'title'
                },
                {
                    lable: '机床编码',
                    key: 'code'
                },
                {
                    lable: '工艺名称',
                    key: 'process_title'
                },
                {
                    lable: '文件路径',
                    key: 'url'
                },
                {
                    lable: '创建时间',
                    key: 'created_at'
                }
            ]
        }
    },
    mounted() {},
    methods: {
        //注入数据
        setData(data) {
            console.log(data);
            this.data = data
        }
    }
}
</script>

<style></style>
