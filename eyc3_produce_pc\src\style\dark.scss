@import 'element-plus/theme-chalk/src/dark/css-vars.scss';

html.dark {
	//变量
	--el-text-color-primary: #d0d0d0;
	--el-color-primary-dark-2: var(--el-color-primary-light-2) !important;
	--el-color-primary-light-9: var(--el-color-primary-dark-8) !important;
	--el-color-primary-light-8: var(--el-color-primary-dark-7) !important;
	--el-color-primary-light-7: var(--el-color-primary-dark-6) !important;
	--el-color-primary-light-5: var(--el-color-primary-dark-4) !important;
	--el-color-primary-light-3: var(--el-color-primary-dark-3) !important;

	//背景
	#app {background: var(--el-bg-color);}

	//登录背景
	.login_bg {background: var(--el-bg-color);}

	//框架
	.adminui-header {background: var(--el-bg-color-overlay);border-bottom: 1px solid var(--el-border-color-light);height:59px;}
	.aminui-side-split {background: var(--el-bg-color);}
	.aminui-side-split li {color: var(--el-text-color-primary);}
	.aminui-side {background: var(--el-bg-color-overlay);border-color: var(--el-border-color-light);}
	.adminui-side-top, .adminui-side-bottom {border-color: var(--el-border-color-light);}
	.adminui-side-top h2 {color: var(--el-text-color-primary);}
	.adminui-topbar, .adminui-tags {background: var(--el-bg-color-overlay);border-color: var(--el-border-color-light);}
	.adminui-main {background: var(--el-bg-color);}
	.drawerBG {background: var(--el-bg-color);}
	.adminui-header-menu .el-menu {--el-menu-bg-color:var(--el-bg-color-overlay) !important;--el-menu-hover-bg-color: #171819 !important;}

	//组件
	.el-header, .el-main.nopadding, .el-footer {background: var(--el-bg-color-overlay);border-color: var(--el-border-color-light);}
	.el-main {background: var(--el-bg-color);}
	.el-aside {background: var(--el-bg-color-overlay);border-color: var(--el-border-color-light);}
	.el-table .el-table__body-wrapper {background: var(--el-bg-color);}
	.el-table th.is-sortable:hover {background: #111;}
}
