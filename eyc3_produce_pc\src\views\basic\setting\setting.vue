
<template>
    <yp_form
        :columns="columns"
        :remote="remote"
        :labelWidth="labelWidth"
    ></yp_form>
</template>

<script>
export default {
    name: 'BasicPowerAdd',
    data() {
        return {
            remote: {
                api: 'config/get_info',
                edit: 'config/post_modify',
                // data: {
                //     s: 1
                // },
                state: 'edit'
            },
            labelWidth: '150px',
            columns: [
                {
                    label: 'bom版本审批',
                    name: 'bom_approve',
                    component: 'switch',
                    options: {
                        activevalue: 1,
                        inactivevalue: 0
                    }
                },
                {
                    label: '工艺卡版本审批',
                    name: 'craft_approve',
                    component: 'switch',
                    options: {
                        activevalue: 1,
                        inactivevalue: 0
                    }
                }
            ]
        }
    }
}
</script>

<style>
</style>