<template>
    <div
        class="custom-tree-node-container"
        v-loading="loading"
    >
        <template v-if="isAllCheck">
            <el-checkbox
                v-model="allChecked"
                :indeterminate="isIndeterminate"
                @change="handleCheckAllChange"
                :disabled="disabled"
                >全选</el-checkbox
            >
        </template>
        <el-tree
            :data="treeData"
            show-checkbox
            node-key="id"
            default-expand-all
            ref="tree"
            :expand-on-click-node="true"
            :props="defaultPropsH"
            @current-change="changeSelect"
            @check="changeNode"
            :check-strictly="isCheck"
        >
        </el-tree>
    </div>
</template>
  
<script>
import { v4 as uuidv4 } from 'uuid'
export default {
    name: 'verificate',
    props: {
        value: { type: String, default: '' },
        remote: { type: Object, default: '' },
        defaultProps: {
            type: Object,
            default: () => {
                return {}
            }
        },
        isCross: { type: Boolean, default: true },
        isAllCheck: { type: <PERSON>olean, default: true },
        disabled: { type: Boolean, default: false }
    },
    data() {
        return {
            treeData: [],
            defaultPropsH: {},
            allChecked: false,
            isIndeterminate: false,
            total: 0,
            defaultKeys: [],
            isCheck: false,
            loading: false
        }
    },
    created() {
        this.defaultPropsH = {
            label: this.defaultProps.label,
            children: this.defaultProps.children,
            class: (node) => {
                if (node.ispenultimate) return 'ispenultimate'
                if (!node.ispenultimate) return ''
            }
        }
    },
    mounted() {
        // console.log('数据', this.value, this.remote, this.defaultProps)
        this.getData()
    },
    methods: {
        //处理远程选项数据
        getData() {
            this.loading = true
            this.$HTTP.get(this.remote.api, this.remote.postData).then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    this.treeData = res.result
                    this.treeData.forEach((el) => {
                        el.acts = JSON.parse(el.acts)
                        el.ispenultimate = true
                        this.total = this.total + 1

                        el.acts.forEach((item) => {
                            this.total = this.total + 1
                            item.id = uuidv4()
                        })
                    })
                    if (this.value) {
                        let selectId = []
                        let getValue = JSON.parse(this.value).forEach((el) => {
                            this.treeData.forEach((item) => {
                                if (el.id == item.id) {
                                    if (el.acts.length == item.acts.length) {
                                        selectId.push(el.id)
                                    } else {
                                        el.acts.forEach((k) => {
                                            item.acts.forEach((j) => {
                                                if (k.title == j.title) {
                                                    selectId.push(j.id)
                                                }
                                            })
                                        })
                                    }
                                }
                            })
                        })
                        this.$refs.tree.setCheckedKeys(selectId)
                        setTimeout(() => {
                            // 定时器代码
                            this.getNode()
                            if (this.disabled) {
                                // this.isCheck = true
                                this.treeData.forEach((el) => {
                                    el.disabled = true
                                    el.acts.forEach((item) => {
                                        item.disabled = true
                                    })
                                })
                                console.log(this.treeData)
                                // this.isCheck = false
                            }

                            setTimeout(() => {
                                this.loading = false
                            }, 500)
                        }, 80)
                    } else {
                        this.loading = false
                    }
                }
                // this.loading = false
            })
        },
        // 点击全选
        handleCheckAllChange(val) {
            this.isIndeterminate = false
            if (val) {
                this.$refs.tree.setCheckedNodes(this.treeData)
            } else {
                // console.log('全选', val, this.$refs.tree)
                this.$refs.tree.setCheckedNodes([])
            }
        },
        // 当前选中节点(底层节点)变化时触发的事件
        changeSelect(val) {
            // console.log('节点发生变化', val)
            // console.log(this.$refs.tree.getCheckedKeys(), this.total)
            this.getNode()
            this.assignVal()
        },
        // 节点被点击时的回调
        changeNode() {
            // console.log('一级节点发生变化')
            // console.log(this.$refs.tree.getCheckedKeys(), this.total)
            this.getNode()
            this.assignVal()
        },
        // 获取节点
        getNode() {
            console.log('修改全选')
            if (this.$refs.tree.getCheckedKeys().length == this.total) {
                this.isIndeterminate = false
                this.allChecked = true
            } else if (this.$refs.tree.getCheckedKeys().length == 0) {
                this.isIndeterminate = false
                this.allChecked = false
            } else {
                this.isIndeterminate = true
                this.allChecked = false
            }
        },
        // 赋值
        assignVal() {
            // console.log(this.$refs.tree.getCheckedKeys())
            let checkedKeys = this.$refs.tree.getCheckedKeys()
            let checkPermission = []
            this.treeData.forEach((el) => {
                if (checkedKeys.includes(el.id)) {
                    checkPermission.push({
                        id: el.id,
                        acts: el.acts,
                        title: el.title,
                        className: el.className
                    })
                } else {
                    let acts = []
                    el.acts.forEach((item) => {
                        if (checkedKeys.includes(item.id)) {
                            acts.push(item)
                        }
                    })
                    if (acts.length != 0) {
                        checkPermission.push({
                            id: el.id,
                            acts: acts,
                            title: el.title,
                            className: el.className
                        })
                    }
                }
            })
            // console.log('输出结果', checkPermission)
            this.$emit('update:value', JSON.stringify(checkPermission))
        }
    }
}
</script>
  
  <style scoped>
.custom-tree-node-container {
    width: 100%;
    border: 1px solid #dcdfe6;
    padding: 8px;
    border-radius: 4px;
}



::v-deep .el-tree-node.is-expanded.ispenultimate > .el-tree-node__children {
    display: flex !important;
    flex-direction: row;
    flex-wrap: wrap;
}
::v-deep .ispenultimate > .el-tree-node__children > div {
    width: 10%;
}
::v-deep .ispenultimate > .el-tree-node__children > div:hover {
    /* color: blue; */
    background: transparent;
}

::v-deep .el-tree > .ispenultimate {
    --el-tree-node-hover-bg-color: none;
}
</style>
