<!--
 * @author: 风源
 * @name: 类名
 * @desc: 介绍
 * @LastEditTime: 2023-01-05 15:36:41
 * @FilePath: \eyc3_canyin_pc\src\views\report\dininghall\total.vue
-->
<template>
    <yp_list
        ref="table"
        :url="url"
        row-key="id"
        :columns="columns"
        stripe
        :add="add"
        :derive="derive"
        :formitems="formitems"
        :paging="false"
        :showsummary="true"
    >
    </yp_list>
</template>

<script>
export default {
    name: 'ReportDininghallTotal',
    data() {
        return {
            url: 'report/get_bill_sum',
            columns: [
                {
                    label: '餐厅名称',
                    prop: 'title'
                },
                {
                    label: '消费次数',
                    children: [
                        {
                            label: '内部消费次数',
                            prop: 'count'
                        },
                        {
                            label: '访客消费次数',
                            prop: 'visitor_count'
                        }
                    ]
                },
                {
                    label: '消费金额',
                    children: [
                        {
                            label: '内部消费',
                            prop: 'money'
                        },
                        {
                            label: '访客消费',
                            prop: 'visitor_money'
                        }
                    ]
                },

                {
                    label: '虚拟金额',
                    children: [
                        {
                            label: '内部虚拟金额',
                            prop: 'virtual_money'
                        },
                        {
                            label: '访客虚拟金额',
                            prop: 'visitor_virtual_money'
                        }
                    ]
                },
                {
                    label: '真实金额',
                    children: [
                        {
                            label: '内部真实金额',
                            prop: 'real_money'
                        },
                        {
                            label: '访客真实金额',
                            prop: 'visitor_real_money'
                        }
                    ]
                },
                {
                    label: '餐补金额',
                    children: [
                        {
                            label: '内部餐补金额',
                            prop: 'subsidy_money'
                        },
                        {
                            label: '访客餐补金额',
                            prop: 'visitor_subsidy_money'
                        }
                    ]
                },
                {
                    label: '优惠券金额',
                    children: [
                        {
                            label: '内部优惠券金额',
                            prop: 'coupon_money'
                        },
                        {
                            label: '访客优惠券金额',
                            prop: 'visitor_coupon_money'
                        }
                    ]
                }
            ],
            derive: {
                url: 'report/get_bill_sum',
                filename:"餐厅汇总"
            },
            formitems: [
                {
                    label: '日期范围',
                    name: 'date_interval',
                    value: [
                        this.$TOOL.dateFormat(new Date(), 'yyyy-MM-01'),
                        this.$TOOL.dateFormat(new Date(), 'yyyy-MM-dd')
                    ],
                    component: 'date',
                    options: {
                        type: 'daterange',
                        rangeseparator: '至',
                        startplaceholder: '开始日期',
                        endplaceholder: '结束日期',
                        valueFormat: 'YYYY-MM-DD'
                    },
                    rules: [
                        {
                            required: true,
                            message: 'Please input Data',
                            trigger: 'change'
                        }
                    ]
                },
                {
                    label: '离职人员',
                    name: 'isleave',
                    value: false,
                    component: 'switch'
                }
            ]
        }
    }
}
</script>

<style></style>
