<template>
	<div style="min-width: 1450px">
		<el-main v-loading="Loading">
			<el-row :gutter="24">
				<!-- <el-col :span="12">
				<el-card shadow="hover">
					<el-header><h2>基础信息</h2></el-header>
					<el-main class="describe">
						<div>
							<div class="destitle">BOM表单</div>
							<div class="desinformation">20</div>
						</div>
						<div>
							<div class="destitle">工艺卡</div>
							<div class="desinformation">20</div>
						</div>
						<div>
							<div class="destitle">图纸</div>
							<div class="desinformation">20</div>
						</div>
					</el-main>
				</el-card>
			</el-col> -->
			<div @click="nevto">111</div>
				<el-col :span="24">
					<el-card shadow="hover">
						<el-header>
							<h2 style="color:red">异常信息</h2>
						</el-header>
						<el-main class="describe">
							<div @click.stop="NobindingNum('原材料')">
								<div class="destitle">没有绑定原材料数量</div>
								<div class="desinf">{{ authority ? '暂无权限' : objdata.without_raw ? objdata.without_raw : "无" }}
								</div>
							</div>
							<div @click.stop="NobindingNum('工艺卡')">
								<div class="destitle">没有工艺卡数量</div>
								<div class="desinf">{{ authority ? '暂无权限' : objdata.without_craft ? objdata.without_craft : "无"
									}}</div>
							</div>
							<div @click.stop="NobindingNum('图纸')">
								<div class="destitle">没有图纸数量</div>
								<div class="desinf">{{
									authority ? '暂无权限' : objdata.without_drawing ? objdata.without_drawing :"无" }}</div>
							</div>
							<div @click.stop="NobindingNum('BOM未发布版本')">
								<div class="destitle">BOM未发布版本</div>
								<div class="desinf">{{ authority ? '暂无权限' : objdata.bom_unpublish ? objdata.bom_unpublish : "无"
									}}</div>
							</div>
							<div @click.stop="NobindingNum('工艺卡未发布版本')">
								<div class="destitle">工艺卡未发布版本</div>
								<div class="desinf">{{
									authority ? '暂无权限' : objdata.craft_unpublish ? objdata.craft_unpublish :"无" }}</div>
							</div>
							<div @click.stop="NobindingNum('未上传下料图纸')">
								<div class="destitle">未上传下料图纸</div>
								<div class="desinf">{{
									authority ? '暂无权限' : objdata.txl_drawing  ? objdata.txl_drawing  :"无" }}</div>
							</div>
						</el-main>
					</el-card>
				</el-col>
			</el-row>
		</el-main>
		<el-main v-loading="Loading_log">
			<el-table :data="tableData" stripe style="width: 100%,height:100%;">
				<template #empty>
					<el-empty :description="description" :image-size="200"></el-empty>
				</template>
				<el-table-column type="index" label="序号" align="center" width="50">
				</el-table-column>
				<el-table-column prop="user_name" label="姓名" align="center" min-width="180">
				</el-table-column>
				<el-table-column prop="created_at" align="center" min-width="180" label="登录时间">
				</el-table-column>
				<el-table-column prop="ip" align="center" min-width="180" label="IP">
				</el-table-column>
			</el-table>
		</el-main>
	</div>
</template>

<script>
import NumberCount from './NumberCount.vue'
export default {
	// name: 'index',
	components: {
		NumberCount
	},
	data() {
		return {
			Loading: false,
			Loading_log: false,
			objdata: {},
			tableData: [],
			description: '',
			authority: null,

		}
	},
	created() {
	},
	mounted() {
		if (JSON.stringify(this.$route.params) != "{}") {
			console.log(this.$route.params.material_number);
			this.post_data({ material_number: this.$route.params.material_number })
		} else {
			this.post_data()
		}
		this.post_data_login()
	},
	methods: {
		NobindingNum(op) {
			switch (op) {
				// 没有原材料表格
				case '原材料':
					this.$router.push({ name: 'indextable', params: { title: '没有绑定原材料', url: 'datav/get_nobom_ls' }, op: this.$route.params.material_number })
					break;
				// 没有工艺卡
				case '工艺卡':
					this.$router.push({ name: 'indextableCard', params: { title: '没有工艺卡', url: 'datav/get_nocraft_ls', op: this.$route.params.material_number } })
					break;
				// 没有图纸
				case '图纸':
					this.$router.push({ name: 'indextableDraw', params: { title: '没有图纸', url: 'datav/get_nodrawing_ls', op: this.$route.params.material_number } })
					break;
				//bom未发布版本
				case "BOM未发布版本":
					this.$router.push({ name: "bomUnreleasedVersion"})
					break;
				case "工艺卡未发布版本":
					this.$router.push({ name: "processCardUnreleasedVersion" })
					break;
				case "未上传下料图纸":
					this.$router.push({ name: "cuttingStockDrawing" })
					break;
			}
		},
		post_data(op) {
			this.Loading = true
			this.$HTTP
				.post('datav/get_total', op).then(res => {
					if (res.errcode != 0) {
						this.authority = true
						this.Loading = false
					} else {
						this.objdata = res.result
						console.log(this.objdata, 9999);
						this.Loading = false
					}
				})
		},
		post_data_login() {
			this.Loading_log = true
			this.$HTTP
				.post('datav/get_login_log').then(res => {
					if (res.errcode != 0) {
						this.description = '暂无权限'
						this.Loading_log = false
					} else {
						this.tableData = res.result.data
						this.description = '暂无数据'
						this.Loading_log = false
					}
				})
		},
		nevto() {
			this.$router.push({ name: 'OAApprovalDetails' })
		}
	}
}
</script>

<style scoped lang="scss">
.describe {
	display: flex;
	justify-content: space-around;
	text-align: center;
}

.destitle {
	font-size: 16px;
	margin-bottom: 10px;
}

.desinformation,
.desinf {
	font-size: 25px;
}

.desinf {
	font-size: 25px;
	color: red;
	cursor: pointer;
}
</style>
